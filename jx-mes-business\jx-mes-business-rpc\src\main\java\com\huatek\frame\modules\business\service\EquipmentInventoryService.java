package com.huatek.frame.modules.business.service;

import com.huatek.frame.modules.business.domain.EquipmentInventory;
import com.huatek.frame.modules.business.domain.vo.EquipmentInventorySampleVO;
import com.huatek.frame.modules.business.service.dto.EquipmentInventoryDTO;
import com.huatek.frame.modules.business.domain.vo.EquipmentInventoryVO;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.business.service.dto.EquipmentInventoryUpdateStatusDTO;

import java.util.List;


/**
* @description 设备台账Service
* <AUTHOR>
* @date 2025-07-18
**/
public interface EquipmentInventoryService {
    
    /**
	 * 分页查找查找 设备台账
	 * 
	 * @param dto 设备台账dto实体对象
	 * @return 
	 */
	TorchResponse<List<EquipmentInventoryVO>> findEquipmentInventoryPage(EquipmentInventoryDTO dto);

    /**
	 * 添加 \修改 设备台账
	 * 
	 * @param equipmentInventoryDto 设备台账dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(EquipmentInventoryDTO equipmentInventoryDto);
	
	/**
	 * 通过id查找设备台账
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<EquipmentInventoryVO> findEquipmentInventory(String id);
	
	/**
	 * 删除 设备台账
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 查找关联信息 设备台账
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<EquipmentInventoryVO>> getOptionsList(String id);




    /**
     * 根据条件查询设备台账列表
     *
     * @param dto 设备台账信息
     * @return 设备台账集合信息
     */
    List<EquipmentInventoryVO> selectEquipmentInventoryList(EquipmentInventoryDTO dto);

    /**
     * 导入设备台账数据
     *
     * @param equipmentInventoryList 设备台账数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    TorchResponse importEquipmentInventory(List<EquipmentInventoryVO> equipmentInventoryList, List<String> unionColumns, Boolean isUpdateSupport, String operName);

	/**
	 * 根据IDS获取设备台账数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectEquipmentInventoryListByIds(List<String> ids);

    /**
     * 设备台账主子表单组合提交
     *
	 * @param equipmentInventoryDto 设备台账DTO实体对象
     * @return
     */
    TorchResponse submitMasterDetails(EquipmentInventoryDTO equipmentInventoryDto);

	/**
	 * 查询设备最新的溯源记录
	 * @param deviceSerialNumber 设备编号
	 * @return
	 */
	TorchResponse getLatestTraceInfo(String deviceSerialNumber);

	/**
	 * 查询相同设备类型的设备信息列表
	 * @param requestParam 设备类型
	 * @return
	 */
	TorchResponse findDeviceListByType(String requestParam);

	/**
	 * 批量修改设备状态
	 * @param requestParam
	 * @return
	 */
    TorchResponse updateStatus(EquipmentInventoryUpdateStatusDTO requestParam);

	/**
	 * 设备及时校准率接口开发
	 * @param id 设备台账id
	 * @return
	 */
    TorchResponse calibrationRate(String id);
	/**
	 * 查询所有设备台账
	 * @return
	 */
	TorchResponse<List<EquipmentInventorySampleVO>> getAllEquipmentInventory();
}