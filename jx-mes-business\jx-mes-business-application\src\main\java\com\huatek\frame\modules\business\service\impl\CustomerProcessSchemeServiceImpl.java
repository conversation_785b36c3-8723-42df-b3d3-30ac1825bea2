package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.domain.*;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.business.domain.vo.CustomerExperimentProjectDataVO;
import com.huatek.frame.modules.business.domain.vo.CustomerExperimentProjectVO;
import com.huatek.frame.modules.business.service.dto.CustomerExperimentProjectDTO;
import com.huatek.frame.modules.business.service.dto.CustomerExperimentProjectDataDTO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.business.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.vo.CustomerProcessSchemeVO;
import com.huatek.frame.modules.business.service.CustomerProcessSchemeService;
import com.huatek.frame.modules.business.service.dto.CustomerProcessSchemeDTO;
import java.sql.Date;
import java.util.stream.Collectors;

import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * 客户工序方案 ServiceImpl
 * 
 * <AUTHOR>
 * @date 2025-07-17
 */
@Service
@DubboService
// @CacheConfig(cacheNames = "customerProcessScheme")
// @RefreshScope
@Slf4j
public class CustomerProcessSchemeServiceImpl implements CustomerProcessSchemeService {

	public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

	@Autowired
	private SecurityUser securityUser;

	@Autowired
	private CustomerProcessSchemeMapper customerProcessSchemeMapper;

	@Autowired
	private CustomerExperimentProjectMapper customerExperimentProjectMapper;
	@Autowired
	private CustomerExperimentProjectDataMapper customerExperimentProjectDataMapper;

	@Autowired
	private ExperimentProjectMapper experimentProjectMapper;
	@Autowired
	private ExperimentProjectDataMapper experimentProjectDataMapper;
	@Autowired
	private StandardProcessPlanMapper standardProcessPlanMapper;

	@Autowired
	private AwaitingProductionOrderMapper awaitingProductionOrderMapper;
	@Autowired
	private StandardSpecificationMapper standardSpecificationMapper;
	@Autowired
	private ProductManagementMapper productManagementMapper;
	@Autowired
	private CustomerInformationManagementMapper customerInformationManagementMapper;
	@Autowired
	protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();

	public CustomerProcessSchemeServiceImpl() {

	}

	@Override
	// @Cacheable(keyGenerator = "keyGenerator")
	@DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<CustomerProcessSchemeVO>> findCustomerProcessSchemePage(CustomerProcessSchemeDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<CustomerProcessSchemeVO> customerProcessSchemes = customerProcessSchemeMapper
				.selectCustomerProcessSchemePage(dto);
		TorchResponse<List<CustomerProcessSchemeVO>> response = new TorchResponse<List<CustomerProcessSchemeVO>>();
		response.getData().setData(customerProcessSchemes);
		response.setStatus(200);
		response.getData().setCount(customerProcessSchemes.getTotal());
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	// @CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(CustomerProcessSchemeDTO customerProcessSchemeDto) {
		String currentUser = SecurityContextHolder.getCurrentUserName();
		if (HuatekTools.isEmpty(customerProcessSchemeDto.getCodexTorchDeleted())) {
			customerProcessSchemeDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
		}
		String id = customerProcessSchemeDto.getId();
		CustomerProcessScheme entity = new CustomerProcessScheme();
		BeanUtils.copyProperties(customerProcessSchemeDto, entity);
		entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
		entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
		entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
			customerProcessSchemeMapper.insert(entity);
		} else {
			customerProcessSchemeMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
		CustomerProcessSchemeVO vo = new CustomerProcessSchemeVO();
		BeanUtils.copyProperties(entity, vo);
		response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	// @Cacheable(key = "#p0")
	public TorchResponse<CustomerProcessSchemeVO> findCustomerProcessScheme(String id) {
		CustomerProcessSchemeVO vo = new CustomerProcessSchemeVO();
		if (!HuatekTools.isEmpty(id)) {
			CustomerProcessScheme entity = customerProcessSchemeMapper.selectById(id);
			if (HuatekTools.isEmpty(entity)) {
				throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
			//查询客户试验项目
			CustomerExperimentProjectDTO projectDTO = new CustomerExperimentProjectDTO();
			projectDTO.setCodexTorchMasterFormId(entity.getId());
			List<CustomerExperimentProjectVO> experimentProjectVOS = customerExperimentProjectMapper.selectCustomerExperimentProjectList(projectDTO);
			for (CustomerExperimentProjectVO project :experimentProjectVOS ){
				CustomerExperimentProjectDataDTO projectDataDTO =  new CustomerExperimentProjectDataDTO();
				projectDataDTO.setCodexTorchMasterFormId(project.getId());
				List<CustomerExperimentProjectDataVO> ProjectDatas = customerExperimentProjectDataMapper.selectCustomerExperimentProjectDataList(projectDataDTO);
				project.setCustomerProjectDataItems(ProjectDatas);
			}
			vo.setCustomerExperimentProjectItems(experimentProjectVOS);
			ProductionOrder productionOrder = awaitingProductionOrderMapper.selectById(entity.getWorkOrder());
			vo.setWorkOrder(productionOrder.getWorkOrderNumber());
			StandardSpecification standardSpecification = standardSpecificationMapper.selectById(entity.getStandardSpecificationNumber());
			vo.setStandardSpecificationNumber(standardSpecification.getSpecificationNumber());
			vo.setStandardSpecificationName(standardSpecification.getSpecificationName());
			CustomerInformationManagement customerInformationManagement =customerInformationManagementMapper.selectById(entity.getEntrustedUnit());
			if(!ObjectUtils.isEmpty(customerInformationManagement))
			vo.setEntrustedUnit(customerInformationManagement.getEntrustedUnit());
			ProductManagement productManagement = productManagementMapper.selectById(entity.getProductModel());
			if(null!=productManagement){
				vo.setProductModel(productManagement.getProductModel());
				vo.setProductName(productManagement.getProductName());
				vo.setProductCategory(productManagement.getProductCategory());
				vo.setManufacturer(productManagement.getManufacturer());
			}
		}
		TorchResponse<CustomerProcessSchemeVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	// @CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
		customerProcessSchemeMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id) {
		if (selectOptionsFuncMap.size() == 0) {
		}

		// 默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
		Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
		if (!HuatekTools.isEmpty(pageFunction)) {
			selectOptionsVOs = pageFunction.apply(id);
		}

		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
		response.getData().setData(selectOptionsVOs);
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setCount(selectOptionsVOs.getTotal());
		return response;
	}

	@Override
	@ExcelExportConversion(tableName = "customer_process_scheme", convertorFields = "")
	@DataScope(groupAlias = "t", userAlias = "t")
	public List<CustomerProcessSchemeVO> selectCustomerProcessSchemeList(CustomerProcessSchemeDTO dto) {
		return customerProcessSchemeMapper.selectCustomerProcessSchemeList(dto);
	}

	@Override
	public TorchResponse selectCustomerProcessSchemeListByIds(List<String> ids) {
		List<CustomerProcessSchemeVO> customerProcessSchemeList = customerProcessSchemeMapper
				.selectCustomerProcessSchemeListByIds(ids);

		TorchResponse<List<CustomerProcessSchemeVO>> response = new TorchResponse<List<CustomerProcessSchemeVO>>();
		response.getData().setData(customerProcessSchemeList);
		response.setStatus(200);
		response.getData().setCount((long) customerProcessSchemeList.size());
		return response;
	}

	@Override
	public TorchResponse setStandardProcessPlan(CustomerProcessSchemeDTO customerProcessSchemeDto) {

		CustomerProcessScheme processScheme = customerProcessSchemeMapper.selectById(customerProcessSchemeDto.getId());
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("CODEX_TORCH_MASTER_FORM_ID", customerProcessSchemeDto.getId());
		List<CustomerExperimentProject> customerExperimentProjects = customerExperimentProjectMapper.selectList(wrapper);
		List<String> experimentProjectids = customerExperimentProjects.stream().map(CustomerExperimentProject::getId)
				.collect(Collectors.toList());
		wrapper.clear();
		wrapper.in("CODEX_TORCH_MASTER_FORM_ID", experimentProjectids);
		List<CustomerExperimentProjectData> customerExperimentProjectDatas = customerExperimentProjectDataMapper
				.selectList(wrapper);

		StandardProcessPlan entity = new StandardProcessPlan();
		BeanUtils.copyProperties(processScheme, entity);
		entity.setProcessSchemeName(customerProcessSchemeDto.getProcessSchemeName());
		entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
		entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
		entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		entity.setId(null);
		standardProcessPlanMapper.insert(entity);
		for (CustomerExperimentProject experimentProject : customerExperimentProjects) {
			ExperimentProject experimentEntity = new ExperimentProject();
			BeanUtils.copyProperties(experimentProject, experimentEntity);
			experimentEntity.setId(null);
			experimentEntity.setCodexTorchMasterFormId(entity.getId());
			experimentEntity.setProcessSchemeName(customerProcessSchemeDto.getProcessSchemeName());
			experimentEntity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
			experimentEntity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
			experimentEntity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
			experimentProjectMapper.insert(experimentEntity);
//			List<CustomerExperimentProjectData> data = customerExperimentProjectDatas.stream()
//					.filter(x -> x.getCodexTorchMasterFormId() == entity.getId())
//					.collect(Collectors.toList());
			for (CustomerExperimentProjectData projectdata : customerExperimentProjectDatas) {
				ExperimentProjectData experimentDataEntity = new ExperimentProjectData();
				BeanUtils.copyProperties(projectdata, experimentDataEntity);
				experimentDataEntity.setId(null);
				experimentDataEntity.setCodexTorchMasterFormId(experimentEntity.getId());
				experimentDataEntity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
				experimentDataEntity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
				experimentProjectDataMapper.insert(experimentDataEntity);
			}
		}
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

}
