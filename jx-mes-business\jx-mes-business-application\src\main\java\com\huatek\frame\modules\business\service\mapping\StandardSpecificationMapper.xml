<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.StandardSpecificationMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.specification_type as specificationType,
		t.specification_number as specificationNumber,
		t.specification_name as specificationName,
		t.entrusted_unit as entrustedUnit,
		t.status as status,
		t.attachment as attachment,
        t.controlled_number as controlledNumber,
		t.CODEX_TORCH_CREATOR_ID as codexTorchCreatorId,
		t.CODEX_TORCH_UPDATER as codexTorchUpdater,
		t.CODEX_TORCH_GROUP_ID as codexTorchGroupId,
		t.CODEX_TORCH_CREATE_DATETIME as codexTorchCreateDatetime,
		t.CODEX_TORCH_UPDATE_DATETIME as codexTorchUpdateDatetime,
		t.CODEX_TORCH_DELETED as codexTorchDeleted
	</sql>
	<select id="selectStandardSpecificationPage" parameterType="com.huatek.frame.modules.business.service.dto.StandardSpecificationDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.StandardSpecificationVO">
		select
		<include refid="Base_Column_List" />
			from standard_specification t
            <where>
                and 1=1
                <if test="specificationType != null and specificationType != ''">
                    and t.specification_type  = #{specificationType}
                </if>
                <if test="specificationNumber != null and specificationNumber != ''">
                    and t.specification_number  like concat('%', #{specificationNumber} ,'%')
                </if>
                <if test="specificationName != null and specificationName != ''">
                    and t.specification_name  like concat('%', #{specificationName} ,'%')
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and t.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
                </if>
                <if test="status != null and status != ''">
                    and t.status  = #{status}
                </if>
                <if test="attachment != null and attachment != ''">
                    and t.attachment  = #{attachment}
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.CODEX_TORCH_CREATOR_ID  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.CODEX_TORCH_UPDATER  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.CODEX_TORCH_GROUP_ID  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.CODEX_TORCH_CREATE_DATETIME  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.CODEX_TORCH_UPDATE_DATETIME  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.CODEX_TORCH_DELETED  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectStandardSpecificationList" parameterType="com.huatek.frame.modules.business.service.dto.StandardSpecificationDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.StandardSpecificationVO">
		select
		<include refid="Base_Column_List" />
			from standard_specification t
            <where>
                and 1=1
                <if test="specificationType != null and specificationType != ''">
                    and t.specification_type  = #{specificationType}
                </if>
                <if test="specificationNumber != null and specificationNumber != ''">
                    and t.specification_number  like concat('%', #{specificationNumber} ,'%')
                </if>
                <if test="specificationName != null and specificationName != ''">
                    and t.specification_name  like concat('%', #{specificationName} ,'%')
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and t.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
                </if>
                <if test="status != null and status != ''">
                    and t.status  = #{status}
                </if>
                <if test="attachment != null and attachment != ''">
                    and t.attachment  = #{attachment}
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.CODEX_TORCH_CREATOR_ID  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.CODEX_TORCH_UPDATER  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.CODEX_TORCH_GROUP_ID  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.CODEX_TORCH_CREATE_DATETIME  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.CODEX_TORCH_UPDATE_DATETIME  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.CODEX_TORCH_DELETED  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectStandardSpecificationListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.StandardSpecificationVO">
		select
		<include refid="Base_Column_List" />
			from standard_specification t
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>

    <select id="selectOptionsByEntrustedUnit" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.entrusted_unit label,
        t.entrusted_unit value
        from customer_information_management t
        WHERE t.entrusted_unit != ''
    </select>
</mapper>