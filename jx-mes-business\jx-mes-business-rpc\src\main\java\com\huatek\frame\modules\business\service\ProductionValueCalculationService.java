package com.huatek.frame.modules.business.service;

import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.business.domain.vo.ProductionValueCalculationDuiZhangVO;
import com.huatek.frame.modules.business.domain.vo.ProductionValueCalculationVO;
import com.huatek.frame.modules.business.service.dto.ProductionValueCalculationContractDTO;
import com.huatek.frame.modules.business.service.dto.ProductionValueCalculationDTO;
import com.huatek.frame.modules.business.service.dto.ProductionValueCalculationDuiZhangDTO;

import java.util.List;


/**
* @description 产值计算Service
* <AUTHOR>
* @date 2025-08-22
**/
public interface ProductionValueCalculationService {
    
    /**
	 * 分页查找查找 产值计算
	 * 
	 * @param dto 产值计算dto实体对象
	 * @return 
	 */
	TorchResponse<List<ProductionValueCalculationVO>> findProductionValueCalculationPage(ProductionValueCalculationDTO dto);

    /**
	 * 添加 \修改 产值计算
	 * 
	 * @param productionValueCalculationDto 产值计算dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(ProductionValueCalculationDTO productionValueCalculationDto);
	
	/**
	 * 通过id查找产值计算
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<ProductionValueCalculationVO> findProductionValueCalculation(String id);
	
	/**
	 * 删除 产值计算
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 查找关联信息 产值计算
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<ProductionValueCalculationVO>> getOptionsList(String id);




    /**
     * 根据条件查询产值计算列表
     *
     * @param dto 产值计算信息
     * @return 产值计算集合信息
     */
    List<ProductionValueCalculationVO> selectProductionValueCalculationList(ProductionValueCalculationDTO dto);

    /**
     * 导入产值计算数据
     *
     * @param productionValueCalculationList 产值计算数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    TorchResponse importProductionValueCalculation(List<ProductionValueCalculationDuiZhangVO> productionValueCalculationList, List<String> unionColumns, Boolean isUpdateSupport, String operName);

	/**
	 * 根据IDS获取产值计算数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectProductionValueCalculationListByIds(List<String> ids);
	
	/**
	 * 对账
	 * 参数ProductionValueCalculationDuiZhangDTO
	 * @param productionValueCalculationDuiZhangDTO
	 * @return
	 *  */
	TorchResponse duiZhang(ProductionValueCalculationDuiZhangDTO productionValueCalculationDuiZhangDTO);

    /**
     * 更新合同编号
     * @param productionValueCalculationContractDTO
     * @return
     */
    TorchResponse updateContract(ProductionValueCalculationContractDTO productionValueCalculationContractDTO);
	/**
	 * 生成产值计算
	 * @return
	 * @throws Exception
	 */
	TorchResponse generateCalculation() throws Exception;
}