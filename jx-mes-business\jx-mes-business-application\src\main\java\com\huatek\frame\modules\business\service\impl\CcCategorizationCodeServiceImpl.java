package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.domain.CcCategorizationCode;
import com.huatek.frame.modules.business.domain.vo.CcCategorizationCodeVO;
import com.huatek.frame.modules.business.mapper.CcCategorizationCodeMapper;
import com.huatek.frame.modules.business.service.CcCategorizationCodeService;
import com.huatek.frame.modules.business.service.dto.CcCategorizationCodeDTO;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysProcessRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import java.sql.Date;
import org.springframework.util.CollectionUtils;



/**
 * 客户分类对应关系 ServiceImpl
 * <AUTHOR>
 * @date 2025-08-22
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "ccCategorizationCode")
//@RefreshScope
@Slf4j
public class CcCategorizationCodeServiceImpl implements CcCategorizationCodeService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private CcCategorizationCodeMapper ccCategorizationCodeMapper;


    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	public CcCategorizationCodeServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<CcCategorizationCodeVO>> findCcCategorizationCodePage(CcCategorizationCodeDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<CcCategorizationCodeVO> ccCategorizationCodes = ccCategorizationCodeMapper.selectCcCategorizationCodePage(dto);
		TorchResponse<List<CcCategorizationCodeVO>> response = new TorchResponse<List<CcCategorizationCodeVO>>();
		response.getData().setData(ccCategorizationCodes);
		response.setStatus(200);
		response.getData().setCount(ccCategorizationCodes.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(CcCategorizationCodeDTO ccCategorizationCodeDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(ccCategorizationCodeDto.getCodexTorchDeleted())) {
            ccCategorizationCodeDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = ccCategorizationCodeDto.getId();
		CcCategorizationCode entity = new CcCategorizationCode();
        BeanUtils.copyProperties(ccCategorizationCodeDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
			ccCategorizationCodeMapper.insert(entity);
		} else {
			ccCategorizationCodeMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        CcCategorizationCodeVO vo = new CcCategorizationCodeVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<CcCategorizationCodeVO> findCcCategorizationCode(String id) {
		CcCategorizationCodeVO vo = new CcCategorizationCodeVO();
		if (!HuatekTools.isEmpty(id)) {
			CcCategorizationCode entity = ccCategorizationCodeMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<CcCategorizationCodeVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
	    List<CcCategorizationCode> ccCategorizationCodeList = ccCategorizationCodeMapper.selectBatchIds(Arrays.asList(ids));
        for (CcCategorizationCode ccCategorizationCode : ccCategorizationCodeList) {
            ccCategorizationCode.setCodexTorchDeleted(Constant.DEFAULT_YES);
            ccCategorizationCodeMapper.updateById(ccCategorizationCode);
        }
		//ccCategorizationCodeMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "cc_categorization_code", convertorFields = "")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<CcCategorizationCodeVO> selectCcCategorizationCodeList(CcCategorizationCodeDTO dto) {
        return ccCategorizationCodeMapper.selectCcCategorizationCodeList(dto);
    }

    /**
     * 导入客户分类对应关系数据
     *
     * @param ccCategorizationCodeList 客户分类对应关系数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "cc_categorization_code", convertorFields = "")
    public TorchResponse importCcCategorizationCode(List<CcCategorizationCodeVO> ccCategorizationCodeList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(ccCategorizationCodeList) || ccCategorizationCodeList.size() == 0) {
            throw new ServiceException("导入客户分类对应关系数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (CcCategorizationCodeVO vo : ccCategorizationCodeList) {
            try {
                CcCategorizationCode ccCategorizationCode = new CcCategorizationCode();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, ccCategorizationCode);
                QueryWrapper<CcCategorizationCode> wrapper = new QueryWrapper();
                CcCategorizationCode oldCcCategorizationCode = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = CcCategorizationCodeVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<CcCategorizationCode> oldCcCategorizationCodeList = ccCategorizationCodeMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldCcCategorizationCodeList) && oldCcCategorizationCodeList.size() > 1) {
                        ccCategorizationCodeMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldCcCategorizationCodeList) && oldCcCategorizationCodeList.size() == 1) {
                        oldCcCategorizationCode = oldCcCategorizationCodeList.get(0);
                    }
                }
                if (StringUtils.isNull(oldCcCategorizationCode)) {
                    BeanValidators.validateWithException(validator, vo);
                    ccCategorizationCodeMapper.insert(ccCategorizationCode);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、客户器件分类 " + vo.getCustomerDeviceClassification() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldCcCategorizationCode, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    ccCategorizationCodeMapper.updateById(oldCcCategorizationCode);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、客户器件分类 " + vo.getCustomerDeviceClassification() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、客户器件分类 " + vo.getCustomerDeviceClassification() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、客户器件分类 " + vo.getCustomerDeviceClassification() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(CcCategorizationCodeVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectCcCategorizationCodeListByIds(List<String> ids) {
        List<CcCategorizationCodeVO> ccCategorizationCodeList = ccCategorizationCodeMapper.selectCcCategorizationCodeListByIds(ids);

		TorchResponse<List<CcCategorizationCodeVO>> response = new TorchResponse<List<CcCategorizationCodeVO>>();
		response.getData().setData(ccCategorizationCodeList);
		response.setStatus(200);
		response.getData().setCount((long)ccCategorizationCodeList.size());
		return response;
    }




}
