package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.modules.business.domain.vo.ProductionValueCalculationDuiZhangVO;
import com.huatek.frame.modules.business.domain.vo.ProductionValueCalculationVO;
import com.huatek.frame.modules.business.service.ProdValCalcDetailsService;
import com.huatek.frame.modules.business.service.ProductionValueCalculationService;
import com.huatek.frame.modules.business.service.dto.ProductionValueCalculationContractDTO;
import com.huatek.frame.modules.business.service.dto.ProductionValueCalculationDTO;
import com.huatek.frame.modules.business.service.dto.ProductionValueCalculationDuiZhangDTO;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-08-22
**/
@Api(tags = "产值计算管理")
@RestController
@RequestMapping("/api/productionValueCalculation")
public class ProductionValueCalculationController {

	@Autowired
    private ProductionValueCalculationService productionValueCalculationService;
    @Autowired
    private ProdValCalcDetailsService prodValCalcDetailsService;

	/**
	 * 产值计算列表
	 * 
	 * @param dto 产值计算DTO 实体对象
	 * @return
	 */
    @Log("产值计算列表")
    @ApiOperation(value = "产值计算列表查询")
    @PostMapping(value = "/productionValueCalculationList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionValueCalculation:list")
    public TorchResponse<List<ProductionValueCalculationVO>> query(@RequestBody ProductionValueCalculationDTO dto){
        return productionValueCalculationService.findProductionValueCalculationPage(dto);
    }

	/**
	 * 新增/修改产值计算
	 * 
	 * @param productionValueCalculationDto 产值计算DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改产值计算")
    @ApiOperation(value = "产值计算新增/修改操作")
    @PostMapping(value = "/productionValueCalculation", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionValueCalculation:add#productionValueCalculation:edit")
    public TorchResponse add(@RequestBody ProductionValueCalculationDTO productionValueCalculationDto) throws Exception {
		// BeanValidatorFactory.validate(productionValueCalculationDto);
		return productionValueCalculationService.saveOrUpdate(productionValueCalculationDto);
	}
//
//	/**
//	 * 查询产值计算详情
//	 *
//	 * @param id 主键id
//	 * @return
//	 */
//	@SuppressWarnings({ "rawtypes", "unchecked" })
//    @Log("产值计算详情")
//    @ApiOperation(value = "产值计算详情查询")
//    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
//    @TorchPerm("productionValueCalculation:detail")
//	public TorchResponse detail(@PathVariable(value = "id") String id) {
//		return productionValueCalculationService.findProductionValueCalculation(id);
//	}

	/**
	 * 删除产值计算
	 * 
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
    @Log("删除产值计算")
    @ApiOperation(value = "产值计算删除操作")
    @TorchPerm("productionValueCalculation:del")
    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	public TorchResponse delete(@RequestBody String[] ids) {
		return productionValueCalculationService.delete(ids);
	}

    @ApiOperation(value = "产值计算联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return productionValueCalculationService.getOptionsList(id);
	}





    @Log("产值计算导出")
    @ApiOperation(value = "产值计算导出")
    @TorchPerm("productionValueCalculation:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody ProductionValueCalculationDTO dto)
    {
        List<ProductionValueCalculationVO> list = productionValueCalculationService.selectProductionValueCalculationList(dto);
        ExcelUtil<ProductionValueCalculationVO> util = new ExcelUtil<ProductionValueCalculationVO>(ProductionValueCalculationVO.class);
        util.exportExcel(response, list, "产值计算数据");
    }

    @Log("产值计算导入")
    @ApiOperation(value = "产值计算导入")
    @TorchPerm("productionValueCalculation:duizhang")
    @PostMapping("/importData")
    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
    {
        ExcelUtil<ProductionValueCalculationDuiZhangVO> util = new ExcelUtil<ProductionValueCalculationDuiZhangVO>(ProductionValueCalculationDuiZhangVO.class);
        List<ProductionValueCalculationDuiZhangVO> list = util.importExcel(file.getInputStream());
        return productionValueCalculationService.importProductionValueCalculation(list, unionColumns, true, "");
    }

    @Log("产值计算对账导入模板")
    @ApiOperation(value = "产值计算对账导入模板")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<ProductionValueCalculationDuiZhangVO> util = new ExcelUtil<ProductionValueCalculationDuiZhangVO>(ProductionValueCalculationDuiZhangVO.class);
        util.importTemplateExcel(response, "产值计算对账数据");
    }

    @Log("根据Ids获取产值计算列表")
    @ApiOperation(value = "产值计算 根据Ids批量查询")
    @PostMapping(value = "/productionValueCalculationList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getProductionValueCalculationListByIds(@RequestBody List<String> ids) {
        return productionValueCalculationService.selectProductionValueCalculationListByIds(ids);
    }

    /**
     * 产值计算明细
     *
     * @param productionValueCalculationId 主键id
     * @return
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("产值计算明细")
    @ApiOperation(value = "产值计算明细查询")
    @GetMapping(value = "/selectDetailsListByPVCId/{productionValueCalculationId}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionValueCalculation:detail")
    public TorchResponse selectDetailsListByPVCId(@PathVariable(value = "productionValueCalculationId") String productionValueCalculationId) {
        return prodValCalcDetailsService.selectProdValCalcDetailsListByProductionValueCalculationId(productionValueCalculationId);
    }


    @Log("产值计算对账")
    @ApiOperation(value = "产值计算对账")
    @PostMapping(value = "/duizhang", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionValueCalculation:duizhang")
    public TorchResponse duiZhang(@RequestBody ProductionValueCalculationDuiZhangDTO productionValueCalculationDuiZhangDTO) {
        return productionValueCalculationService.duiZhang(productionValueCalculationDuiZhangDTO);
    }

    @ApiOperation(value = "更新合同编号")
    @PostMapping(value = "/updateContract", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionValueCalculation:contract")
    public TorchResponse updateContract(@RequestBody ProductionValueCalculationContractDTO productionValueCalculationContractDTO) {
        return productionValueCalculationService.updateContract(productionValueCalculationContractDTO);
    }

    /**
     * 生成产值计算
     *
     * @return
     * @throws Exception
     */
    @SuppressWarnings("rawtypes")
    @Log("生成产值计算")
    @ApiOperation(value = "生成产值计算")
    @PostMapping(value = "/generateCalculation", produces = { "application/json;charset=utf-8" })
    public TorchResponse generateCalculation() throws Exception {
        return productionValueCalculationService.generateCalculation();
    }

    /**
     * 客户价格重新核算
     *
     * @param ids 记录ID列表
     * @return
     */
    @SuppressWarnings("rawtypes")
    @Log("客户价格重新核算")
    @ApiOperation(value = "客户价格重新核算")
    @PostMapping(value = "/recalculateCustomerPrice", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionValueCalculation:recalculateCustomerPrice")
    public TorchResponse recalculateCustomerPrice(@RequestBody List<String> ids) {
        return productionValueCalculationService.recalculateCustomerPrice(ids);
    }

    /**
     * 内部价格重新核算
     *
     * @param ids 记录ID列表
     * @return
     */
    @SuppressWarnings("rawtypes")
    @Log("内部价格重新核算")
    @ApiOperation(value = "内部价格重新核算")
    @PostMapping(value = "/recalculateInternalPrice", produces = { "application/json;charset=utf-8" })
    @TorchPerm("productionValueCalculation:recalculateInternalPrice")
    public TorchResponse recalculateInternalPrice(@RequestBody List<String> ids) {
        return productionValueCalculationService.recalculateInternalPrice(ids);
    }

}