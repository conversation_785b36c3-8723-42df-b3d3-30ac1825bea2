2025-08-25 09:30:39,164 WARN basic [main] o.m.s.mapper.ClassPathMapperScanner [Logger.java : 44] No MyBatis mapper was found in '[com.huatek.frame]' package. Please check your configuration.
2025-08-25 09:30:52,106 WARN basic [main] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:0) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:31:14,617 WARN basic [main] c.b.m.c.injector.DefaultSqlInjector [DefaultSqlInjector.java : 56] class com.huatek.frame.modules.system.domain.RoleGroup ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-25 09:31:14,800 WARN basic [main] c.b.m.c.injector.DefaultSqlInjector [DefaultSqlInjector.java : 56] class com.huatek.frame.modules.system.domain.RolePermission ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-25 09:31:18,979 WARN basic [main] c.b.m.c.injector.DefaultSqlInjector [DefaultSqlInjector.java : 56] class com.huatek.frame.modules.system.domain.UserRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-25 09:31:19,096 WARN basic [main] c.b.m.c.injector.DefaultSqlInjector [DefaultSqlInjector.java : 56] class com.huatek.frame.modules.system.domain.RuleRole ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-25 09:31:59,793 WARN basic [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 121] No URLs will be polled as dynamic configuration sources.
2025-08-25 09:31:59,816 WARN basic [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 121] No URLs will be polled as dynamic configuration sources.
2025-08-25 09:32:06,221 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:0) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:32:09,382 WARN basic [main] s.d.s.w.r.p.ParameterDataTypeReader [ParameterDataTypeReader.java : 102] Trying to infer dataType org.springframework.web.multipart.MultipartFile[]
2025-08-25 09:32:36,259 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:1) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:33:06,289 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:1) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:33:36,309 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:2) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:33:36,323 WARN basic [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] c.a.c.d.registry.DubboCloudRegistry [DubboCloudRegistry.java : 256] APP jx-mes-gateway instance changed, size changed zero!!!
2025-08-25 09:34:06,348 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:2) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:34:36,368 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:2) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:35:06,386 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:2) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:35:36,411 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:2) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:35:38,784 WARN basic [main] o.m.s.mapper.ClassPathMapperScanner [Logger.java : 44] No MyBatis mapper was found in '[com.huatek.frame]' package. Please check your configuration.
2025-08-25 09:35:46,091 WARN basic [main] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:2) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:36:06,427 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:2) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:36:17,662 WARN basic [main] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 476] The Spring application[name : jx-mes-basic-application] does not expose The REST metadata in the Dubbo services.
2025-08-25 09:36:17,663 WARN basic [main] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 476] The Spring application[name : jx-mes-gateway] does not expose The REST metadata in the Dubbo services.
2025-08-25 09:36:17,672 WARN basic [main] c.a.c.d.r.GenearalServiceSubscribeHandler [GenearalServiceSubscribeHandler.java : 247] The metadata of Dubbo service[key : com.huatek.frame.modules.system.service.SysProcessDefinitionService] still can't be found, it could effect the further Dubbo service invocation
2025-08-25 09:36:17,733 WARN basic [main] c.a.c.d.r.GenearalServiceSubscribeHandler [GenearalServiceSubscribeHandler.java : 247] The metadata of Dubbo service[key : com.huatek.frame.modules.system.service.SysProcessRecordService] still can't be found, it could effect the further Dubbo service invocation
2025-08-25 09:36:33,449 WARN basic [main] c.a.c.d.r.GenearalServiceSubscribeHandler [GenearalServiceSubscribeHandler.java : 247] The metadata of Dubbo service[key : com.huatek.frame.modules.system.service.SysGroupService] still can't be found, it could effect the further Dubbo service invocation
2025-08-25 09:36:35,218 WARN basic [main] c.a.c.d.r.GenearalServiceSubscribeHandler [GenearalServiceSubscribeHandler.java : 247] The metadata of Dubbo service[key : com.huatek.frame.modules.system.service.SysUserService] still can't be found, it could effect the further Dubbo service invocation
2025-08-25 09:36:36,440 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:2) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:36:36,736 WARN basic [main] c.a.c.d.r.GenearalServiceSubscribeHandler [GenearalServiceSubscribeHandler.java : 247] The metadata of Dubbo service[key : com.huatek.frame.modules.system.service.RoleService] still can't be found, it could effect the further Dubbo service invocation
2025-08-25 09:36:36,793 WARN basic [main] c.a.c.d.r.GenearalServiceSubscribeHandler [GenearalServiceSubscribeHandler.java : 247] The metadata of Dubbo service[key : com.huatek.frame.modules.system.service.DicDetailService] still can't be found, it could effect the further Dubbo service invocation
2025-08-25 09:36:37,191 WARN basic [main] c.a.c.d.r.GenearalServiceSubscribeHandler [GenearalServiceSubscribeHandler.java : 247] The metadata of Dubbo service[key : com.huatek.frame.modules.log.service.LogService] still can't be found, it could effect the further Dubbo service invocation
2025-08-25 09:36:40,444 WARN basic [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 121] No URLs will be polled as dynamic configuration sources.
2025-08-25 09:36:40,469 WARN basic [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 121] No URLs will be polled as dynamic configuration sources.
2025-08-25 09:36:41,759 WARN basic [main] o.s.b.a.t.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration [ThymeleafAutoConfiguration.java : 106] Cannot find template location: classpath:/templates/ (please add some templates or check your Thymeleaf configuration)
2025-08-25 09:36:45,150 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:2) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:37:06,449 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:37:06,456 WARN basic [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] c.a.c.d.registry.DubboCloudRegistry [DubboCloudRegistry.java : 256] APP jx-mes-business-application instance changed, size changed zero!!!
2025-08-25 09:37:15,172 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:37:35,679 WARN basic [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] c.a.c.d.registry.DubboCloudRegistry [DubboCloudRegistry.java : 372] Subscription app jx-mes-business-application, can't find metadata handler
2025-08-25 09:37:36,466 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:37:45,186 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:38:06,473 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:38:15,199 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:38:36,484 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:38:45,224 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:39:06,500 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:39:15,239 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:39:36,514 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:39:45,279 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:40:06,523 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:40:15,293 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:40:36,531 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:40:45,303 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:41:06,538 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:41:15,315 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:41:36,548 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:41:45,336 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:42:06,557 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:42:15,349 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:42:36,567 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:42:45,376 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:43:06,576 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:43:15,392 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:43:36,588 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:43:45,406 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:44:06,598 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:44:15,467 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:44:36,608 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:44:45,475 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:45:06,616 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:45:15,486 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:45:36,632 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:45:45,494 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:46:06,642 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:46:15,502 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:46:36,650 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:46:45,513 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:47:06,658 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:47:15,523 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:47:36,668 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:47:45,533 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:48:06,678 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:48:15,541 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:48:36,687 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:48:45,550 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:49:06,700 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:49:15,556 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:49:36,713 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:49:45,567 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:50:06,723 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:50:15,583 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:50:36,730 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:50:45,590 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:51:06,742 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:51:15,604 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:51:36,752 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:51:45,613 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:52:06,762 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:52:15,620 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:52:36,772 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:52:45,627 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:53:06,782 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:53:15,637 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:53:36,799 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:53:45,648 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:54:06,809 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:54:15,660 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:54:36,822 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:54:45,671 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:55:06,830 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:55:15,681 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:55:36,838 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:55:45,691 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:56:06,847 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:56:15,703 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:56:36,856 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:56:45,711 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:57:06,866 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:57:15,721 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:57:36,874 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:57:45,731 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:58:06,887 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:58:15,740 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:58:36,896 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:58:45,751 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:59:06,916 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:59:15,770 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:59:36,925 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 09:59:45,783 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:00:06,934 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:00:15,794 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:00:36,946 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:00:45,803 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:01:06,956 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:01:15,814 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:01:36,967 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:01:45,823 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:02:06,977 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:02:15,834 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:02:36,984 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:02:45,852 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:03:06,990 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:03:15,873 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:03:36,997 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:03:45,883 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:04:07,005 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:04:15,892 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:04:37,012 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:04:45,902 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:05:07,021 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:05:15,912 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:05:37,028 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:05:45,921 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:06:07,053 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:06:15,931 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:06:37,076 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:06:45,937 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:07:07,084 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:07:15,955 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:07:37,094 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:07:45,967 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:08:07,099 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:08:15,980 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:08:37,109 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:08:45,988 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:09:07,122 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:09:16,004 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:09:37,128 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:09:46,014 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:10:07,137 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:10:16,026 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:10:37,145 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:10:46,035 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:11:07,155 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:11:16,045 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:11:37,162 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:11:46,055 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:12:07,170 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:12:16,065 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:12:37,176 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:12:46,074 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:13:07,186 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:13:16,085 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:13:37,195 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:13:46,097 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:14:07,204 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:14:16,106 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:14:37,213 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:14:46,116 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:15:07,221 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:15:16,123 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:15:37,230 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:15:46,131 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:16:07,236 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:16:16,141 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:16:37,247 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:16:46,150 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:17:07,260 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:17:16,160 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:17:37,269 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:17:46,170 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:18:07,278 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:18:16,187 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:18:37,289 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:18:46,203 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:19:07,296 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:19:16,217 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:19:37,306 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:19:46,223 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:20:07,314 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:20:16,234 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:20:37,324 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:20:46,258 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:21:07,334 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:21:16,265 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:21:37,343 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:21:46,273 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:22:07,365 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:22:16,296 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:22:37,376 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:22:46,315 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:23:07,382 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:23:16,335 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:23:37,393 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:23:46,341 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:24:07,397 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:24:16,349 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:24:37,412 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:24:46,360 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:25:07,419 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:25:16,383 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:25:37,424 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:25:46,395 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:26:07,429 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:26:16,402 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:26:37,435 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:26:46,412 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:27:07,451 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:27:16,424 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:27:37,460 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:27:46,436 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:28:07,465 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:28:16,445 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:28:37,470 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:28:46,456 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:29:07,475 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:29:16,469 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:29:37,483 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:29:46,486 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:30:07,492 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:30:16,493 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:30:37,512 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:30:46,512 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:31:07,518 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:31:16,536 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:31:37,523 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:31:46,546 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:32:07,533 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:32:16,570 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:32:37,544 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:32:46,580 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:33:07,554 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:33:16,592 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:33:37,562 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:33:46,601 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:34:07,580 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:34:16,611 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:34:37,589 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:34:46,620 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:35:07,597 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:35:16,638 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:35:37,606 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:35:46,646 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:36:07,623 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:36:16,656 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:36:37,631 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:36:46,664 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:37:07,639 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:37:16,677 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:37:37,648 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:37:46,687 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:38:07,653 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:38:16,694 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:38:37,667 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:38:46,712 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:39:07,676 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:39:16,721 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:39:37,684 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:39:46,741 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:40:07,693 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:40:16,751 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:40:37,703 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:40:46,767 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:41:07,711 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:41:16,774 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:41:37,719 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:41:46,782 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:42:07,726 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:42:16,792 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:42:37,735 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:42:46,800 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:43:07,750 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:43:16,816 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:43:37,757 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:43:46,821 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:44:07,766 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:44:16,830 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:44:37,789 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:44:46,841 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:45:07,798 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:45:16,865 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:45:37,807 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:45:46,872 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:46:07,817 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:46:16,881 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:46:37,827 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:46:46,901 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:47:07,834 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:47:16,910 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:47:37,842 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:47:46,920 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:48:07,850 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:48:16,927 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:48:37,858 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:48:46,937 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:49:07,866 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:49:16,945 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:49:37,871 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:49:46,952 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:50:07,880 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:50:16,963 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:50:37,891 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:50:46,985 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:51:07,898 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:51:16,994 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:51:37,906 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:51:47,002 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:52:07,916 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:52:17,012 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:52:37,925 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:52:47,022 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:53:07,934 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:53:17,031 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:53:37,942 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:53:47,042 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:54:07,950 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:54:17,053 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:54:37,959 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:54:47,061 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:55:07,968 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:55:17,072 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:55:37,989 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:55:47,080 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:56:07,997 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:56:17,090 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:56:38,003 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:56:47,099 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:57:08,010 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:57:17,106 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:57:38,018 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:57:47,116 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:58:08,027 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:58:17,126 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:58:38,036 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:58:47,132 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:59:08,044 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:59:17,141 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:59:38,050 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 10:59:47,147 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:00:08,065 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:00:17,170 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:00:38,071 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:00:47,190 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:01:08,081 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:01:17,205 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:01:38,097 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:01:47,214 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:02:08,105 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:02:17,223 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:02:38,115 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:02:47,233 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:03:08,121 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:03:17,240 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:03:38,131 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:03:47,250 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:04:08,147 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:04:17,259 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:04:38,154 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:04:47,282 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:05:08,168 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:05:17,287 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:05:38,176 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:05:47,297 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:06:08,185 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:06:17,317 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:06:38,195 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:06:47,338 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:07:08,210 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:07:17,346 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:07:38,222 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:07:47,358 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:08:08,229 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:08:17,368 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:08:38,238 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:08:47,374 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:09:08,247 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:09:17,383 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:09:38,276 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:09:47,393 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:10:08,283 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:10:17,406 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:10:38,291 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:10:47,422 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:11:08,299 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:11:17,430 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:11:38,309 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:11:47,436 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:12:08,317 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:12:17,444 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:12:38,325 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:12:47,454 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:13:08,331 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:13:17,460 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:13:38,338 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:13:47,473 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:14:08,348 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:14:17,484 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:14:38,359 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:14:47,491 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:15:08,368 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:15:17,498 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:15:38,376 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:15:47,506 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:16:08,395 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:16:17,526 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:16:38,403 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:16:47,545 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:17:08,413 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:17:17,553 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:17:38,422 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:17:47,562 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:18:08,429 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:18:17,577 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:18:38,451 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:18:47,583 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:19:08,460 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:19:17,595 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:19:38,469 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:19:47,604 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:20:08,479 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:20:17,613 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:20:38,487 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:20:47,623 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:21:08,496 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:21:17,629 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:21:38,504 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:21:47,638 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:22:08,518 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:22:17,657 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:22:38,534 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:22:47,667 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:23:08,543 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:23:17,676 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:23:38,552 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:23:47,687 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:24:08,561 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:24:17,697 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:24:38,570 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:24:47,706 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:25:08,579 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:25:17,715 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:25:38,588 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:25:47,722 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:26:08,594 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:26:17,733 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:26:38,599 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:26:47,751 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:27:08,604 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:27:17,756 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:27:38,613 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:27:47,764 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:28:08,623 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:28:17,779 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:28:38,631 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:28:47,788 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:29:08,640 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:29:17,800 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:29:38,648 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:29:47,809 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:30:08,655 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:30:17,816 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:30:38,663 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:30:47,824 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:31:08,669 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:31:17,834 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:31:38,678 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:31:47,843 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:32:08,685 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:32:17,853 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:32:38,705 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:32:47,865 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:33:08,715 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:33:17,872 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:33:38,723 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:33:47,887 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:34:08,731 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:34:17,894 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:34:38,741 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:34:47,905 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:35:08,757 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:35:17,910 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:35:38,765 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:35:47,915 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:36:08,773 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:36:17,924 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:36:38,785 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:36:47,934 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:37:08,794 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:37:17,941 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:37:38,799 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:37:47,959 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:38:08,809 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:38:17,971 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:38:38,817 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:38:47,976 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:39:08,825 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:39:17,998 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:39:38,835 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:39:48,006 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:40:08,842 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:40:18,025 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:40:38,856 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:40:48,043 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:41:08,863 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:41:18,049 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:41:38,872 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:41:48,059 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:42:08,880 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:42:18,065 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:42:38,887 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:42:48,073 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:43:08,897 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:43:18,085 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:43:38,905 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:43:48,099 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:44:08,912 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:44:18,106 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:44:38,917 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:44:48,131 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:45:08,923 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:45:18,135 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:45:38,931 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:45:48,141 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:46:08,941 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:46:18,147 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:46:38,949 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:46:48,166 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:47:08,957 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:47:18,177 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:47:38,965 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:47:48,182 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:48:08,974 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:48:18,188 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:48:38,982 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:48:48,194 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:49:08,991 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:49:18,199 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:49:39,010 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:49:48,215 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:50:09,022 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:50:18,222 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:50:39,029 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:50:48,229 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:51:09,049 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:51:18,243 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:51:39,056 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:51:48,264 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:52:09,064 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:52:18,271 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:52:39,074 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:52:48,289 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:53:09,082 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:53:18,297 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:53:39,091 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:53:48,307 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:54:09,108 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:54:18,318 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:54:39,117 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:54:48,322 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:55:09,127 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:55:18,338 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:55:39,135 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:55:48,347 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:56:09,157 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:56:18,352 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:56:39,164 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:56:48,362 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:57:09,173 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:57:18,371 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:57:39,182 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:57:48,389 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:58:09,190 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:58:18,398 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:58:39,197 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:58:48,407 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:59:09,205 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:59:18,420 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:59:39,213 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 11:59:48,429 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:00:09,232 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:00:18,443 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:00:39,240 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:00:48,452 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:01:09,248 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:01:18,463 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:01:39,257 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:01:48,482 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:02:09,265 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:02:18,490 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:02:39,273 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:02:48,502 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:03:09,280 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:03:18,516 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:03:39,288 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:03:48,524 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:04:09,295 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:04:18,534 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:04:39,304 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:04:48,557 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:05:09,314 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:05:18,574 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:05:39,321 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:05:48,583 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:06:09,325 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:06:18,606 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:06:39,333 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:06:48,615 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:07:09,342 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:07:18,623 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:07:39,350 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:07:48,632 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:08:09,360 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:08:18,640 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:08:39,368 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:08:48,649 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:09:09,376 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:09:18,657 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:09:39,384 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:09:48,666 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:10:09,389 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:10:18,674 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:10:39,397 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:10:48,684 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:11:09,404 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:11:18,692 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:11:39,412 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:11:48,700 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:12:09,418 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:12:18,714 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:12:39,430 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:12:48,723 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:13:09,441 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:13:18,731 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:13:39,463 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:13:48,741 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:14:09,470 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:14:18,749 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:14:39,477 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:14:48,759 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:15:09,497 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:15:18,780 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:15:39,504 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:15:48,798 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:16:09,512 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:16:18,814 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:16:39,520 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:16:48,822 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:17:09,528 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:17:18,846 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:17:39,543 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:17:48,854 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:18:09,552 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:18:18,863 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:18:39,559 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:18:48,872 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:19:09,568 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:19:18,883 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:19:39,590 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:19:48,892 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:20:09,603 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:20:18,901 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:20:39,622 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:20:48,910 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:21:09,641 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:21:18,920 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:21:39,650 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:21:48,930 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:22:09,657 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:22:18,939 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:22:39,664 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:22:48,949 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:23:09,682 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:23:18,969 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:23:39,698 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:23:48,979 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:24:09,707 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:24:18,988 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:24:39,715 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:24:48,999 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:25:09,724 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:25:19,007 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:25:39,730 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:25:49,025 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:26:09,738 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:26:19,034 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:26:39,746 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:26:49,047 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:27:09,759 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:27:19,057 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:27:39,768 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:27:49,068 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:28:09,777 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:28:19,078 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:28:39,799 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:28:49,086 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:29:09,804 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:29:19,096 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:29:39,827 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:29:49,115 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:30:09,834 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:30:19,124 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:30:39,852 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:30:49,133 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:31:09,859 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:31:19,141 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:31:39,867 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:31:49,151 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:32:09,873 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:32:19,159 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:32:39,881 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:32:49,182 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:33:09,888 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:33:19,191 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:33:39,895 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:33:49,200 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:34:09,903 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:34:19,209 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:34:39,911 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:34:49,233 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:35:09,917 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:35:19,243 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:35:39,921 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:35:49,249 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:36:09,929 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:36:19,259 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:36:39,936 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:36:49,268 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:37:09,944 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:37:19,278 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:37:39,950 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:37:49,287 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:38:09,965 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:38:19,311 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:38:39,973 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:38:49,331 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:39:09,980 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:39:19,340 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:39:39,989 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:39:49,354 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:40:09,997 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:40:19,363 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:40:40,005 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:40:49,369 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:41:10,013 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:41:19,375 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:41:40,022 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:41:49,391 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:42:10,028 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:42:19,400 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:42:40,036 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:42:49,409 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:43:10,049 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:43:19,417 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:43:40,059 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:43:49,426 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:44:10,083 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:44:19,435 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:44:40,091 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:44:49,444 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:45:10,099 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:45:19,455 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:45:40,104 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:45:49,463 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:46:10,124 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:46:19,476 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:46:40,133 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:46:49,485 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:47:10,140 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:47:19,494 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:47:40,147 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:47:49,503 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:48:10,155 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:48:19,514 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:48:40,163 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:48:49,523 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:49:10,171 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:49:19,533 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:49:40,178 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:49:49,542 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:50:10,194 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:50:19,551 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:50:40,203 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:50:49,560 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:51:10,210 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:51:19,572 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:51:40,218 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:51:49,582 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:52:10,233 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:52:19,592 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:52:40,240 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:52:49,601 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:53:10,247 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:53:19,610 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:53:40,256 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:53:49,616 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:54:10,264 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:54:19,638 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:54:40,272 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:54:49,647 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:55:10,289 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:55:19,655 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:55:40,300 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:55:49,664 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:56:10,307 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:56:19,684 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:56:40,316 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:56:49,697 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:57:10,326 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:57:19,705 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:57:40,342 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:57:49,714 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:58:10,350 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:58:19,724 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:58:40,357 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:58:49,732 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:59:10,369 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:59:19,741 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:59:40,379 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 12:59:49,750 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:00:10,386 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:00:19,760 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:00:40,395 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:00:49,768 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:01:10,402 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:01:19,777 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:01:40,420 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:01:49,786 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:02:10,428 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:02:19,794 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:02:40,440 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:02:49,803 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:03:10,459 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:03:19,811 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:03:40,468 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:03:49,820 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:04:10,475 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:04:19,829 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:04:40,485 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:04:49,838 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:05:10,492 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:05:19,847 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:05:40,499 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:05:49,857 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:06:10,504 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:06:19,862 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:06:40,513 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:06:49,879 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:07:10,532 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:07:19,897 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:07:40,540 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:07:49,906 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:08:10,548 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:08:19,928 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:08:40,557 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:08:49,938 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:09:10,565 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:09:19,946 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:09:40,574 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:09:49,954 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:10:10,578 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:10:19,962 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:10:40,588 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:10:49,968 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:11:10,613 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:11:19,977 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:11:40,627 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:11:49,987 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:12:10,647 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:12:19,993 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:12:40,656 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:12:50,010 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:13:10,664 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:13:20,019 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:13:40,672 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:13:50,036 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:14:10,680 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:14:20,044 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:14:40,688 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:14:50,054 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:15:10,695 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:15:20,074 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:15:40,713 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:15:50,084 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:16:10,722 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:16:20,092 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:16:40,731 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:16:50,109 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:17:10,739 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:17:20,127 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:17:40,748 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:17:50,137 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:18:10,756 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:18:20,145 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:18:40,764 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:18:50,154 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:19:10,773 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:19:20,164 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:19:40,781 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:19:50,182 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:20:10,790 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:20:20,192 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:20:40,796 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:20:50,199 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:21:10,804 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:21:20,208 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:21:40,809 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:21:50,217 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:22:10,816 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:22:20,235 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:22:40,822 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:22:50,255 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:23:10,832 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:23:20,265 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:23:40,839 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:23:50,273 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:24:10,846 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:24:20,283 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:24:40,854 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:24:50,292 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:25:10,865 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:25:20,302 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:25:40,884 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:25:50,312 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:26:10,892 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:26:20,321 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:26:40,897 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:26:50,329 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:27:10,906 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:27:20,338 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:27:40,913 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:27:50,347 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:28:10,919 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:28:20,363 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:28:40,927 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:28:50,372 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:29:10,933 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:29:20,383 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:29:40,947 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:29:50,403 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:30:10,954 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:30:20,412 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:30:40,964 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:30:50,423 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:31:10,972 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:31:20,431 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:31:40,980 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:31:50,455 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:32:10,989 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:32:20,464 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:32:40,999 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:32:50,472 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:33:11,007 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:33:20,481 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:33:41,016 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:33:50,491 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:34:11,023 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:34:20,500 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:34:41,031 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:34:50,510 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:35:11,041 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:35:20,534 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:35:41,049 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:35:50,551 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:36:11,057 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:36:20,559 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:36:41,065 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:36:50,566 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:37:11,086 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:37:20,587 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:37:41,170 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:37:50,784 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:38:11,189 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:38:20,795 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:38:41,200 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:38:50,803 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:39:11,206 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:39:20,813 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:39:41,219 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:39:50,819 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:40:11,239 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:40:20,827 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:40:41,250 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:40:50,836 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:41:11,257 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:41:20,866 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:41:41,265 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:41:50,901 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:42:11,284 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:42:20,912 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:42:41,292 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:42:50,928 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:43:11,300 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:43:20,939 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:43:41,322 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:43:50,958 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:44:11,335 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:44:20,967 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:44:41,341 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:44:50,976 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:45:11,346 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:45:20,985 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:45:41,356 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:45:50,994 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:46:11,361 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:46:21,013 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:46:41,367 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:46:51,022 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:47:11,372 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:47:21,039 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:47:41,394 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:47:51,057 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:48:11,400 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:48:21,072 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:48:41,408 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:48:51,091 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:49:11,412 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:49:21,100 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:49:41,419 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:49:51,109 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:50:11,427 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:50:21,115 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:50:41,432 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:50:51,122 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:51:11,437 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:51:21,128 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:51:41,443 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:51:51,138 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:52:11,447 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:52:21,145 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:52:41,455 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:52:51,167 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:53:11,462 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:53:21,174 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:53:41,468 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:53:51,180 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:54:11,476 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:54:21,193 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:54:41,485 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:54:51,204 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:55:11,492 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:55:21,218 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:55:41,499 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:55:51,225 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:56:11,513 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:56:21,234 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:56:41,520 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:56:51,240 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:57:11,528 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:57:21,250 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:57:41,533 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:57:51,266 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:58:11,540 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:58:21,282 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:58:41,558 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:58:51,290 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:59:11,566 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:59:21,296 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:59:41,575 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 13:59:51,310 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:00:11,583 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:00:21,318 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:00:41,592 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:00:51,333 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:01:11,610 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:01:21,345 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:01:41,618 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:01:51,354 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:02:11,625 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:02:21,360 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:02:41,631 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:02:51,369 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:03:11,639 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:03:21,377 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:03:41,646 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:03:51,433 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:04:11,662 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:04:21,445 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:04:41,677 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:04:51,453 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:05:11,685 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:05:21,468 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:05:41,699 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:05:51,483 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:06:11,712 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:06:21,500 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:06:41,720 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:06:51,520 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:07:11,728 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:07:21,528 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:07:41,733 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:07:51,561 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:08:11,755 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:08:21,570 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:08:41,777 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:08:51,574 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:09:11,787 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:09:21,582 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:09:41,793 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:09:51,591 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:10:11,800 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:10:21,607 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:10:41,808 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:10:51,630 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:11:11,818 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:11:21,644 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:11:41,826 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:11:51,654 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:12:11,834 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:12:21,664 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:12:41,842 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:12:51,673 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:13:11,862 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:13:21,682 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:13:41,868 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:13:51,777 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:14:11,876 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:14:21,783 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:14:41,884 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:14:51,793 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:15:11,893 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:15:21,805 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:15:41,901 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:15:51,811 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:16:11,911 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:16:21,821 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:16:41,919 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:16:51,841 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:17:11,926 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:17:21,864 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:17:41,933 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:17:51,874 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:18:11,944 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:18:21,890 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:18:41,961 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:18:51,908 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:19:11,967 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:19:21,917 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:19:41,972 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:19:51,924 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:20:11,976 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:20:21,942 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:20:41,983 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:20:51,948 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:21:11,991 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:21:21,965 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:21:41,999 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:21:51,974 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:22:12,006 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:22:21,981 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:22:42,013 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:22:51,989 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:23:12,025 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:23:21,999 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:23:42,029 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:23:52,008 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:24:12,041 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:24:22,018 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:24:42,049 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:24:52,036 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:25:12,059 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:25:22,045 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:25:42,067 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:25:52,063 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:26:12,076 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:26:22,074 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:26:42,088 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:26:52,090 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:27:12,104 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:27:22,100 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:27:42,117 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:27:52,112 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:28:12,132 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:28:22,133 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:28:42,143 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:28:52,146 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:29:12,154 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:29:22,155 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:29:42,166 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:29:52,162 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:30:12,174 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:30:22,168 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:30:42,180 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:30:52,182 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:31:12,184 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:31:22,196 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:31:42,192 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:31:52,208 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:32:12,198 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:32:22,220 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:32:42,205 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:32:52,237 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:33:12,213 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:33:22,256 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:33:42,219 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:33:52,263 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:34:12,238 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:34:22,274 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:34:42,245 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:34:52,299 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:35:12,267 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:35:22,311 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:35:42,276 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:35:52,322 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:36:12,284 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:36:22,337 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:36:42,296 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:36:52,347 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:37:12,314 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:37:22,357 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:37:42,326 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:37:52,381 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:38:12,331 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:38:22,391 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:38:42,340 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:38:52,400 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:39:12,360 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:39:22,515 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:39:42,368 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:39:52,538 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
2025-08-25 14:40:12,374 WARN basic [Nacos-Watch-Task-Scheduler-1] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 209] Current application will subscribe all services(size:3) in registry, a lot of memory and CPU cycles may be used, thus it's strongly recommend you using the externalized property 'dubbo.cloud.subscribed-services' to specify the services
