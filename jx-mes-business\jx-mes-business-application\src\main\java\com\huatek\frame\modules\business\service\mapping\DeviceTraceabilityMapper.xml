<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.DeviceTraceabilityMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.device_serial_number as deviceSerialNumber,
		t.specification_model as specificationModel,
		t.device_name as deviceName,
		t.scheduled_traceability_date as scheduledTraceabilityDate,
		t.traceback_scheme as tracebackScheme,
		t.traceback_date as tracebackDate,
		t.trace_confirm_form as traceConfirmForm,
		t.traceability_cyclein_months as traceabilityCycleinMonths,
        t.status as status,
		t.next_traceability_date as nextTraceabilityDate,
		t.CODEX_TORCH_CREATOR_ID as codexTorchCreatorId,
		t.CODEX_TORCH_UPDATER as codexTorchUpdater,
		t.CODEX_TORCH_GROUP_ID as codexTorchGroupId,
		t.CODEX_TORCH_CREATE_DATETIME as codexTorchCreateDatetime,
		t.CODEX_TORCH_UPDATE_DATETIME as codexTorchUpdateDatetime,
		t.CODEX_TORCH_DELETED as codexTorchDeleted
	</sql>
	<select id="selectDeviceTraceabilityPage" parameterType="com.huatek.frame.modules.business.service.dto.DeviceTraceabilityDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.DeviceTraceabilityVO">
		select
		<include refid="Base_Column_List" />, g.group_name as belongGroupName, e.d_03 as serialNumber, e.responsible_person as responsiblePerson
			from device_traceability t
            LEFT JOIN equipment_inventory e ON e.id = t.equipment_inventory_id
            LEFT JOIN sys_group g ON g.id = e.belonging_group
            <where>
                and 1=1
                <if test="deviceSerialNumber != null and deviceSerialNumber != ''">
                    and t.device_serial_number  like concat('%', #{deviceSerialNumber} ,'%')
                </if>
                <if test="specificationModel != null and specificationModel != ''">
                    and t.specification_model  like concat('%', #{specificationModel} ,'%')
                </if>
                <if test="deviceName != null and deviceName != ''">
                    and t.device_name  like concat('%', #{deviceName} ,'%')
                </if>
                <if test="scheduledTraceabilityDate != null and scheduledTraceabilityDate != ''">
                    and t.scheduled_traceability_date  like concat('%', #{scheduledTraceabilityDate} ,'%')
                </if>
                <if test="tracebackScheme != null and tracebackScheme != ''">
                    and t.traceback_scheme  = #{tracebackScheme}
                </if>
                <if test="tracebackDate != null and tracebackDate != ''">
                    and t.traceback_date  = #{tracebackDate}
                </if>
                <if test="traceConfirmForm != null and traceConfirmForm != ''">
                    and t.trace_confirm_form  = #{traceConfirmForm}
                </if>
                <if test="traceabilityCycleinMonths != null and traceabilityCycleinMonths != ''">
                    and t.traceability_cyclein_months  = #{traceabilityCycleinMonths}
                </if>
                <if test="status != null and status != ''">
                    and t.status  = #{status}
                </if>
                <if test="nextTraceabilityDate != null and nextTraceabilityDate != ''">
                    and t.next_traceability_date  = #{nextTraceabilityDate}
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.CODEX_TORCH_CREATOR_ID  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.CODEX_TORCH_UPDATER  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.CODEX_TORCH_GROUP_ID  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.CODEX_TORCH_CREATE_DATETIME  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.CODEX_TORCH_UPDATE_DATETIME  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.CODEX_TORCH_DELETED  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectDeviceTraceabilityList" parameterType="com.huatek.frame.modules.business.service.dto.DeviceTraceabilityDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.DeviceTraceabilityVO">
		select
		<include refid="Base_Column_List" />
			from device_traceability t
            <where>
                and 1=1
                <if test="deviceSerialNumber != null and deviceSerialNumber != ''">
                    and t.device_serial_number  like concat('%', #{deviceSerialNumber} ,'%')
                </if>
                <if test="specificationModel != null and specificationModel != ''">
                    and t.specification_model  like concat('%', #{specificationModel} ,'%')
                </if>
                <if test="deviceName != null and deviceName != ''">
                    and t.device_name  like concat('%', #{deviceName} ,'%')
                </if>
                <if test="scheduledTraceabilityDate != null and scheduledTraceabilityDate != ''">
                    and t.scheduled_traceability_date  like concat('%', #{scheduledTraceabilityDate} ,'%')
                </if>
                <if test="tracebackScheme != null and tracebackScheme != ''">
                    and t.traceback_scheme  = #{tracebackScheme}
                </if>
                <if test="tracebackDate != null">
                    and t.traceback_date  = #{tracebackDate}
                </if>
                <if test="traceConfirmForm != null and traceConfirmForm != ''">
                    and t.trace_confirm_form  = #{traceConfirmForm}
                </if>
                <if test="traceabilityCycleinMonths != null and traceabilityCycleinMonths != ''">
                    and t.traceability_cyclein_months  = #{traceabilityCycleinMonths}
                </if>
                <if test="nextTraceabilityDate != null and nextTraceabilityDate != ''">
                    and t.next_traceability_date  = #{nextTraceabilityDate}
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.CODEX_TORCH_CREATOR_ID  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.CODEX_TORCH_UPDATER  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.CODEX_TORCH_GROUP_ID  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.CODEX_TORCH_CREATE_DATETIME  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.CODEX_TORCH_UPDATE_DATETIME  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.CODEX_TORCH_DELETED  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectDeviceTraceabilityListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.DeviceTraceabilityVO">
		select
		<include refid="Base_Column_List" />
			from device_traceability t
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>

	<select id="selectValidityPeriod" resultType="java.lang.String">
        SELECT next_traceability_date
        FROM device_traceability
        WHERE equipment_inventory_id = #{id}
<!--        AND traceback_date IS NOT NULL-->
<!--        AND next_traceability_date IS NOT NULL-->
<!--        AND STR_TO_DATE(traceback_date, '%Y-%m-%d') &lt;= DATE(#{date})-->
<!--        AND STR_TO_DATE(next_traceability_date, '%Y-%m-%d') &gt;= DATE(#{date})-->
        ORDER BY next_traceability_date DESC
        LIMIT 1
    </select>
</mapper>