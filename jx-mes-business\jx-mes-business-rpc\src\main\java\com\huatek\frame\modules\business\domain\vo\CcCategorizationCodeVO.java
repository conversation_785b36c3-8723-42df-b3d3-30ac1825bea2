package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;

/**
* @description 客户分类对应关系VO实体类
* <AUTHOR>
* @date 2025-08-22
**/
@Data
@ApiModel("客户分类对应关系DTO实体类")
public class CcCategorizationCodeVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
	 * 客户器件分类
     **/
    @ApiModelProperty("客户器件分类")
    @Excel(name = "客户器件分类",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String customerDeviceClassification;

    /**
	 * 内部分类
     **/
    @ApiModelProperty("内部分类")
    @Excel(name = "内部分类",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String internalClassification;

    /**
	 * 收费标准编号
     **/
    @ApiModelProperty("收费标准编号")
    @Excel(name = "收费标准编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String chargeStandardNumber;

    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private String codexTorchUpdater;

    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}