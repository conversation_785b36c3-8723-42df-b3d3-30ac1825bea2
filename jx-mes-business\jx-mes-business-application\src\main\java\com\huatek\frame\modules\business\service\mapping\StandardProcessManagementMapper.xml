<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.StandardProcessManagementMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.step_number as stepNumber,
		t.process_name2 as processName2,
		t.process_classification as processClassification,
		t.process_description as processDescription,
        t.test_basis as testBasis,
        t.judgment_criteria as judgmentCriteria,
<!--		t.workstation as workstation,-->
<!--		t.testing_team as testingTeam,-->
		t.threshold_of_pda as thresholdOfPda,
		t.is_approval as isApproval,
		t.testing_times as testingTimes,
		t.duration_of_testing as durationOfTesting,
<!--		t.mp_pda_calc_rls as mpPdaCalcRls,-->
<!--		t.total_noncompliant_count as totalNoncompliantCount,-->
		t.`comment` as `comment`,
<!--        t.department as department,-->
		t.CODEX_TORCH_CREATOR_ID as codexTorchCreatorId,
		t.CODEX_TORCH_UPDATER as codexTorchUpdater,
		t.CODEX_TORCH_GROUP_ID as codexTorchGroupId,
		t.CODEX_TORCH_CREATE_DATETIME as codexTorchCreateDatetime,
		t.CODEX_TORCH_UPDATE_DATETIME as codexTorchUpdateDatetime,
		t.CODEX_TORCH_DELETED as codexTorchDeleted
	</sql>
	<select id="selectStandardProcessManagementPage" parameterType="com.huatek.frame.modules.business.service.dto.StandardProcessManagementDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.StandardProcessManagementVO">
		select
		<include refid="Base_Column_List" />,sg.group_name as department,s.group_name as testingTeam,w.workstation_name as workstation,
        (select GROUP_CONCAT(DISTINCT s.process_name2 SEPARATOR ', ') from standard_process_management s where  FIND_IN_SET(s.id, t.mp_pda_calc_rls)) as mpPdaCalcRls,
        (select GROUP_CONCAT(DISTINCT s.process_name2 SEPARATOR ', ') from standard_process_management s where t.total_noncompliant_count = s.id) as totalNoncompliantCount
        from standard_process_management t left join sys_group sg on t.department  = sg.id
        left join sys_group s on t.testing_team  = s.id left join workstation w on t.workstation =w.id
            <where>
                and 1=1
                <if test="stepNumber != null and stepNumber != ''">
                    and t.step_number  like concat('%', #{stepNumber} ,'%')
                </if>
                <if test="processName2 != null and processName2 != ''">
                    and t.process_name2  like concat('%', #{processName2} ,'%')
                </if>
                <if test="processClassification != null and processClassification != ''">
                    and t.process_classification  = #{processClassification}
                </if>
                <if test="processDescription != null and processDescription != ''">
                    and t.process_description  like concat('%', #{processDescription} ,'%')
                </if>
                <if test="workstation != null and workstation != ''">
                    and w.id = #{workstation}
                </if>

                <if test="testingTeam != null and testingTeam != ''">
                    and s.id  = #{testingTeam}
                </if>
                <if test="thresholdOfPda != null and thresholdOfPda != ''">
                    and t.threshold_of_pda  like concat('%', #{thresholdOfPda} ,'%')
                </if>
                <if test="isApproval != null and isApproval != ''">
                    and t.is_approval  = #{isApproval}
                </if>
                <if test="testingTimes != null and testingTimes != ''">
                    and t.testing_times  = #{testingTimes}
                </if>
                <if test="durationOfTesting != null and durationOfTesting != ''">
                    and t.duration_of_testing  = #{durationOfTesting}
                </if>
                <if test="mpPdaCalcRls != null and mpPdaCalcRls != ''">
                    and t.mp_pda_calc_rls  = #{mpPdaCalcRls}
                </if>
                <if test="totalNoncompliantCount != null and totalNoncompliantCount != ''">
                    and t.total_noncompliant_count  = #{totalNoncompliantCount}
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  like concat('%', #{comment} ,'%')
                </if>
                <if test="department != null and department != ''">
                    and sg.id  like concat('%', #{department} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.CODEX_TORCH_CREATOR_ID  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.CODEX_TORCH_UPDATER  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.CODEX_TORCH_GROUP_ID  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.CODEX_TORCH_CREATE_DATETIME  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.CODEX_TORCH_UPDATE_DATETIME  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.CODEX_TORCH_DELETED  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>
     <select id="selectOptionsByWorkstation" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.workstation_name label,
        	t.id value
        from workstation t
        WHERE t.workstation_number != '' and t.group_id = #{department}
     </select>
     <select id="selectOptionsByTestingTeam" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.group_name label,
        	t.id value
        from sys_group t
        WHERE t.group_code != '' and t.group_parent_id = #{department}
     </select>

    <select id="selectOptionsByDepartment" parameterType="String"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
        t.group_name label,
        t.id value
        from sys_group t
        WHERE t.group_code != ''
    </select>
     <select id="selectOptionsByMpPdaCalcRls" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
         select
             t.process_name2  label,
             t.id  value
         from standard_process_management   t
         WHERE t.step_number != '' and t.department =#{department}
     </select>
     <select id="selectOptionsByTotalNoncompliantCount" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
         select
             t.process_name2  label,
             t.id  value
         from standard_process_management   t
         WHERE t.step_number != '' and t.department =#{department}
     </select>

    <select id="selectStandardProcessManagementList" parameterType="com.huatek.frame.modules.business.service.dto.StandardProcessManagementDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.StandardProcessManagementVO">
		select
        <include refid="Base_Column_List" />,sg.group_name as department,s.group_name as testingTeam,w.workstation_name as workstation,
        ( SELECT GROUP_CONCAT(spm.process_name2 SEPARATOR ', ') FROM standard_process_management spm WHERE FIND_IN_SET(spm.id, t.mp_pda_calc_rls)) AS mpPdaCalcRls,
        ( SELECT spm.process_name2 FROM standard_process_management spm WHERE spm.id = t.total_noncompliant_count LIMIT 1 ) AS totalNoncompliantCount
        from standard_process_management t left join sys_group sg on t.department  = sg.id
        left join sys_group s on t.testing_team  = s.id left join workstation w on t.workstation =w.id
            <where>
                and 1=1
                <if test="stepNumber != null and stepNumber != ''">
                    and t.step_number  like concat('%', #{stepNumber} ,'%')
                </if>
                <if test="processName2 != null and processName2 != ''">
                    and t.process_name2  like concat('%', #{processName2} ,'%')
                </if>
                <if test="processClassification != null and processClassification != ''">
                    and t.process_classification  = #{processClassification}
                </if>
                <if test="processDescription != null and processDescription != ''">
                    and t.process_description  like concat('%', #{processDescription} ,'%')
                </if>
                <if test="workstation != null and workstation != ''">
                    and w.id  = #{workstation}
                </if>
                <if test="testingTeam != null and testingTeam != ''">
                    and s.id  = #{testingTeam}
                </if>
                <if test="thresholdOfPda != null and thresholdOfPda != ''">
                    and t.threshold_of_pda  like concat('%', #{thresholdOfPda} ,'%')
                </if>
                <if test="isApproval != null and isApproval != ''">
                    and t.is_approval  = #{isApproval}
                </if>
                <if test="testingTimes != null and testingTimes != ''">
                    and t.testing_times  = #{testingTimes}
                </if>
                <if test="durationOfTesting != null and durationOfTesting != ''">
                    and t.duration_of_testing  = #{durationOfTesting}
                </if>
                <if test="mpPdaCalcRls != null and mpPdaCalcRls != ''">
                    and t.mp_pda_calc_rls  = #{mpPdaCalcRls}
                </if>
                <if test="totalNoncompliantCount != null and totalNoncompliantCount != ''">
                    and t.total_noncompliant_count  = #{totalNoncompliantCount}
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  like concat('%', #{comment} ,'%')
                </if>
                <if test="department != null and department != ''">
                    and sg.id  like concat('%', #{department} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.CODEX_TORCH_CREATOR_ID  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.CODEX_TORCH_UPDATER  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.CODEX_TORCH_GROUP_ID  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.CODEX_TORCH_CREATE_DATETIME  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.CODEX_TORCH_UPDATE_DATETIME  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.CODEX_TORCH_DELETED  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectStandardProcessManagementListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.StandardProcessManagementVO">
		select
		<include refid="Base_Column_List" />,sg.group_name as department,s.group_name as testingTeam,w.workstation_name as workstation,
        (select process_name2 from standard_process_management where mp_pda_calc_rls = t.id) as mpPdaCalcRls,
        (select process_name2 from standard_process_management where total_noncompliant_count = t.id) as totalNoncompliantCount
        from standard_process_management t left join sys_group sg on t.department  = sg.id
        left join sys_group s on t.testing_team  = s.id left join workstation w on t.workstation =w.id
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>

</mapper>