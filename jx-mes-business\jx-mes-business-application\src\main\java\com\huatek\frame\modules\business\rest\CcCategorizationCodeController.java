package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.modules.business.domain.vo.CcCategorizationCodeVO;
import com.huatek.frame.modules.business.service.CcCategorizationCodeService;
import com.huatek.frame.modules.business.service.dto.CcCategorizationCodeDTO;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-08-22
**/
@Api(tags = "客户分类对应关系管理")
@RestController
@RequestMapping("/api/ccCategorizationCode")
public class CcCategorizationCodeController {

	@Autowired
    private CcCategorizationCodeService ccCategorizationCodeService;

	/**
	 * 客户分类对应关系列表
	 * 
	 * @param dto 客户分类对应关系DTO 实体对象
	 * @return
	 */
    @Log("客户分类对应关系列表")
    @ApiOperation(value = "客户分类对应关系列表查询")
    @PostMapping(value = "/ccCategorizationCodeList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("ccCategorizationCode:list")
    public TorchResponse<List<CcCategorizationCodeVO>> query(@RequestBody CcCategorizationCodeDTO dto){
        return ccCategorizationCodeService.findCcCategorizationCodePage(dto);
    }

	/**
	 * 新增/修改客户分类对应关系
	 * 
	 * @param ccCategorizationCodeDto 客户分类对应关系DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改客户分类对应关系")
    @ApiOperation(value = "客户分类对应关系新增/修改操作")
    @PostMapping(value = "/ccCategorizationCode", produces = { "application/json;charset=utf-8" })
    @TorchPerm("ccCategorizationCode:add#ccCategorizationCode:edit")
    public TorchResponse add(@RequestBody CcCategorizationCodeDTO ccCategorizationCodeDto) throws Exception {
		// BeanValidatorFactory.validate(ccCategorizationCodeDto);
		return ccCategorizationCodeService.saveOrUpdate(ccCategorizationCodeDto);
	}

	/**
	 * 查询客户分类对应关系详情
	 * 
	 * @param id 主键id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("客户分类对应关系详情")
    @ApiOperation(value = "客户分类对应关系详情查询")
    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("ccCategorizationCode:detail")
	public TorchResponse detail(@PathVariable(value = "id") String id) {
		return ccCategorizationCodeService.findCcCategorizationCode(id);
	}

	/**
	 * 删除客户分类对应关系
	 * 
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
    @Log("删除客户分类对应关系")
    @ApiOperation(value = "客户分类对应关系删除操作")
    @TorchPerm("ccCategorizationCode:del")
    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	public TorchResponse delete(@RequestBody String[] ids) {
		return ccCategorizationCodeService.delete(ids);
	}

    @ApiOperation(value = "客户分类对应关系联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return ccCategorizationCodeService.getOptionsList(id);
	}





    @Log("客户分类对应关系导出")
    @ApiOperation(value = "客户分类对应关系导出")
    @TorchPerm("ccCategorizationCode:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody CcCategorizationCodeDTO dto)
    {
        List<CcCategorizationCodeVO> list = ccCategorizationCodeService.selectCcCategorizationCodeList(dto);
        ExcelUtil<CcCategorizationCodeVO> util = new ExcelUtil<CcCategorizationCodeVO>(CcCategorizationCodeVO.class);
        util.exportExcel(response, list, "客户分类对应关系数据");
    }

    @Log("客户分类对应关系导入")
    @ApiOperation(value = "客户分类对应关系导入")
    @TorchPerm("ccCategorizationCode:import")
    @PostMapping("/importData")
    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
    {
        ExcelUtil<CcCategorizationCodeVO> util = new ExcelUtil<CcCategorizationCodeVO>(CcCategorizationCodeVO.class);
        List<CcCategorizationCodeVO> list = util.importExcel(file.getInputStream());
        return ccCategorizationCodeService.importCcCategorizationCode(list, unionColumns, true, "");
    }

    @Log("客户分类对应关系导入模板")
    @ApiOperation(value = "客户分类对应关系导入模板下载")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<CcCategorizationCodeVO> util = new ExcelUtil<CcCategorizationCodeVO>(CcCategorizationCodeVO.class);
        util.importTemplateExcel(response, "客户分类对应关系数据");
    }

    @Log("根据Ids获取客户分类对应关系列表")
    @ApiOperation(value = "客户分类对应关系 根据Ids批量查询")
    @PostMapping(value = "/ccCategorizationCodeList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getCcCategorizationCodeListByIds(@RequestBody List<String> ids) {
        return ccCategorizationCodeService.selectCcCategorizationCodeListByIds(ids);
    }




}