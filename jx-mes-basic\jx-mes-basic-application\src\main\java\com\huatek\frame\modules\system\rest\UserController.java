package com.huatek.frame.modules.system.rest;

import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.desensitized.TorchEnableSensitive;
import com.huatek.frame.common.annotation.factory.BeanValidatorFactory;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.annotation.secret.TorchSecret;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.modules.system.domain.SysUser;
import com.huatek.frame.modules.system.domain.vo.SysGroupVO;
import com.huatek.frame.modules.system.domain.vo.SysUserVO;
import com.huatek.frame.modules.system.service.SysGroupService;
import com.huatek.frame.modules.system.service.SysUserService;
import com.huatek.frame.modules.system.service.dto.DataRuleDTO;
import com.huatek.frame.modules.system.service.dto.SysUserDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 用户管理
 * 
 * <AUTHOR>
 *
 */
@Api(tags = "用户管理")
@RestController
@RequestMapping("/api/user")
public class UserController {

	@Autowired
	private SysUserService sysUserService;

	@Autowired
	private SysGroupService sysGroupService;

	/**
	 * 用户列表
	 * 
	 * @param user
	 * @return
	 */
	@Log(value = "用户列表", enableLogAudit = false)
	@ApiOperation(value = "用户列表分页查询")
	@PostMapping(value = "/users", produces = { "application/json;charset=utf-8" })
	@TorchPerm("user:list")
	@TorchEnableSensitive
	public  ResponseEntity<TorchResponse<List<SysUser>>> users(@RequestBody SysUserDTO user) {
		return new ResponseEntity<TorchResponse<List<SysUser>>>(sysUserService.findUserPage(user), HttpStatus.OK);
	}

	@Log(value = "查询所有用户", enableLogAudit = false)
	@ApiOperation(value = "查询所有用户")
	@GetMapping(value = "/listAll", produces = { "application/json;charset=utf-8" })
	public TorchResponse<List<SysUserVO>> findAllUsers(){
		return sysUserService.findAllUsers();
	}
	


	/**
	 * 新增用户
	 * 
	 * @param sysUser
	 * @return
	 *
	 * @throws Exception 
	 */
	@SuppressWarnings("rawtypes")
	@Log(value = "新增、修改用户")
	@ApiOperation(value = "新增修改")
	@PostMapping(value = "/user", produces = { "application/json;charset=utf-8" })
	@TorchPerm("user:add#user:edit")
	@TorchSecret(reqPropsNames = {"userMobile", "userEmail"}, respPropsNames = {})
	public ResponseEntity addUser(@RequestBody SysUser sysUser) throws Exception {
		BeanValidatorFactory.validate(sysUser);
		return new ResponseEntity(sysUserService.saveOrUpdate(sysUser), HttpStatus.OK);
	}

	public TorchResponse test(){
		return sysUserService.test();
	}

	/**
	 * 查询
	 *
	 * @param id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Log(value = "用户详情", enableLogAudit = false)
	@ApiOperation(value = "用户详情")
	@GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
	@TorchPerm("user:detail")
	@TorchSecret(reqPropsNames = {}, respPropsNames = {"userMobile", "userEmail"})
	@TorchEnableSensitive(enable = false)
	public ResponseEntity findUser(@PathVariable(value = "id") String id) {
		return new ResponseEntity(sysUserService.findUser(id), HttpStatus.OK);
	}

	/**
	 * 删除用户
	 * 
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	@Log("删除用户")
	@ApiOperation(value = "删除用户")
	@PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	@TorchPerm("user:del")
	public ResponseEntity delete(@RequestBody String[] ids) {
		sysUserService.delete(ids);
		return new ResponseEntity(HttpStatus.OK);
	}

	/**
	 * 更新用户角色关系
	 * 
	 * @param uid
	 * @param dtos
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	@Log("更新用户角色关系")
	@ApiOperation(value = "角色绑定")
	@PostMapping(value = "/updateUserRole/{uid}", produces = { "application/json;charset=utf-8" })
	@TorchPerm("user:update")
	public ResponseEntity updateUserRole(@PathVariable("uid") String uid, @RequestBody List<DataRuleDTO> dtos) {
		sysUserService.updateUserRole(uid, dtos);
		return new ResponseEntity(HttpStatus.OK);
	}

	@Log("重置密码")
	@ApiOperation("重置密码")
	@PostMapping(value = "/resetPwd", produces = { "application/json;charset=utf-8" })
	@TorchPerm("user:resetPwd")
	public ResponseEntity<TorchResponse<?>> resetPwd(@RequestParam("userId") String userId) {
		return new ResponseEntity(sysUserService.resetPwd(userId),HttpStatus.OK);
	}


	/**
	 * 用户中心；用户资料
	 *
	 * @param token
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	@Log(value = "用户资料", enableLogAudit = false)
	@ApiOperation(value = "用户资料")
	@GetMapping(value = "/profile", produces = { "application/json;charset=utf-8" })
	@TorchSecret(reqPropsNames = {}, respPropsNames = {"userMobile", "userEmail"})
	@TorchEnableSensitive(enable = false)
	public ResponseEntity profile(@RequestHeader(value = Constant.TOKEN) String token) {
		return new ResponseEntity(sysUserService.getProfile(token), HttpStatus.OK);
	}

	@Log(value = "更新用户资料")
	@ApiOperation(value = "更新用户资料")
	@PostMapping(value = "/updateProfile", produces = { "application/json;charset=utf-8" })
	@TorchSecret(reqPropsNames = {"userMobile", "userEmail"}, respPropsNames = {})
	public ResponseEntity updateProfile(@RequestBody SysUser sysUser) {
		return new ResponseEntity(sysUserService.updateProfile(sysUser), HttpStatus.OK);
	}

	@Log(value = "更新用户头像", enableLogAudit = false)
	@ApiOperation(value = "更新用户头像")
	@PostMapping(value = "/avatar", produces = { "application/json;charset=utf-8" })
	public ResponseEntity avatar(@RequestParam("avatarfile") MultipartFile file, @RequestHeader(value = Constant.TOKEN) String token) {
		return new ResponseEntity(sysUserService.avatar(file, token), HttpStatus.OK);
	}

	@Log("系统用户导出")
	@TorchPerm("user:export")
	@PostMapping("/export")
	public void export(HttpServletResponse response, @RequestBody SysUserDTO dto) {
		List<SysUserVO> list = sysUserService.selectUserList(dto);
		ExcelUtil<SysUserVO> util = new ExcelUtil<SysUserVO>(SysUserVO.class);
		util.exportExcel(response, list, "系统用户数据");
	}

	@Log("系统用户导入")
	@TorchPerm("user:import")
	@PostMapping("/importData")
	public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception {
		ExcelUtil<SysUserVO> util = new ExcelUtil<SysUserVO>(SysUserVO.class);
		List<SysUserVO> list = util.importExcel(file.getInputStream());
		return sysUserService.importUser(list, unionColumns, true, "");
	}

	@Log("系统用户导入模板")
	@PostMapping("/importTemplate")
	public void importTemplate(HttpServletResponse response) throws IOException {
		ExcelUtil<SysUserVO> util = new ExcelUtil<SysUserVO>(SysUserVO.class);
		util.importTemplateExcel(response, "系统用户数据");
	}

	@Log("获取用户部门")
	@GetMapping("/groupId")
	public TorchResponse getGroupId() {
		String groupId = sysUserService.getCurrentUserGroupId();
		TorchResponse<SysGroupVO> response = sysGroupService.findGroup(groupId);
		return response;
	}

	@Log("根据用户id获取用户部门")
	@GetMapping("/findDepartById/{userId}")
	public TorchResponse<SysGroupVO> findDepartById(@PathVariable(value = "userId") String userId) {
		return sysUserService.findDepartById(userId);

	}


	/**
	 * 根据部门获取该部门下所有用户
	 * @param requestParam 部门ID
	 * @return
	 */
	@Log("根据部门ID获取用户列表")
	@GetMapping("/findUsers")
	public TorchResponse getUsersByDepart(@RequestParam String groupId){
		List<SysUserVO> userList = sysUserService.getUsersByDepart(groupId);
		TorchResponse response = new TorchResponse();
		response.getData().setData(userList);
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setCount((long)userList.size());
		return response;
	}
}
