package com.huatek.frame.modules.business.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.huatek.frame.modules.business.domain.EvaluationOrder;
import com.huatek.frame.modules.business.domain.vo.EvaluationOrderQueryRespVO;
import com.huatek.frame.modules.business.service.dto.EvaluationOrderDTO;
import com.huatek.frame.modules.business.domain.vo.EvaluationOrderVO;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.business.service.dto.EvaluationOrderReviewDTO;

import java.util.List;

import java.util.Map;

/**
* @description 测评订单Service
* <AUTHOR>
* @date 2025-07-30
**/
public interface EvaluationOrderService extends IService<EvaluationOrder> {
    
    /**
	 * 分页查找查找 测评订单
	 * 
	 * @param dto 测评订单dto实体对象
	 * @return 
	 */
	TorchResponse<List<EvaluationOrderVO>> findEvaluationOrderPage(EvaluationOrderDTO dto);

    /**
	 * 添加 \修改 测评订单
	 * 
	 * @param evaluationOrderDto 测评订单dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(EvaluationOrderDTO evaluationOrderDto);
	
	/**
	 * 通过id查找测评订单
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<EvaluationOrderVO> findEvaluationOrder(String id);
	
	/**
	 * 删除 测评订单
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 查找关联信息 测评订单
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<EvaluationOrderVO>> getOptionsList(String id);




    /**
     * 联动数据查询
     *
     * @param linkageDataTableName
     * @param conditionalValue
     * @return
     */
    TorchResponse getLinkageData(String linkageDataTableName, String conditionalValue);

    Map<String,String> selectDataLinkageByEntrustedUnit(String customer_id0);
    /**
     * 根据条件查询测评订单列表
     *
     * @param dto 测评订单信息
     * @return 测评订单集合信息
     */
    List<EvaluationOrderVO> selectEvaluationOrderList(EvaluationOrderDTO dto);

    /**
     * 导入测评订单数据
     *
     * @param evaluationOrderList 测评订单数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    TorchResponse importEvaluationOrder(List<EvaluationOrderVO> evaluationOrderList, List<String> unionColumns, Boolean isUpdateSupport, String operName);

	/**
	 * 根据IDS获取测评订单数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectEvaluationOrderListByIds(List<String> ids);

    /**
     * 测评订单主子表单组合提交
     *
	 * @param evaluationOrderDto 测评订单DTO实体对象
     * @return
     */
    TorchResponse submitMasterDetails(EvaluationOrderDTO evaluationOrderDto);


	/**
	 * 计算交付率
	 * @return
	 */
    TorchResponse onTimeDeliveryRate();

}