package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.modules.business.domain.BaseEntity;
import com.huatek.frame.common.utils.HuatekTools;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import java.sql.Timestamp;
import java.sql.Date;
import java.io.Serializable;

/**
* @description 工作站DTO 实体类
* <AUTHOR>
* @date 2025-07-17
**/
@Data
@ApiModel("工作站DTO实体类")
public class WorkstationDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;
    
    /**
	 * 工作站编号
     **/
    @ApiModelProperty("工作站编号")
    private String workstationNumber;
    
    /**
	 * 工作站名称
     **/
    @ApiModelProperty("工作站名称")
    private String workstationName;
    
    /**
	 * 扫码枪编号
     **/
    @ApiModelProperty("扫码枪编号")
    private String scannerGunNumber;
    
    /**
	 * 扫码枪SN码
     **/
    @ApiModelProperty("扫码枪SN码")
    private String scannerGunSnCode;
    
    /**
	 * 所属部门
     **/
    @ApiModelProperty("所属部门")
    private String department;

    /**
     * 所属部门编码
     **/
    @ApiModelProperty("所属部门编码")
    private String groupCode;

    /**
     * 所属部门Id
     **/
    @ApiModelProperty("所属部门Id")
    private String groupId;
    
    /**
	 * 状态
     **/
    @ApiModelProperty("状态")
    private String status;

    
    /**
	 * 备注
     **/
    @ApiModelProperty("备注")
    private String comment;
    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;



	/**
	 * 页码
	 */
	@ApiModelProperty("当前页码")
	private Integer page;
	
	/**
	 * 每页显示数量
	 */
	@ApiModelProperty("每页显示大小")
	private Integer limit;

}