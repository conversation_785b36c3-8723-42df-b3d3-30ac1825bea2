package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;
import com.huatek.frame.modules.business.domain.StandardSpecification;
import  com.huatek.frame.modules.business.domain.vo.StandardSpecificationVO;
import com.huatek.frame.modules.business.service.dto.StandardSpecificationDTO;

import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;
/**
* 标准规范mapper
* <AUTHOR>
* @date 2025-07-21
**/
public interface StandardSpecificationMapper extends BaseMapper<StandardSpecification> {

     /**
	 * 标准规范分页
	 * @param dto
	 * @return
	 */
	Page<StandardSpecificationVO> selectStandardSpecificationPage(StandardSpecificationDTO dto);


    /**
     * 根据条件查询标准规范列表
     *
     * @param dto 标准规范信息
     * @return 标准规范集合信息
     */
    List<StandardSpecificationVO> selectStandardSpecificationList(StandardSpecificationDTO dto);

	/**
	 * 根据IDS查询标准规范列表
	 * @param ids
	 * @return
	 */
    List<StandardSpecificationVO> selectStandardSpecificationListByIds(@Param("ids") List<String> ids);

	/**
	 * 外键关联表: customer_information_management - specification_number
	 **/
	@ApiModelProperty("外键 customer_information_management - entrusted_unit")
	Page<SelectOptionsVO> selectOptionsByEntrustedUnit(String entrusted_unit);
}