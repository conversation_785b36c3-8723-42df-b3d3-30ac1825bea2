package com.huatek.frame.modules.business.service.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.huatek.frame.modules.system.domain.BaseEntity;
import com.huatek.frame.common.utils.HuatekTools;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import java.sql.Timestamp;
import java.sql.Date;
import java.math.BigDecimal;
import java.io.Serializable;

/**
* @description 待制工单DTO 实体类
* <AUTHOR>
* @date 2025-07-30
**/
@Data
@ApiModel("待制工单DTO实体类")
public class ProductionOrderDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    private String[] orderIds;
    
    /**
	 * 工单编号
     **/
    @ApiModelProperty("工单编号")
    private String workOrderNumber;
    
    /**
	 * 订单编号
     **/
    @ApiModelProperty("订单编号")
    private String orderNumber;
    
    /**
	 * 委托单位
     **/
    @ApiModelProperty("委托单位")
    private String entrustedUnit;
    
    /**
	 * 工程代码
     **/
    @ApiModelProperty("工程代码")
    private String engineeringCode;
    
    /**
	 * 订单备注
     **/
    @ApiModelProperty("订单备注")
    private String orderRemarks;
    
    /**
	 * 报告需求
     **/
    @ApiModelProperty("报告需求")
    private String reportRequirements;
    
    /**
	 * 报告形式
     **/
    @ApiModelProperty("报告形式")
    private String reportFormat;
    
    /**
	 * 电子版报告数据要求
     **/
    @ApiModelProperty("电子版报告数据要求")
    private String dataReqERep;
    
    /**
	 * 纸质版报告数据要求
     **/
    @ApiModelProperty("纸质版报告数据要求")
    private String dataReqsPapereport;
    
    /**
	 * 产品型号
     **/
    @ApiModelProperty("产品型号")
    private String productModel;
    
    /**
	 * 产品名称
     **/
    @ApiModelProperty("产品名称")
    private String productName;
    
    /**
	 * 生产批次
     **/
    @ApiModelProperty("生产批次")
    private String productionBatch;
    
    /**
	 * 生产厂家
     **/
    @ApiModelProperty("生产厂家")
    private String manufacturer;
    /**
     * 产品分类
     **/
    @ApiModelProperty("产品分类")
    private String productCategory;
    
    /**
	 * 送检数量
     **/
    @ApiModelProperty("送检数量")
    private Integer inspectionQuantity2;
    
    /**
	 * 样本总数
     **/
    @ApiModelProperty("样本总数")
    private Integer sampleTotalCount;
    
    /**
	 * 任务等级
     **/
    @ApiModelProperty("任务等级")
    private String taskLevel;
    
    /**
	 * 标准规范号
     **/
    @ApiModelProperty("标准规范号")
    private String standardSpecificationNumber;
    
    /**
	 * 试验项目
     **/
    @ApiModelProperty("试验项目")
    private String experimentProject;
    
    /**
	 * 工单送检编号
     **/
    @ApiModelProperty("工单送检编号")
    private String workOrderInspectionNumber1;
    
    /**
	 * 封装形式
     **/
    @ApiModelProperty("封装形式")
    private String packageForm;
    
    /**
	 * 质量等级
     **/
    @ApiModelProperty("质量等级")
    private String qualityGrade;
    
    /**
	 * 试验类型
     **/
    @ApiModelProperty("试验类型")
    private String testType;
    
    /**
	 * 要求完成日期
     **/
    @ApiModelProperty("要求完成日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date deadline;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date deadlineStart;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date deadlineEnd;
    /**
	 * 试验方式
     **/
    @ApiModelProperty("试验方式")
    private String testMethodology;
    
    /**
	 * 前置工单
     **/
    @ApiModelProperty("前置工单")
    private String predecessorWorkOrder;
    
    /**
	 * 关联工单
     **/
    @ApiModelProperty("关联工单")
    private String relatedWorkOrder;
    
    /**
	 * PDA
     **/
    @ApiModelProperty("PDA")
    private BigDecimal pda;
    
    /**
	 * 是否加入排产
     **/
    @ApiModelProperty("是否加入排产")
    private String whetherToIncludeInScheduling;
    
    /**
	 * 预计完成时间
     **/
    @ApiModelProperty("预计完成时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date estimatedCompletionTime;
    
    /**
	 * 数量
     **/
    @ApiModelProperty("数量")
    private Integer quantity;

    /**
     * 原始工单数量
     **/
    @ApiModelProperty("原始工单数量")
    private Integer oldQquantity;
    /**
     * 新工单数量
     **/
    @ApiModelProperty("数量")
    private Integer[] splitQuantity;
    
    /**
	 * 附件
     **/
    @ApiModelProperty("附件")
    private String attachment;
    
    /**
	 * 工单状态
     **/
    @ApiModelProperty("工单状态")
    private String workOrderStatus;

    /**
     * 报告状态
     **/
    @ApiModelProperty("报告状态")
    private String reportStatus;

    /**
     * 工单报告类型
     **/
    @ApiModelProperty("工单报告类型")
    private String reportType;
    /**
	 * 负责人
     **/
    @ApiModelProperty("负责人")
    private String responsiblePerson;
    /**
     * 制单人
     **/
    @ApiModelProperty("制单人")
    private String preparedBy;
    
    /**
	 * 生产阶段
     **/
    @ApiModelProperty("生产阶段")
    private String productionStage;
    
    /**
	 * 是否同型同批二次检测
     **/
    @ApiModelProperty("是否同型同批二次检测")
    private String wtstabr;
    
    /**
	 * 是否入器件
     **/
    @ApiModelProperty("是否入器件")
    private String whetherToEnterComponents;
    
    /**
	 * 是否入资料
     **/
    @ApiModelProperty("是否入资料")
    private String whetherToEnterDocuments;
    
    /**
	 * 实际完成时间
     **/
    @ApiModelProperty("实际完成时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp completionTime;
    
    /**
	 * 不合格率预警标记
     **/
    @ApiModelProperty("不合格率预警标记")
    private String wmfcnr;
    
    /**
	 * 不可筛原因
     **/
    @ApiModelProperty("不可筛原因")
    private String irretrievableReason;

    /**
     * 不可老化原因
     **/
    @ApiModelProperty("不可老化原因")
    private String nonAgingReason;

    /**
     * 申请人
     **/
    @ApiModelProperty("申请人")
    private String codexTorchApplicant;

    /**
     * 待审批人
     **/
    @ApiModelProperty("待审批人")
    private String codexTorchApprover;

    /**
     * 待审批人列表
     **/
    @ApiModelProperty("待审批人列表")
    private String codexTorchApprovers;

    /**
     * 流程状态
     **/
    @ApiModelProperty("流程状态")
    private String codexTorchApprovalStatus;

    /**
     * 外协状态
     **/
    @ApiModelProperty("外协状态")
    private String outSourcingStatus;
    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


    /**
     * 工作流查询角色
     **/
    @ApiModelProperty("工作流查询角色")
    private String workflowQueryRole;

    /**
     * 复制类型
     **/
    @ApiModelProperty("复制类型")
    private String copyType;


	/**
	 * 页码
	 */
	@ApiModelProperty("当前页码")
	private Integer page;
	
	/**
	 * 每页显示数量
	 */
	@ApiModelProperty("每页显示大小")
	private Integer limit;
    /**
     * 菜单查询类型
     */
    @ApiModelProperty("菜单查询类型")
    private String menuType;

}