package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.sql.Date;

import lombok.Getter;
import lombok.Setter;

/**
* @description 工作站
* <AUTHOR>
* @date 2025-07-17
**/
@Setter
@Getter
@TableName("workstation")
public class Workstation implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    
    /**
	 * 工作站编号
     **/
    @TableField(value = "workstation_number"
    )
    private String workstationNumber;

    
    /**
	 * 工作站名称
     **/
    @TableField(value = "workstation_name"
    )
    private String workstationName;

    
    /**
	 * 扫码枪编号
     **/
    @TableField(value = "scanner_gun_number"
    )
    private String scannerGunNumber;

    
    /**
	 * 扫码枪SN码
     **/
    @TableField(value = "scanner_gun_sn_code"
    )
    private String scannerGunSnCode;

    
    /**
	 * 所属部门编码
     **/
    @TableField(value = "group_code"
    )
    private String groupCode;

    /**
     * 所属部门名称
     **/
    @TableField(value = "department"
    )
    private String department;

    /**
     * 所属部门id
     **/
    @TableField(value = "group_id"
    )
    private String groupId;
    
    /**
	 * 状态
     **/
    @TableField(value = "status"
    )
    private String status;


    
    /**
	 * 备注
     **/
    @TableField(value = "`comment`"
    )
    private String comment;

    
    /**
	 * 创建人
     **/
    @TableField(value = "CODEX_TORCH_CREATOR_ID"
    )
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @TableField(value = "CODEX_TORCH_UPDATER"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;

    
    /**
	 * 所属组织
     **/
    @TableField(value = "CODEX_TORCH_GROUP_ID"
    )
    private String codexTorchGroupId;

    
    /**
	 * 创建时间
     **/
    @TableField(value = "CODEX_TORCH_CREATE_DATETIME"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "CODEX_TORCH_UPDATE_DATETIME"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @TableField(value = "CODEX_TORCH_DELETED"
    )
    private String codexTorchDeleted;
}