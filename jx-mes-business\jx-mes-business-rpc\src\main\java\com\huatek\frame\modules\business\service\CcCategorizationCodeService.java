package com.huatek.frame.modules.business.service;

import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.business.domain.vo.CcCategorizationCodeVO;
import com.huatek.frame.modules.business.service.dto.CcCategorizationCodeDTO;

import java.util.List;


/**
* @description 客户分类对应关系Service
* <AUTHOR>
* @date 2025-08-22
**/
public interface CcCategorizationCodeService {
    
    /**
	 * 分页查找查找 客户分类对应关系
	 * 
	 * @param dto 客户分类对应关系dto实体对象
	 * @return 
	 */
	TorchResponse<List<CcCategorizationCodeVO>> findCcCategorizationCodePage(CcCategorizationCodeDTO dto);

    /**
	 * 添加 \修改 客户分类对应关系
	 * 
	 * @param ccCategorizationCodeDto 客户分类对应关系dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(CcCategorizationCodeDTO ccCategorizationCodeDto);
	
	/**
	 * 通过id查找客户分类对应关系
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<CcCategorizationCodeVO> findCcCategorizationCode(String id);
	
	/**
	 * 删除 客户分类对应关系
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 查找关联信息 客户分类对应关系
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<CcCategorizationCodeVO>> getOptionsList(String id);




    /**
     * 根据条件查询客户分类对应关系列表
     *
     * @param dto 客户分类对应关系信息
     * @return 客户分类对应关系集合信息
     */
    List<CcCategorizationCodeVO> selectCcCategorizationCodeList(CcCategorizationCodeDTO dto);

    /**
     * 导入客户分类对应关系数据
     *
     * @param ccCategorizationCodeList 客户分类对应关系数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    TorchResponse importCcCategorizationCode(List<CcCategorizationCodeVO> ccCategorizationCodeList, List<String> unionColumns, Boolean isUpdateSupport, String operName);

	/**
	 * 根据IDS获取客户分类对应关系数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectCcCategorizationCodeListByIds(List<String> ids);



}