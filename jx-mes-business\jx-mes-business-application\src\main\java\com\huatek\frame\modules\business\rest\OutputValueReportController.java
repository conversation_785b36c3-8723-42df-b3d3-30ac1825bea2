package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.utils.Constant;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.modules.business.domain.OutputValueReport;
import com.huatek.frame.modules.business.service.OutputValueReportService;
import com.huatek.frame.modules.business.service.dto.OutputValueReportDTO;
import com.huatek.frame.modules.business.domain.vo.OutputValueReportVO;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-08-28
**/
@Api(tags = "产值报表管理")
@RestController
@RequestMapping("/api/outputValueReport")
public class OutputValueReportController {

	@Autowired
    private OutputValueReportService outputValueReportService;



	/**
	 * 产值报表列表
	 * 
	 * @param dto 产值报表DTO 实体对象
	 * @return
	 */
    @Log("产值报表列表")
    @ApiOperation(value = "产值报表列表查询")
    @PostMapping(value = "/outputValueReportList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("outputValueReport:list")
    public TorchResponse<List<OutputValueReportVO>> query(@RequestBody OutputValueReportDTO dto){
        return outputValueReportService.findOutputValueReportPage(dto);
    }
//
//	/**
//	 * 新增/修改产值报表
//	 *
//	 * @param outputValueReportDto 产值报表DTO实体对象
//	 * @return
//	 * @throws Exception
//	 */
//    @SuppressWarnings("rawtypes")
//    @Log("新增/修改产值报表")
//    @ApiOperation(value = "产值报表新增/修改操作")
//    @PostMapping(value = "/outputValueReport", produces = { "application/json;charset=utf-8" })
//    @TorchPerm("outputValueReport:add#outputValueReport:edit")
//    public TorchResponse add(@RequestBody OutputValueReportDTO outputValueReportDto) throws Exception {
//		// BeanValidatorFactory.validate(outputValueReportDto);
//		return outputValueReportService.saveOrUpdate(outputValueReportDto);
//	}
//
//	/**
//	 * 查询产值报表详情
//	 *
//	 * @param id 主键id
//	 * @return
//	 */
//	@SuppressWarnings({ "rawtypes", "unchecked" })
//    @Log("产值报表详情")
//    @ApiOperation(value = "产值报表详情查询")
//    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
//    @TorchPerm("outputValueReport:detail")
//	public TorchResponse detail(@PathVariable(value = "id") String id) {
//		return outputValueReportService.findOutputValueReport(id);
//	}
//
//	/**
//	 * 删除产值报表
//	 *
//	 * @param ids
//	 * @return
//	 */
//	@SuppressWarnings("rawtypes")
//    @Log("删除产值报表")
//    @ApiOperation(value = "产值报表删除操作")
//    @TorchPerm("outputValueReport:del")
//    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
//	public TorchResponse delete(@RequestBody String[] ids) {
//		return outputValueReportService.delete(ids);
//	}
//
//    @ApiOperation(value = "产值报表联动选项值查询")
//    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
//	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
//		return outputValueReportService.getOptionsList(id);
//	}
//
//
//


    @Log("产值报表导出")
    @ApiOperation(value = "产值报表导出")
    @TorchPerm("outputValueReport:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody OutputValueReportDTO dto)
    {
        List<OutputValueReportVO> list = outputValueReportService.selectOutputValueReportList(dto);
        ExcelUtil<OutputValueReportVO> util = new ExcelUtil<OutputValueReportVO>(OutputValueReportVO.class);
        util.exportExcel(response, list, "产值报表数据");
    }
//
//    @Log("产值报表导入")
//    @ApiOperation(value = "产值报表导入")
//    @TorchPerm("outputValueReport:import")
//    @PostMapping("/importData")
//    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
//    {
//        ExcelUtil<OutputValueReportVO> util = new ExcelUtil<OutputValueReportVO>(OutputValueReportVO.class);
//        List<OutputValueReportVO> list = util.importExcel(file.getInputStream());
//        return outputValueReportService.importOutputValueReport(list, unionColumns, true, "");
//    }
//
//    @Log("产值报表导入模板")
//    @ApiOperation(value = "产值报表导入模板下载")
//    @PostMapping("/importTemplate")
//    public void importTemplate(HttpServletResponse response) throws IOException
//    {
//        ExcelUtil<OutputValueReportVO> util = new ExcelUtil<OutputValueReportVO>(OutputValueReportVO.class);
//        util.importTemplateExcel(response, "产值报表数据");
//    }
//
//    @Log("根据Ids获取产值报表列表")
//    @ApiOperation(value = "产值报表 根据Ids批量查询")
//    @PostMapping(value = "/outputValueReportList/ids", produces = {"application/json;charset=utf-8"})
//    public TorchResponse getOutputValueReportListByIds(@RequestBody List<String> ids) {
//        return outputValueReportService.selectOutputValueReportListByIds(ids);
//    }
    /**
     * 生成产值报表
     *
     * @return
     * @throws Exception
     */
    @SuppressWarnings("rawtypes")
    @Log("生成产值报表")
    @ApiOperation(value = "生成产值报表")
    @PostMapping(value = "/generateOutputVal", produces = { "application/json;charset=utf-8" })
    public TorchResponse generateOutputVal() throws Exception {
        return outputValueReportService.generateOutputVal();
    }
    /**
     * 获取首页看板统计数据
     * @return 统计数据
     */
    @Log("获取首页看板统计数据")
    @ApiOperation(value = "获取首页看板统计数据")
    @GetMapping(value = "/dashboard", produces = { "application/json;charset=utf-8" })
//    @TorchPerm("outputValueReport:dashboard")
    public TorchResponse getDashboardStats() {
        return outputValueReportService.getDashboardStats();
    }


}