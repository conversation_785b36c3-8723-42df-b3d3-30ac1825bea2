package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;

import com.huatek.frame.modules.business.domain.ProductionTask;
import com.huatek.frame.modules.business.domain.vo.ProductionTaskVO;
import com.huatek.frame.modules.business.domain.vo.ProductionTaskViewVO;
import com.huatek.frame.modules.business.domain.vo.UnqualifiedProcessVO;
import com.huatek.frame.modules.business.service.dto.ProductionTaskDTO;
import org.apache.ibatis.annotations.Param;
import java.util.Map;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

/**
* 生产任务mapper
* <AUTHOR>
* @date 2025-08-11
**/
public interface ProductionTaskMapper extends BaseMapper<ProductionTask> {

     /**
	 * 生产任务分页
	 * @param dto
	 * @return
	 */
	Page<ProductionTaskVO> selectProductionTaskPage(ProductionTaskDTO dto);

    /**
	 * 外键关联表: capability_asset - capability_number
     **/
    @ApiModelProperty("外键 capability_asset - capability_number")
	Page<SelectOptionsVO> selectOptionsByTechnicalCompetencyNumber(String technicalCompetencyNumber);
    Map<String,String> selectDataLinkageByTechnicalCompetencyNumber(@Param("capability_number") String capability_number);

    /**
     * 根据条件查询生产任务列表
     *
     * @param dto 生产任务信息
     * @return 生产任务集合信息
     */
    List<ProductionTaskVO> selectProductionTaskList(ProductionTaskDTO dto);

	/**
	 * 根据IDS查询生产任务列表
	 * @param ids
	 * @return
	 */
    List<ProductionTaskVO> selectProductionTaskListByIds(@Param("ids") List<String> ids);

    /**
     * 查询当前用户角色
     * @param currentUserId
     * @return
     */
    List<String> selectCurrentUserRoles(@Param("currentUserId") String currentUserId);

	/**
	 * 根据工单查询工单任务列表
	 * @param workOrderNumber
	 * @return
	 */
    List<ProductionTaskViewVO> selectProductionTaskByProductionOrder(String workOrderNumber);

    /**
     * 查询工单最后一个工序的合格数量
     * @param workOrderNumber 工单编号
     * @return 合格数量
     */
    Long getLastProcessQualifiedQuantity(@Param("workOrderNumber") String workOrderNumber);

    /**
     * 查询工单的所有不合格工序信息
     * @param workOrderNumber 工单编号
     * @return 不合格工序信息列表
     */
    List<UnqualifiedProcessVO> getWorkOrderQualityInfo(@Param("workOrderNumber") String workOrderNumber);

    /**
     * 检查前一个工序是否已完成
     * @param workOrderNumber 工单编号
     * @param executionSequence 当前执行顺序
     * @return 前一个工序的状态，如果已完成返回完成状态，否则返回其他状态，如果不存在返回null
     */
    String checkPreviousProcessStatus(@Param("workOrderNumber") String workOrderNumber,
                                    @Param("executionSequence") Integer executionSequence);

    /**
     * 检查关联工单前置工序是否已完成
     * @param relatedWorkOrder 关联工单编号
     * @param assoWoPredProc 关联工单前置工序名称
     * @return 关联工单前置工序的状态，如果已完成返回完成状态，否则返回其他状态，如果不存在返回null
     */
    String checkRelatedProcessStatus(@Param("relatedWorkOrder") String relatedWorkOrder,
                                   @Param("assoWoPredProc") String assoWoPredProc);
                                   
    /**
     * 检查是否为最后一个工序
     * @param id 任务ID
     * @return 如果是最后一个工序返回1，否则返回0
     */
    int isLastProcess(@Param("id") String id);
    
    /**
     * 根据工序编码查询工序分类
     * @param processCode 工序编码
     * @return 工序分类
     */
    String getProcessClassificationByCode(@Param("processCode") String processCode);

    /**
     * 查询已延时未开始的生产任务
     * @return 延时任务列表
     */
    List<ProductionTaskVO> findDelayedTasks();

}