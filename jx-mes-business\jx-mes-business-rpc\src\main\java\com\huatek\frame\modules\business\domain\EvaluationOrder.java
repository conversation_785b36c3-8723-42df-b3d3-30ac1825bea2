package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.sql.Date;

import lombok.Getter;
import lombok.Setter;

/**
* @description 测评订单
* <AUTHOR>
* @date 2025-07-30
**/
@Setter
@Getter
@TableName("evaluation_order")
public class EvaluationOrder implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 客户信息id
     */
    @TableField(value = "customer_id")
    private String customerId;

    /**
     * 委托单位
     */
    @TableField(value = "entrusted_unit")
    private String entrustedUnit;


    /**
	 * 订单编号
     **/
    @TableField(value = "order_number"
    )
    private String orderNumber;

    
    /**
	 * 订单类型
     **/
    @TableField(value = "order_type"
    )
    private String orderType;
    
    /**
	 * 委托人
     **/
    @TableField(value = "principal"
    )
    private String principal;

    
    /**
	 * 委托人电话
     **/
    @TableField(value = "principals_phone_number"
    )
    private String principalsPhoneNumber;

    
    /**
	 * 委托日期
     **/
    @TableField(value = "date_of_entrustment"
    )
    private Date dateOfEntrustment;

    
    /**
	 * 要求完成日期
     **/
    @TableField(value = "deadline"
    )
    private Date deadline;

    /**
     * 实际完成日期
     */
    @TableField(value = "complete_date")
    private Date completeDate;

    /**
	 * 紧急程度
     **/
    @TableField(value = "urgency_level"
    )
    private String urgencyLevel;

    
    /**
	 * 工程代码
     **/
    @TableField(value = "engineering_code"
    )
    private String engineeringCode;

    
    /**
	 * 订单送检编号
     **/
    @TableField(value = "order_inspection_number"
    )
    private String orderInspectionNumber;

    
    /**
	 * 报告需求
     **/
    @TableField(value = "report_requirements"
    )
    private String reportRequirements;

    
    /**
	 * 报告形式
     **/
    @TableField(value = "report_format"
    )
    private String reportFormat;

    
    /**
	 * 数据形式
     **/
    @TableField(value = "data_format"
    )
    private String dataFormat;

    
    /**
	 * 电子版报告数据要求
     **/
    @TableField(value = "data_req_e_rep"
    )
    private String dataReqERep;

    
    /**
	 * 纸质版报告数据要求
     **/
    @TableField(value = "data_reqs_papereport"
    )
    private String dataReqsPapereport;

    
    /**
	 * 其他要求
     **/
    @TableField(value = "other_requirements"
    )
    private String otherRequirements;

    
    /**
	 * 备注
     **/
    @TableField(value = "`comment`"
    )
    private String comment;

    
    /**
	 * 上传附件
     **/
    @TableField(value = "upload_attachment"
    )
    private String uploadAttachment;

    
    /**
	 * 状态
     **/
    @TableField(value = "status"
    )
    private String status;

    
    /**
	 * 生产阶段
     **/
    @TableField(value = "production_stage"
    )
    private String productionStage;

    
    /**
	 * 试验方式
     **/
    @TableField(value = "test_methodology"
    )
    private String testMethodology;

    
    /**
	 * 验收通知单
     **/
    @TableField(value = "acceptance_notice"
    )
    private String acceptanceNotice;

    
    /**
	 * 订货合同号
     **/
    @TableField(value = "purchase_order_contract_number"
    )
    private String purchaseOrderContractNumber;

    
    /**
	 * 创建人
     **/
    @TableField(value = "codex_torch_creator_id"
    )
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @TableField(value = "codex_torch_updater"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;

    
    /**
	 * 所属组织
     **/
    @TableField(value = "codex_torch_group_id"
    )
    private String codexTorchGroupId;

    
    /**
	 * 创建时间
     **/
    @TableField(value = "codex_torch_create_datetime"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "codex_torch_update_datetime"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @TableField(value = "codex_torch_deleted"
    )
    private String codexTorchDeleted;
}