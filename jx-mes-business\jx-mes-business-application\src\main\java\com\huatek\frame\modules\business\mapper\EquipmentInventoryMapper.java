package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;

import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.business.domain.EquipmentInventory;
import com.huatek.frame.modules.business.domain.vo.DeviceInfoVO;
import com.huatek.frame.modules.business.domain.vo.EquipmentInventorySampleVO;
import  com.huatek.frame.modules.business.domain.vo.EquipmentInventoryVO;
import com.huatek.frame.modules.business.service.dto.EquipmentInventoryDTO;

import com.huatek.frame.modules.business.service.dto.EquipmentInventoryUpdateStatusDTO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

/**
* 设备台账mapper
* <AUTHOR>
* @date 2025-07-18
**/
public interface EquipmentInventoryMapper extends BaseMapper<EquipmentInventory> {

     /**
	 * 设备台账分页
	 * @param dto
	 * @return
	 */
	Page<EquipmentInventoryVO> selectEquipmentInventoryPage(EquipmentInventoryDTO dto);

    /**
	 * 外键关联表: standard_process_plan - manufacturer
     **/
    @ApiModelProperty("外键 standard_process_plan - manufacturer")
	Page<SelectOptionsVO> selectOptionsByManufacturer(String manufacturer);
    /**
	 * 外键关联表: device_type - device_type_code
     **/
    @ApiModelProperty("外键 device_type - device_type_code")
	Page<SelectOptionsVO> selectOptionsByDeviceType(String deviceType);
    /**
	 * 外键关联表: sys_group - group_code
     **/
    @ApiModelProperty("外键 sys_group - group_code")
	Page<SelectOptionsVO> selectOptionsByBelongingGroup(String belongingGroup);

    /**
     * 根据条件查询设备台账列表
     *
     * @param dto 设备台账信息
     * @return 设备台账集合信息
     */
    List<EquipmentInventoryVO> selectEquipmentInventoryList(EquipmentInventoryDTO dto);

	/**
	 * 根据IDS查询设备台账列表
	 * @param ids
	 * @return
	 */
    List<EquipmentInventoryVO> selectEquipmentInventoryListByIds(@Param("ids") List<String> ids);

	/**
	 * 查询相同设备类型的设备信息列表
	 * @param requestParam 设备类型
	 * @return
	 */
    List<DeviceInfoVO> findDeviceListByType(@Param("specificationModel") String requestParam);

	/**
	 * 批量修改设备状态
	 * @param requestParam
	 */
	void updateStatus( EquipmentInventoryUpdateStatusDTO requestParam);
	List<EquipmentInventorySampleVO> getAllEquipmentInventory();

}