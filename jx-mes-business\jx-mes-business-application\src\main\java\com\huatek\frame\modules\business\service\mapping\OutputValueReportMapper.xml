<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.OutputValueReportMapper">
    <sql id="Base_Column_List">
        t.id as id,
        t.settlement_unit as settlementUnit,
        t.annual as annual,
        t.`month` as `month`,
        t.internal_accounting_amount as internalAccountingAmount,
        t.customer_reconciliation_price as customerReconciliationPrice,
        t.codex_torch_creator_id as codexTorchCreatorId,
        t.codex_torch_updater as codexTorchUpdater,
        t.codex_torch_group_id as codexTorchGroupId,
        t.codex_torch_create_datetime as codexTorchCreateDatetime,
        t.codex_torch_update_datetime as codexTorchUpdateDatetime,
        t.codex_torch_deleted as codexTorchDeleted
    </sql>
    <select id="selectOutputValueReportPage"
            parameterType="com.huatek.frame.modules.business.service.dto.OutputValueReportDTO"
            resultType="com.huatek.frame.modules.business.domain.vo.OutputValueReportVO">
        select
        <include refid="Base_Column_List"/>
        from output_value_report t
        <where>
            and t.codex_torch_deleted = '0'
            <if test="settlementUnit != null and settlementUnit != ''">
                and t.settlement_unit like concat('%', #{settlementUnit} ,'%')
            </if>
            <if test="annual != null and annual != ''">
                and t.annual like concat('%', #{annual} ,'%')
            </if>
            <if test="month != null and month != ''">
                and t.month like concat('%', #{month} ,'%')
            </if>
            <if test="internalAccountingAmount != null and internalAccountingAmount != ''">
                and t.internal_accounting_amount = #{internalAccountingAmount}
            </if>
            <if test="customerReconciliationPrice != null and customerReconciliationPrice != ''">
                and t.customer_reconciliation_price = #{customerReconciliationPrice}
            </if>
            <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                and t.codex_torch_creator_id like concat('%', #{codexTorchCreatorId} ,'%')
            </if>
            <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                and t.codex_torch_updater like concat('%', #{codexTorchUpdater} ,'%')
            </if>
            <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                and t.codex_torch_group_id like concat('%', #{codexTorchGroupId} ,'%')
            </if>
            <if test="codexTorchCreateDatetime != null">
                and t.codex_torch_create_datetime = #{codexTorchCreateDatetime}
            </if>
            <if test="codexTorchUpdateDatetime != null">
                and t.codex_torch_update_datetime = #{codexTorchUpdateDatetime}
            </if>
            <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                and t.codex_torch_deleted like concat('%', #{codexTorchDeleted} ,'%')
            </if>
            ${params.dataScope}
        </where>
    </select>

    <select id="selectOutputValueReportList"
            parameterType="com.huatek.frame.modules.business.service.dto.OutputValueReportDTO"
            resultType="com.huatek.frame.modules.business.domain.vo.OutputValueReportVO">
        select
        <include refid="Base_Column_List"/>
        from output_value_report t
        <where>
            and t.codex_torch_deleted = '0'
            <if test="settlementUnit != null and settlementUnit != ''">
                and t.settlement_unit like concat('%', #{settlementUnit} ,'%')
            </if>
            <if test="annual != null and annual != ''">
                and t.annual like concat('%', #{annual} ,'%')
            </if>
            <if test="month != null and month != ''">
                and t.month like concat('%', #{month} ,'%')
            </if>
            <if test="internalAccountingAmount != null and internalAccountingAmount != ''">
                and t.internal_accounting_amount = #{internalAccountingAmount}
            </if>
            <if test="customerReconciliationPrice != null and customerReconciliationPrice != ''">
                and t.customer_reconciliation_price = #{customerReconciliationPrice}
            </if>
            <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                and t.codex_torch_creator_id like concat('%', #{codexTorchCreatorId} ,'%')
            </if>
            <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                and t.codex_torch_updater like concat('%', #{codexTorchUpdater} ,'%')
            </if>
            <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                and t.codex_torch_group_id like concat('%', #{codexTorchGroupId} ,'%')
            </if>
            <if test="codexTorchCreateDatetime != null">
                and t.codex_torch_create_datetime = #{codexTorchCreateDatetime}
            </if>
            <if test="codexTorchUpdateDatetime != null">
                and t.codex_torch_update_datetime = #{codexTorchUpdateDatetime}
            </if>
            <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                and t.codex_torch_deleted like concat('%', #{codexTorchDeleted} ,'%')
            </if>
            ${params.dataScope}
        </where>
    </select>

    <select id="selectOutputValueReportListByIds"
            resultType="com.huatek.frame.modules.business.domain.vo.OutputValueReportVO">
        select
        <include refid="Base_Column_List"/>
        from output_value_report t
        <where>
            <if test="ids != null and ids.size > 0">
                t.id in
                <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <!-- 获取首页看板统计数据 -->
    <select id="selectDashboardStats" resultType="com.huatek.frame.modules.business.domain.vo.OutputValueDashboardVO">
        SELECT
        t.internal_accounting_amount AS monthInternalAmount,
        t.customer_reconciliation_price AS monthCustomerAmount,
        (
        SELECT SUM(o.internal_accounting_amount)
        FROM output_value_report o
        WHERE o.annual = t.annual
        ) AS yearInternalTotalAmount,
        (
        SELECT SUM(o2.customer_reconciliation_price)
        FROM output_value_report o2
        WHERE o2.annual = t.annual
        ) AS yearCustomerTotalAmount
        FROM output_value_report t
        WHERE t.annual = YEAR(CURRENT_DATE)
        AND t.month = LPAD(MONTH(CURRENT_DATE), 2, '0')
    </select>
    <select id="selectProductionValueCalculations"
            resultType="com.huatek.frame.modules.business.service.dto.ProductionValueCalculationDTO">
        SELECT
        settlement_unit,
        internal_accounting_price,
        customer_accounting_price
        FROM production_value_calculation
        WHERE status = '1'
        AND codex_torch_deleted = 0
        AND YEAR(shipping_date) = YEAR(CURDATE())
        AND MONTH(shipping_date) = MONTH(CURDATE());

    </select>

</mapper>