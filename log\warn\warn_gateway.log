2025-09-02 09:36:32,702 WARN gateway [Thread-2] c.a.n.c.http.HttpClientBeanHolder [HttpClientBeanHolder.java : 108] [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-02 09:36:32,704 WARN gateway [Thread-13] c.a.nacos.common.notify.NotifyCenter [NotifyCenter.java : 145] [NotifyCenter] Start destroying Publisher
2025-09-02 09:36:32,717 WARN gateway [Thread-13] c.a.nacos.common.notify.NotifyCenter [NotifyCenter.java : 162] [NotifyCenter] Destruction of the end
2025-09-02 09:36:32,728 WARN gateway [Thread-2] c.a.n.c.http.HttpClientBeanHolder [HttpClientBeanHolder.java : 114] [HttpClientBeanHolder] Destruction of the end
2025-09-02 09:36:41,828 WARN gateway [SpringContextShutdownHook] o.s.b.f.s.DisposableBeanAdapter [DisposableBeanAdapter.java : 346] Destroy method 'close' on bean with name 'nacosServiceRegistry' threw an exception
java.lang.NullPointerException: null
	at com.alibaba.cloud.nacos.NacosServiceManager.nacosServiceShutDown(NacosServiceManager.java:115)
	at com.alibaba.cloud.nacos.registry.NacosServiceRegistry.close(NacosServiceRegistry.java:120)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.invokeCustomDestroyMethod(DisposableBeanAdapter.java:339)
	at org.springframework.beans.factory.support.DisposableBeanAdapter.destroy(DisposableBeanAdapter.java:273)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroyBean(DefaultSingletonBeanRegistry.java:579)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingleton(DefaultSingletonBeanRegistry.java:551)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingleton(DefaultListableBeanFactory.java:1092)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.destroySingletons(DefaultSingletonBeanRegistry.java:512)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.destroySingletons(DefaultListableBeanFactory.java:1085)
	at org.springframework.context.support.AbstractApplicationContext.destroyBeans(AbstractApplicationContext.java:1061)
	at org.springframework.context.support.AbstractApplicationContext.doClose(AbstractApplicationContext.java:1030)
	at org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContext.doClose(ReactiveWebServerApplicationContext.java:142)
	at org.springframework.context.support.AbstractApplicationContext$1.run(AbstractApplicationContext.java:949)
2025-09-02 09:42:59,874 WARN gateway [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 121] No URLs will be polled as dynamic configuration sources.
2025-09-02 09:42:59,889 WARN gateway [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 121] No URLs will be polled as dynamic configuration sources.
