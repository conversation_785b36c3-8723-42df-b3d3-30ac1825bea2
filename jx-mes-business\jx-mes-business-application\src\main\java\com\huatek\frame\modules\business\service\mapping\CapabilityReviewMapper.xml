<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.CapabilityReviewMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.inspection_type as inspectionType,
		t.`type` as `type`,
		t.entrusted_unit as entrustedUnit,
		t.product_model as productModel,
		t.product_name as productName,
		t.manufacturer as manufacturer,
		t.product_category as productCategory,
		t.product_information1 as productInformation1,
		t.standard_specification_number0 as standardSpecificationNumber0,
		t.review_result as reviewResult,
		t.feedback_processing as feedbackProcessing,
		t.`comment` as `comment`,
		t.reviewer_of_feedback as reviewerOfFeedback,
		t.review_time_of_feedback as reviewTimeOfFeedback,
		t.production_work_order as productionWorkOrder,
		t.order_number as orderNumber,
		t.submitter as submitter,
		t.submission_time as submissionTime,
		t.assoc_exception_feedback_num as assocExceptionFeedbackNum,
        t.capability_verification_id as capabilityVerificationId,
        t.product_list_id as productListId,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>
   <sql id="Base_Column_List2">
		t.id as id,
		t.inspection_type as inspectionType,
		t.`type` as `type`,
		t.entrusted_unit as entrustedUnit,
		t.product_model as productModel,
		t.product_name as productName,
		t.manufacturer as manufacturer,
        pc.category_name as productCategory,
		t.product_information1 as productInformation1,
		t.standard_specification_number0 as standardSpecificationNumber0,
		t.review_result as reviewResult,
		t.feedback_processing as feedbackProcessing,
		t.`comment` as `comment`,
		t.reviewer_of_feedback as reviewerOfFeedback,
		t.review_time_of_feedback as reviewTimeOfFeedback,
		t.production_work_order as productionWorkOrder,
		t.order_number as orderNumber,
		t.submitter as submitter,
		t.submission_time as submissionTime,
		t.assoc_exception_feedback_num as assocExceptionFeedbackNum,
        t.capability_verification_id as capabilityVerificationId,
        t.product_list_id as productListId,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>
	<select id="selectCapabilityReviewPage" parameterType="com.huatek.frame.modules.business.service.dto.CapabilityReviewDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.CapabilityReviewVO">
		select
		<include refid="Base_Column_List" />
			from capability_review t
            left join product_category pc on t.product_category = pc.id
            <where>
                and t.codex_torch_deleted = '0'
                <if test="inspectionType != null and inspectionType != ''">
                    and t.inspection_type  = #{inspectionType}
                </if>
                <if test="type != null and type != ''">
                     and t.type = #{type}
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and t.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
                </if>
                <if test="productModel != null and productModel != ''">
                    and t.product_model  like concat('%', #{productModel} ,'%')
                </if>
                <if test="productName != null and productName != ''">
                    and t.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and t.manufacturer  like concat('%', #{manufacturer} ,'%')
                </if>
                <if test="productCategory != null and productCategory != ''">
                    and pc.category_name  like concat('%', #{productCategory} ,'%')
                </if>
                <if test="productInformation1 != null and productInformation1 != ''">
                    and t.product_information1  = #{productInformation1}
                </if>
                <if test="standardSpecificationNumber0 != null and standardSpecificationNumber0 != ''">
                    and t.standard_specification_number0  like concat('%', #{standardSpecificationNumber0} ,'%')
                </if>
                <if test="reviewResult != null and reviewResult != ''">
                    and t.review_result  like concat('%', #{reviewResult} ,'%')
                </if>
                <if test="feedbackProcessing != null and feedbackProcessing != ''">
                    <choose>
                        <when test="feedbackProcessing.contains(',')">
                            and t.feedback_processing in 
                            <foreach collection="feedbackProcessing.split(',')" item="item" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                        </when>
                        <otherwise>
                            and t.feedback_processing = #{feedbackProcessing}
                        </otherwise>
                    </choose>
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  like concat('%', #{comment} ,'%')
                </if>
                <if test="reviewerOfFeedback != null and reviewerOfFeedback != ''">
                    and t.reviewer_of_feedback  like concat('%', #{reviewerOfFeedback} ,'%')
                </if>
                <if test="reviewTimeOfFeedback != null">
                    and t.review_time_of_feedback  = #{reviewTimeOfFeedback}
                </if>
                <if test="productionWorkOrder != null and productionWorkOrder != ''">
                    and t.production_work_order  like concat('%', #{productionWorkOrder} ,'%')
                </if>
                <if test="orderNumber != null and orderNumber != ''">
                    and t.order_number  like concat('%', #{orderNumber} ,'%')
                </if>
                <if test="submitter != null and submitter != ''">
                    and t.submitter  like concat('%', #{submitter} ,'%')
                </if>
                <if test="submissionTime != null">
                    and t.submission_time  = #{submissionTime}
                </if>
                <if test="assocExceptionFeedbackNum != null and assocExceptionFeedbackNum != ''">
                    and t.assoc_exception_feedback_num  like concat('%', #{assocExceptionFeedbackNum} ,'%')
                </if>
                <if test="capabilityVerificationId != null and capabilityVerificationId != ''">
                    and t.capability_verification_id = #{capabilityVerificationId}
                </if>

                <if test="productListId != null and productListId != ''">
                    and t.product_list_id = #{productListId}
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectCapabilityReviewList" parameterType="com.huatek.frame.modules.business.service.dto.CapabilityReviewDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.CapabilityReviewVO">
		select
		<include refid="Base_Column_List2" />
			from capability_review t
            left join product_category pc on t.product_category = pc.id
            <where>
                and t.codex_torch_deleted = '0'
                <if test="inspectionType != null and inspectionType != ''">
                    and t.inspection_type  = #{inspectionType}
                </if>
                <if test="type != null and type != ''">
                    and t.type  = #{type}
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and t.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
                </if>
                <if test="productModel != null and productModel != ''">
                    and t.product_model  like concat('%', #{productModel} ,'%')
                </if>
                <if test="productName != null and productName != ''">
                    and t.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and t.manufacturer  like concat('%', #{manufacturer} ,'%')
                </if>
                <if test="productCategory != null and productCategory != ''">
                    and pc.category_name  like concat('%', #{productCategory} ,'%')
                </if>
                <if test="productInformation1 != null and productInformation1 != ''">
                    and t.product_information1  = #{productInformation1}
                </if>
                <if test="standardSpecificationNumber0 != null and standardSpecificationNumber0 != ''">
                    and t.standard_specification_number0  like concat('%', #{standardSpecificationNumber0} ,'%')
                </if>
                <if test="reviewResult != null and reviewResult != ''">
                    and t.review_result  like concat('%', #{reviewResult} ,'%')
                </if>
                <if test="feedbackProcessing != null and feedbackProcessing != ''">
                    and t.feedback_processing  = #{feedbackProcessing}
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  like concat('%', #{comment} ,'%')
                </if>
                <if test="reviewerOfFeedback != null and reviewerOfFeedback != ''">
                    and t.reviewer_of_feedback  like concat('%', #{reviewerOfFeedback} ,'%')
                </if>
                <if test="reviewTimeOfFeedback != null">
                    and t.review_time_of_feedback  = #{reviewTimeOfFeedback}
                </if>
                <if test="productionWorkOrder != null and productionWorkOrder != ''">
                    and t.production_work_order  like concat('%', #{productionWorkOrder} ,'%')
                </if>
                <if test="orderNumber != null and orderNumber != ''">
                    and t.order_number  like concat('%', #{orderNumber} ,'%')
                </if>
                <if test="submitter != null and submitter != ''">
                    and t.submitter  like concat('%', #{submitter} ,'%')
                </if>
                <if test="submissionTime != null">
                    and t.submission_time  = #{submissionTime}
                </if>
                <if test="assocExceptionFeedbackNum != null and assocExceptionFeedbackNum != ''">
                    and t.assoc_exception_feedback_num  like concat('%', #{assocExceptionFeedbackNum} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectCapabilityReviewListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.CapabilityReviewVO">
		select
		<include refid="Base_Column_List" />
			from capability_review t
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>
</mapper>