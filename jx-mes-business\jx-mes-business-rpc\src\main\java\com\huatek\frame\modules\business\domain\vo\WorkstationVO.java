package com.huatek.frame.modules.business.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.huatek.frame.common.annotation.poi.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @description 工作站VO实体类
* <AUTHOR>
* @date 2025-07-17
**/
@Data
@ApiModel("工作站DTO实体类")
public class WorkstationVO implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;
    
    /**
	 * 工作站编号
     **/
    @ApiModelProperty("工作站编号")
    @Excel(name = "工作站编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String workstationNumber;
    
    /**
	 * 工作站名称
     **/
    @ApiModelProperty("工作站名称")
    @Excel(name = "工作站名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String workstationName;
    
    /**
	 * 扫码枪编号
     **/
    @ApiModelProperty("扫码枪编号")
    @Excel(name = "扫码枪编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String scannerGunNumber;
    
    /**
	 * 扫码枪SN码
     **/
    @ApiModelProperty("扫码枪SN码")
    @Excel(name = "扫码枪SN码",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String scannerGunSnCode;
    
    /**
	 * 所属部门
     **/
    @ApiModelProperty("所属部门")
    @Excel(name = "所属部门",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String department;

    /**
     * 所属部门
     **/
    @ApiModelProperty("所属部门编码")
    private String groupCode;

    /**
     * 所属部门id
     **/
    @ApiModelProperty("所属部门Id")
    private String groupId;
    
    /**
	 * 状态
     **/
    @ApiModelProperty("状态")
    @Excel(name = "状态",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String status;

    
    /**
	 * 备注
     **/
    @ApiModelProperty("备注")
    @Excel(name = "备注",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String comment;
    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}