package com.huatek.frame.modules.business.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;

/**
* @description 测评订单VO实体类
* <AUTHOR>
* @date 2025-07-30
**/
@Data
@ApiModel("测评订单DTO实体类")
public class EvaluationOrderVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
	 * 订单编号
     **/
    @ApiModelProperty("订单编号")
    @Excel(name = "订单编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String orderNumber;

    /**
     * 客户信息ID
     **/
    @ApiModelProperty("客户信息ID")
    private String customerId;

    /**
	 * 订单类型
     **/
    @ApiModelProperty("订单类型")
    @Excel(name = "订单类型",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String orderType;

    /**
     * 委托单位id
     **/
    @ApiModelProperty("委托单位id")
    private String entrustedUnit;

    /**
	 * 委托单位
     **/
    @ApiModelProperty("委托单位")
    @Excel(name = "委托单位",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String entrustedUnitName;

    /**
	 * 结算单位
     **/
    @ApiModelProperty("结算单位")
    private String settlementUnit;

    /**
     * 委托人
     **/
    @ApiModelProperty("委托人")
    private String principal;

    /**
     * 委托人电话
     **/
    @ApiModelProperty("委托人电话")
    private String principalsPhoneNumber;


    /**
     * 委托日期
     **/
    @ApiModelProperty("委托日期")
    @Excel(name = "委托日期",
            cellType = Excel.ColumnType.NUMERIC,
            dateFormat = "yyyy-MM-dd",
            type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateOfEntrustment;

    /**
     * 紧急程度
     **/
    @ApiModelProperty("紧急程度")
    @Excel(name = "紧急程度",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String urgencyLevel;

    /**
     * 要求完成日期
     **/
    @ApiModelProperty("要求完成日期")
    @Excel(name = "要求完成日期",
            cellType = Excel.ColumnType.NUMERIC,
            dateFormat = "yyyy-MM-dd",
            type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date deadline;

    /**
     * 实际完成日期
     */
    @ApiModelProperty("实际完成日期")
//    @Excel(name = "实际完成日期",
//            cellType = Excel.ColumnType.NUMERIC,
//            dateFormat = "yyyy-MM-dd",
//            type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date completeDate;

    /**
     * 状态
     **/
    @ApiModelProperty("状态")
    @Excel(name = "状态",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String status;

    /**
     * 生产阶段
     **/
    @ApiModelProperty("生产阶段")
    @Excel(name = "生产阶段",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productionStage;

    /**
     * 客户经理
     **/
    @ApiModelProperty("客户经理")
    @Excel(name = "客户经理",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String customerManager;


    /**
	 * 工程代码
     **/
    @ApiModelProperty("工程代码")
    private String engineeringCode;

    /**
	 * 订单送检编号
     **/
    @ApiModelProperty("订单送检编号")
    private String orderInspectionNumber;

    /**
	 * 报告需求
     **/
    @ApiModelProperty("报告需求")
    private String reportRequirements;

    /**
	 * 报告形式
     **/
    @ApiModelProperty("报告形式")
    private String reportFormat;

    /**
	 * 数据形式
     **/
    @ApiModelProperty("数据形式")
    private String dataFormat;

    /**
	 * 电子版报告数据要求
     **/
    @ApiModelProperty("电子版报告数据要求")
    private String dataReqERep;

    /**
	 * 纸质版报告数据要求
     **/
    @ApiModelProperty("纸质版报告数据要求")
    private String dataReqsPapereport;

    /**
	 * 其他要求
     **/
    @ApiModelProperty("其他要求")
    private String otherRequirements;

    /**
	 * 备注
     **/
    @ApiModelProperty("备注")
    private String comment;

    /**
	 * 上传附件
     **/
    @ApiModelProperty("上传附件")
    private String uploadAttachment;

    /**
	 * 试验方式
     **/
    @ApiModelProperty("试验方式")
    private String testMethodology;

    /**
	 * 验收通知单
     **/
    @ApiModelProperty("验收通知单")
    private String acceptanceNotice;

    /**
	 * 订货合同号
     **/
    @ApiModelProperty("订货合同号")
    private String purchaseOrderContractNumber;


    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
     * 创建人姓名
     */
    @ApiModelProperty("创建人姓名")
    private String creator;


    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @Excel(name = "创建时间",
            cellType = Excel.ColumnType.NUMERIC,
            dateFormat = "yyyy-MM-dd",
            type = Excel.Type.ALL)
    private Timestamp codexTorchCreateDatetime;

    /**
     * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.EXPORT)
    private String codexTorchUpdater;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}