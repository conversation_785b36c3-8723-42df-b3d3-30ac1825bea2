package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.modules.business.domain.vo.EquipmentInventorySampleVO;
import com.huatek.frame.modules.business.service.dto.EquipmentInventoryUpdateStatusDTO;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.modules.business.domain.EquipmentInventory;
import com.huatek.frame.modules.business.service.EquipmentInventoryService;
import com.huatek.frame.modules.business.service.dto.EquipmentInventoryDTO;
import com.huatek.frame.modules.business.domain.vo.EquipmentInventoryVO;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-07-18
**/
@Api(tags = "设备台账管理")
@RestController
@RequestMapping("/api/equipmentInventory")
public class EquipmentInventoryController {

	@Autowired
    private EquipmentInventoryService equipmentInventoryService;

	/**
	 * 设备台账列表
	 * 
	 * @param dto 设备台账DTO 实体对象
	 * @return
	 */
    @Log("设备台账列表")
    @ApiOperation(value = "设备台账列表查询")
    @PostMapping(value = "/equipmentInventoryList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("equipmentInventory:list")
    public TorchResponse<List<EquipmentInventoryVO>> query(@RequestBody EquipmentInventoryDTO dto){
        return equipmentInventoryService.findEquipmentInventoryPage(dto);
    }

	/**
	 * 新增/修改设备台账
	 * 
	 * @param equipmentInventoryDto 设备台账DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改设备台账")
    @ApiOperation(value = "设备台账新增/修改操作")
    @PostMapping(value = "/equipmentInventory", produces = { "application/json;charset=utf-8" })
    @TorchPerm("equipmentInventory:add#equipmentInventory:edit")
    public TorchResponse add(@RequestBody EquipmentInventoryDTO equipmentInventoryDto) throws Exception {
		// BeanValidatorFactory.validate(equipmentInventoryDto);
		return equipmentInventoryService.saveOrUpdate(equipmentInventoryDto);
	}

	/**
	 * 查询设备台账详情
	 * 
	 * @param id 主键id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("设备台账详情")
    @ApiOperation(value = "设备台账详情查询")
    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("equipmentInventory:detail")
	public TorchResponse detail(@PathVariable(value = "id") String id) {
		return equipmentInventoryService.findEquipmentInventory(id);
	}

	/**
	 * 删除设备台账
	 * 
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
    @Log("删除设备台账")
    @ApiOperation(value = "设备台账删除操作")
    @TorchPerm("equipmentInventory:del")
    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	public TorchResponse delete(@RequestBody String[] ids) {
		return equipmentInventoryService.delete(ids);
	}

    @ApiOperation(value = "设备台账联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return equipmentInventoryService.getOptionsList(id);
	}





    @Log("设备台账导出")
    @ApiOperation(value = "设备台账导出")
    @TorchPerm("equipmentInventory:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody EquipmentInventoryDTO dto)
    {
        List<EquipmentInventoryVO> list = equipmentInventoryService.selectEquipmentInventoryList(dto);
        ExcelUtil<EquipmentInventoryVO> util = new ExcelUtil<EquipmentInventoryVO>(EquipmentInventoryVO.class);
        util.exportExcel(response, list, "设备台账数据");
    }

    @Log("设备台账导入")
    @ApiOperation(value = "设备台账导入")
    @TorchPerm("equipmentInventory:import")
    @PostMapping("/importData")
    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
    {
        ExcelUtil<EquipmentInventoryVO> util = new ExcelUtil<EquipmentInventoryVO>(EquipmentInventoryVO.class);
        List<EquipmentInventoryVO> list = util.importExcel(file.getInputStream());
        return equipmentInventoryService.importEquipmentInventory(list, unionColumns, true, "");
    }

    @Log("设备台账导入模板")
    @ApiOperation(value = "设备台账导入模板下载")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<EquipmentInventoryVO> util = new ExcelUtil<EquipmentInventoryVO>(EquipmentInventoryVO.class);
        util.importTemplateExcel(response, "设备台账数据");
    }

    @Log("根据Ids获取设备台账列表")
    @ApiOperation(value = "设备台账 根据Ids批量查询")
    @PostMapping(value = "/equipmentInventoryList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getEquipmentInventoryListByIds(@RequestBody List<String> ids) {
        return equipmentInventoryService.selectEquipmentInventoryListByIds(ids);
    }

    /**
     * 设备台账主子表单组合提交
     *
	 * @param equipmentInventoryDto 设备台账DTO实体对象
     * @return
     */
    @SuppressWarnings("rawtypes")
    @Log("设备台账主子表单组合提交")
    @ApiOperation(value = "设备台账主子表单组合提交")
    //@TorchPerm("equipmentInventory:masterDetailSubmit")
    @PostMapping(value = "/masterDetailSubmit", produces = {"application/json;charset=utf-8"})
    public TorchResponse submitMasterDetails(@RequestBody EquipmentInventoryDTO equipmentInventoryDto) {
        return equipmentInventoryService.submitMasterDetails(equipmentInventoryDto);
    }

    @Log("查询设备最新的溯源记录")
    @ApiOperation(value = "查询设备最新的溯源记录")
    @GetMapping(value = "/latestTraceInfo" , produces = {"application/json;charset=utf-8"})
    public TorchResponse getLatestTraceInfo(@RequestParam String deviceSerialNumber){
        return equipmentInventoryService.getLatestTraceInfo(deviceSerialNumber);
    }

    /**
     * 查询相同设备类型的设备信息列表
     * @param requestParam 设备类型
     * @return
     */
    @Log("查询相同设备类型的设备信息列表")
    @ApiOperation(value = "查询相同设备类型的设备信息列表")
    @GetMapping(value = "/deviceInfoList", produces = {"application/json;charset=utf-8"})
    public TorchResponse findDeviceInfoListByType(@RequestParam String requestParam){
        return equipmentInventoryService.findDeviceListByType(requestParam);
    }

    /**
     * 批量更新设备状态
     * @param
     * @return
     */
    @Log("批量更新设备状态")
    @ApiOperation(value = "批量更新设备状态")
    @PostMapping(value = "/updateStatus", produces = {"application/json;charset=utf-8"})
    public TorchResponse updateStatus(@RequestBody EquipmentInventoryUpdateStatusDTO requestParam){
        return equipmentInventoryService.updateStatus(requestParam);
    }

    /**
     * 设备及时校准率
     * @param id 设备台账id
     * @return
     */
    @Log("设备及时校准率")
    @ApiOperation(value = "设备及时校准率")
    @PostMapping(value = "/calibrationRate", produces = {"application/json;charset=utf-8"})
    public TorchResponse calibrationRate(@RequestParam String id){
        return equipmentInventoryService.calibrationRate(id);
    }


    @Log("查询所有设备台账")
    @ApiOperation(value = "查询所有设备台账")
    @GetMapping(value = "/all", produces = {"application/json;charset=utf-8"})
    public TorchResponse<List<EquipmentInventorySampleVO>> getAllEquipmentInventory(){
        return equipmentInventoryService.getAllEquipmentInventory();
    }

}