package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;

import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;

import com.huatek.frame.modules.business.service.CodeManagementService;
import com.huatek.frame.modules.system.domain.SysGroup;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
//import com.huatek.frame.modules.system.mapper.SysGroupMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.StandardProcessManagement;
import com.huatek.frame.modules.business.domain.vo.StandardProcessManagementVO;
import com.huatek.frame.modules.business.mapper.StandardProcessManagementMapper;
import com.huatek.frame.modules.business.service.StandardProcessManagementService;
import com.huatek.frame.modules.business.service.dto.StandardProcessManagementDTO;
import java.sql.Date;
import com.huatek.frame.modules.business.domain.Workstation;
import com.huatek.frame.modules.business.mapper.WorkstationMapper;

import org.springframework.util.CollectionUtils;



/**
 * 标准工序管理 ServiceImpl
 * <AUTHOR>
 * @date 2025-07-17
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "standardProcessManagement")
//@RefreshScope
@Slf4j
public class StandardProcessManagementServiceImpl implements StandardProcessManagementService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private StandardProcessManagementMapper standardProcessManagementMapper;

	@Autowired
    private WorkstationMapper workstationMapper;
//	@Autowired
//    private SysGroupMapper sysGroupMapper;
    @Autowired
    private CodeManagementService codeManagementService;

    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	public StandardProcessManagementServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<StandardProcessManagementVO>> findStandardProcessManagementPage(StandardProcessManagementDTO dto) {

		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<StandardProcessManagementVO> standardProcessManagements = standardProcessManagementMapper.selectStandardProcessManagementPage(dto);
		TorchResponse<List<StandardProcessManagementVO>> response = new TorchResponse<List<StandardProcessManagementVO>>();
		response.getData().setData(standardProcessManagements);
		response.setStatus(200);
		response.getData().setCount(standardProcessManagements.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(StandardProcessManagementDTO standardProcessManagementDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(standardProcessManagementDto.getCodexTorchDeleted())) {
            standardProcessManagementDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = standardProcessManagementDto.getId();
		StandardProcessManagement entity = new StandardProcessManagement();
        BeanUtils.copyProperties(standardProcessManagementDto, entity);
        entity.setDepartment(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
            TorchResponse response =  codeManagementService.getOrderNumber("GXBM");
            entity.setStepNumber(response.getData().getData().toString());
			standardProcessManagementMapper.insert(entity);
		} else {
			standardProcessManagementMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        StandardProcessManagementVO vo = new StandardProcessManagementVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<StandardProcessManagementVO> findStandardProcessManagement(String id) {
		StandardProcessManagementVO vo = new StandardProcessManagementVO();
		if (!HuatekTools.isEmpty(id)) {
			StandardProcessManagement entity = standardProcessManagementMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);

		}
		TorchResponse<StandardProcessManagementVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
		standardProcessManagementMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("workstation", workstation -> standardProcessManagementMapper.selectOptionsByWorkstation(workstation,SecurityContextHolder.getCurrentUserGroupId()));
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("testingTeam",testingTeam -> standardProcessManagementMapper.selectOptionsByTestingTeam(testingTeam,SecurityContextHolder.getCurrentUserGroupId()));
            selectOptionsFuncMap.put("department",standardProcessManagementMapper::selectOptionsByDepartment);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("mpPdaCalcRls",mpPdaCalcRls -> standardProcessManagementMapper.selectOptionsByMpPdaCalcRls(mpPdaCalcRls,SecurityContextHolder.getCurrentUserGroupId()));
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("totalNoncompliantCount",totalNoncompliantCount -> standardProcessManagementMapper.selectOptionsByTotalNoncompliantCount(totalNoncompliantCount,SecurityContextHolder.getCurrentUserGroupId()));
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "standard_process_management", convertorFields = "processClassification,status,isApproval")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<StandardProcessManagementVO> selectStandardProcessManagementList(StandardProcessManagementDTO dto) {
        return standardProcessManagementMapper.selectStandardProcessManagementList(dto);
    }

    /**
     * 导入标准工序管理数据
     *
     * @param standardProcessManagementList 标准工序管理数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "standard_process_management", convertorFields = "processClassification,status,isApproval")
    public TorchResponse importStandardProcessManagement(List<StandardProcessManagementVO> standardProcessManagementList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(standardProcessManagementList) || standardProcessManagementList.size() == 0) {
            throw new ServiceException("导入标准工序管理数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (StandardProcessManagementVO vo : standardProcessManagementList) {
            try {
                StandardProcessManagement standardProcessManagement = new StandardProcessManagement();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, standardProcessManagement);
                QueryWrapper<StandardProcessManagement> wrapper = new QueryWrapper();
                StandardProcessManagement oldStandardProcessManagement = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = StandardProcessManagementVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<StandardProcessManagement> oldStandardProcessManagementList = standardProcessManagementMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldStandardProcessManagementList) && oldStandardProcessManagementList.size() > 1) {
                        standardProcessManagementMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldStandardProcessManagementList) && oldStandardProcessManagementList.size() == 1) {
                        oldStandardProcessManagement = oldStandardProcessManagementList.get(0);
                    }
                }
                if (StringUtils.isNull(oldStandardProcessManagement)) {
                    BeanValidators.validateWithException(validator, vo);
                    standardProcessManagementMapper.insert(standardProcessManagement);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、工序编号 " + vo.getStepNumber() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldStandardProcessManagement, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    standardProcessManagementMapper.updateById(oldStandardProcessManagement);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、工序编号 " + vo.getStepNumber() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、工序编号 " + vo.getStepNumber() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、工序编号 " + vo.getStepNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(StandardProcessManagementVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (HuatekTools.isEmpty(vo.getStepNumber())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>工序编号不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getProcessName2())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>工序名称不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getProcessClassification())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>工序分类不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getWorkstation())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>工作站不能为空!");
        }
        if (!HuatekTools.isEmpty(vo.getWorkstation())) {
            List<String> workstationList = Arrays.asList(vo.getWorkstation().split(","));
            List<Workstation> list = workstationMapper.selectList(new QueryWrapper<Workstation>().in("workstation_number", workstationList));
            if (CollectionUtils.isEmpty(list)) {
                failureRecord++;
                failureRecordMsg.append("工作站=" + vo.getWorkstation() + "; ");
            }
        }
//        if (!HuatekTools.isEmpty(vo.getTestingTeam())) {
//            List<String> testingTeamList = Arrays.asList(vo.getTestingTeam().split(","));
//            List<SysGroup> list = sysGroupMapper.selectList(new QueryWrapper<SysGroup>().in("group_code", testingTeamList));
//            if (CollectionUtils.isEmpty(list)) {
//                failureRecord++;
//                failureRecordMsg.append("试验班组=" + vo.getTestingTeam() + "; ");
//            }
//        }
        if (!HuatekTools.isEmpty(vo.getMpPdaCalcRls())) {
            List<String> mpPdaCalcRlsList = Arrays.asList(vo.getMpPdaCalcRls().split(","));
            List<Workstation> list = workstationMapper.selectList(new QueryWrapper<Workstation>().in("workstation_number", mpPdaCalcRlsList));
            if (CollectionUtils.isEmpty(list)) {
                failureRecord++;
                failureRecordMsg.append("多工序PDA计算规则=" + vo.getMpPdaCalcRls() + "; ");
            }
        }
        if (!HuatekTools.isEmpty(vo.getTotalNoncompliantCount())) {
            List<String> totalNoncompliantCountList = Arrays.asList(vo.getTotalNoncompliantCount().split(","));
            List<Workstation> list = workstationMapper.selectList(new QueryWrapper<Workstation>().in("workstation_number", totalNoncompliantCountList));
            if (CollectionUtils.isEmpty(list)) {
                failureRecord++;
                failureRecordMsg.append("不合格数量总和/=" + vo.getTotalNoncompliantCount() + "; ");
            }
        }
        if (HuatekTools.isEmpty(vo.getCodexTorchUpdater())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>更新人不能为空!");
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectStandardProcessManagementListByIds(List<String> ids) {
        List<StandardProcessManagementVO> standardProcessManagementList = standardProcessManagementMapper.selectStandardProcessManagementListByIds(ids);

		TorchResponse<List<StandardProcessManagementVO>> response = new TorchResponse<List<StandardProcessManagementVO>>();
		response.getData().setData(standardProcessManagementList);
		response.setStatus(200);
		response.getData().setCount((long)standardProcessManagementList.size());
		return response;
    }


    @Override
    public TorchResponse getLinkageData(String linkageDataTableName, String conditionalValue) {
        Map<String, String> data = new HashMap();
        try {
            switch (linkageDataTableName) {
                case "workstation":
                    data = selectDataLinkageByWorkstation(conditionalValue);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("查询数据异常，请联系管理员！");
        }
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(data);
        return response;
    }

    @Override
    public Map<String,String> selectDataLinkageByWorkstation(String workstation_id) {
        return standardProcessManagementMapper.selectDataLinkageByWorkstation(workstation_id);
    }

}
