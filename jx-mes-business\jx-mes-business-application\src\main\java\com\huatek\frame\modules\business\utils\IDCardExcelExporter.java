package com.huatek.frame.modules.business.utils;

import com.huatek.frame.modules.business.domain.vo.ProductionOrderIDCardVO;
import com.huatek.frame.modules.business.domain.vo.ProductionOrderExpiredCardVO;
import com.huatek.frame.modules.business.domain.vo.ProductionOrderIDCardDPAVO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

/**
 * 标识卡Excel导出工具类
 * 支持三种类型的标识卡：非DPA标识卡、非DPA失效标识卡、DPA标识卡
 * 每张标识卡为7行2列格式，带边框，多个标识卡换行排列
 * 
 * <AUTHOR>
 * @date 2025-01-02
 */
public class IDCardExcelExporter {

    private static final int CARD_ROWS = 7; // 每张标识卡7行
    private static final int CARD_COLS = 2; // 每张标识卡2列
    private static final int CARDS_PER_ROW = 3; // 每行显示3张标识卡
    
    /**
     * 导出标识卡Excel
     * @param response HTTP响应
     * @param cardDataList 标识卡数据列表
     * @throws IOException IO异常
     */
    public static void exportIDCards(HttpServletResponse response, List<Object> cardDataList) throws IOException {
        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("标识卡");
        
        // 设置列宽
        for (int i = 0; i < CARDS_PER_ROW * CARD_COLS; i++) {
            sheet.setColumnWidth(i, 4000); // 设置列宽
        }
        
        // 创建样式
        CellStyle headerStyle = createHeaderStyle(workbook);
        CellStyle labelStyle = createLabelStyle(workbook);
        CellStyle valueStyle = createValueStyle(workbook);
        CellStyle borderStyle = createBorderStyle(workbook);
        
        int currentRow = 0;
        int cardIndex = 0;
        
        while (cardIndex < cardDataList.size()) {
            // 计算当前行要显示的卡片数量
            int cardsInCurrentRow = Math.min(CARDS_PER_ROW, cardDataList.size() - cardIndex);
            
            // 为当前行的卡片创建行
            for (int rowInCard = 0; rowInCard < CARD_ROWS; rowInCard++) {
                Row row = sheet.createRow(currentRow + rowInCard);
                row.setHeight((short) 600); // 设置行高
                
                // 为每张卡片创建单元格
                for (int cardInRow = 0; cardInRow < cardsInCurrentRow; cardInRow++) {
                    Object cardData = cardDataList.get(cardIndex + cardInRow);
                    int startCol = cardInRow * CARD_COLS;
                    
                    createCardRow(row, startCol, rowInCard, cardData, headerStyle, labelStyle, valueStyle, borderStyle);
                }
            }
            
            currentRow += CARD_ROWS + 1; // 卡片间留一行空隙
            cardIndex += cardsInCurrentRow;
        }
        
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = "标识卡_" + System.currentTimeMillis() + ".xlsx";
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(fileName, "UTF-8"));
        
        // 写入响应流
        workbook.write(response.getOutputStream());
        workbook.close();
    }
    
    /**
     * 创建标识卡的一行
     */
    private static void createCardRow(Row row, int startCol, int rowInCard, Object cardData, 
                                    CellStyle headerStyle, CellStyle labelStyle, CellStyle valueStyle, CellStyle borderStyle) {
        
        if (cardData instanceof ProductionOrderIDCardVO) {
            createNormalCardRow(row, startCol, rowInCard, (ProductionOrderIDCardVO) cardData, headerStyle, labelStyle, valueStyle);
        } else if (cardData instanceof ProductionOrderExpiredCardVO) {
            createExpiredCardRow(row, startCol, rowInCard, (ProductionOrderExpiredCardVO) cardData, headerStyle, labelStyle, valueStyle);
        } else if (cardData instanceof ProductionOrderIDCardDPAVO) {
            createDPACardRow(row, startCol, rowInCard, (ProductionOrderIDCardDPAVO) cardData, headerStyle, labelStyle, valueStyle);
        }
    }
    
    /**
     * 创建非DPA标识卡行
     */
    private static void createNormalCardRow(Row row, int startCol, int rowInCard, ProductionOrderIDCardVO card,
                                          CellStyle headerStyle, CellStyle labelStyle, CellStyle valueStyle) {
        Cell leftCell = row.createCell(startCol);
        Cell rightCell = row.createCell(startCol + 1);
        
        switch (rowInCard) {
            case 0: // 标题行
                leftCell.setCellValue("Logo 君信\n电子");
                leftCell.setCellStyle(headerStyle);
                rightCell.setCellValue("标 识 卡");
                rightCell.setCellStyle(headerStyle);
                break;
            case 1: // 编号
                leftCell.setCellValue("编号");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getWorkOrderNumber() != null ? card.getWorkOrderNumber() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 2: // 送筛单位
                leftCell.setCellValue("送筛单位");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getEntrustedUnit() != null ? card.getEntrustedUnit() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 3: // 器件型号
                leftCell.setCellValue("器件型号");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getProductModel() != null ? card.getProductModel() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 4: // 生产批次
                leftCell.setCellValue("生产批次");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getProductionBatch() != null ? card.getProductionBatch() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 5: // 合格数
                leftCell.setCellValue("合格数");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getQualifiedQuantity() != null ? card.getQualifiedQuantity().toString() : "0");
                rightCell.setCellStyle(valueStyle);
                break;
            case 6: // 失效数
                leftCell.setCellValue("失效数");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getFailureQuantity() != null ? card.getFailureQuantity().toString() : "0");
                rightCell.setCellStyle(valueStyle);
                break;
        }
    }
    
    /**
     * 创建失效标识卡行
     */
    private static void createExpiredCardRow(Row row, int startCol, int rowInCard, ProductionOrderExpiredCardVO card,
                                           CellStyle headerStyle, CellStyle labelStyle, CellStyle valueStyle) {
        Cell leftCell = row.createCell(startCol);
        Cell rightCell = row.createCell(startCol + 1);
        
        switch (rowInCard) {
            case 0: // 标题行
                leftCell.setCellValue("Logo 君信\n电子");
                leftCell.setCellStyle(headerStyle);
                rightCell.setCellValue("标 识 卡");
                rightCell.setCellStyle(headerStyle);
                break;
            case 1: // 编号
                leftCell.setCellValue("编号");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getWorkOrderNumber() != null ? card.getWorkOrderNumber() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 2: // 送筛单位
                leftCell.setCellValue("送筛单位");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getEntrustedUnit() != null ? card.getEntrustedUnit() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 3: // 器件型号
                leftCell.setCellValue("器件型号");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getProductModel() != null ? card.getProductModel() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 4: // 生产批次
                leftCell.setCellValue("生产批次");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getProductionBatch() != null ? card.getProductionBatch() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 5: // 失效数
                leftCell.setCellValue("失效数");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getFailureQuantity() != null ? card.getFailureQuantity().toString() : "0");
                rightCell.setCellStyle(valueStyle);
                break;
            case 6: // 备注
                leftCell.setCellValue("备注");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getRemark() != null ? card.getRemark() : "");
                rightCell.setCellStyle(valueStyle);
                break;
        }
    }
    
    /**
     * 创建DPA标识卡行
     */
    private static void createDPACardRow(Row row, int startCol, int rowInCard, ProductionOrderIDCardDPAVO card,
                                       CellStyle headerStyle, CellStyle labelStyle, CellStyle valueStyle) {
        Cell leftCell = row.createCell(startCol);
        Cell rightCell = row.createCell(startCol + 1);
        
        switch (rowInCard) {
            case 0: // 标题行
                leftCell.setCellValue("Logo 君信\n电子");
                leftCell.setCellStyle(headerStyle);
                rightCell.setCellValue("标 识 卡");
                rightCell.setCellStyle(headerStyle);
                break;
            case 1: // 编号
                leftCell.setCellValue("编号");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getWorkOrderNumber() != null ? card.getWorkOrderNumber() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 2: // 委托单位
                leftCell.setCellValue("委托单位");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getEntrustedUnit() != null ? card.getEntrustedUnit() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 3: // 委托类型
                leftCell.setCellValue("委托类型 DPA");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue("");
                rightCell.setCellStyle(valueStyle);
                break;
            case 4: // 器件型号
                leftCell.setCellValue("器件型号");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getProductModel() != null ? card.getProductModel() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 5: // 生产批次
                leftCell.setCellValue("生产批次");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getProductionBatch() != null ? card.getProductionBatch() : "");
                rightCell.setCellStyle(valueStyle);
                break;
            case 6: // 结论
                leftCell.setCellValue("结论");
                leftCell.setCellStyle(labelStyle);
                rightCell.setCellValue(card.getConclusion() != null ? card.getConclusion() : "");
                rightCell.setCellStyle(valueStyle);
                break;
        }
    }

    /**
     * 创建标题样式
     */
    private static CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置字体
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);

        // 设置自动换行
        style.setWrapText(true);

        return style;
    }

    /**
     * 创建标签样式
     */
    private static CellStyle createLabelStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置字体
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);

        return style;
    }

    /**
     * 创建值样式
     */
    private static CellStyle createValueStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        // 设置对齐方式
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置字体
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);

        // 设置自动换行
        style.setWrapText(true);

        return style;
    }

    /**
     * 创建边框样式
     */
    private static CellStyle createBorderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();

        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        return style;
    }
}
