package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;

import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;

import com.huatek.frame.modules.business.service.CodeManagementService;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
//import com.huatek.frame.modules.system.mapper.DicDetailMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.StandardSpecification;
import com.huatek.frame.modules.business.domain.vo.StandardSpecificationVO;
import com.huatek.frame.modules.business.mapper.StandardSpecificationMapper;
import com.huatek.frame.modules.business.service.StandardSpecificationService;
import com.huatek.frame.modules.business.service.dto.StandardSpecificationDTO;
import java.sql.Date;
import org.springframework.util.CollectionUtils;



/**
 * 标准规范 ServiceImpl
 * <AUTHOR>
 * @date 2025-07-21
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "standardSpecification")
//@RefreshScope
@Slf4j
public class StandardSpecificationServiceImpl implements StandardSpecificationService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private StandardSpecificationMapper standardSpecificationMapper;
//    @Autowired
//    private DicDetailMapper dicDetailMapper;
    @Autowired
    private CodeManagementService codeManagementService;

    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	public StandardSpecificationServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<StandardSpecificationVO>> findStandardSpecificationPage(StandardSpecificationDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<StandardSpecificationVO> standardSpecifications = standardSpecificationMapper.selectStandardSpecificationPage(dto);
		TorchResponse<List<StandardSpecificationVO>> response = new TorchResponse<List<StandardSpecificationVO>>();
		response.getData().setData(standardSpecifications);
		response.setStatus(200);
		response.getData().setCount(standardSpecifications.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(StandardSpecificationDTO standardSpecificationDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(standardSpecificationDto.getCodexTorchDeleted())) {
            standardSpecificationDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }

        String id = standardSpecificationDto.getId();
		StandardSpecification entity = new StandardSpecification();
        BeanUtils.copyProperties(standardSpecificationDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
            //查询你编码前准获取编码
            if(standardSpecificationDto.getSpecificationType().equals("1")){
                TorchResponse response =  codeManagementService.getOrderNumber("Q/JX-WLBZ-");
                entity.setControlledNumber(response.getData().getData().toString());
            }
            if(standardSpecificationDto.getSpecificationType().equals("0")){
                TorchResponse response = codeManagementService.getOrderNumber("Q/JX-CPGF-");
                entity.setControlledNumber(response.getData().getData().toString());
            }
			standardSpecificationMapper.insert(entity);

		} else {
			standardSpecificationMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        StandardSpecificationVO vo = new StandardSpecificationVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<StandardSpecificationVO> findStandardSpecification(String id) {
		StandardSpecificationVO vo = new StandardSpecificationVO();
		if (!HuatekTools.isEmpty(id)) {
			StandardSpecification entity = standardSpecificationMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<StandardSpecificationVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
		standardSpecificationMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
            selectOptionsFuncMap.put("entrustedUnit",standardSpecificationMapper::selectOptionsByEntrustedUnit);
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "standard_specification", convertorFields = "specificationType")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<StandardSpecificationVO> selectStandardSpecificationList(StandardSpecificationDTO dto) {
        return standardSpecificationMapper.selectStandardSpecificationList(dto);
    }

    /**
     * 导入标准规范数据
     *
     * @param standardSpecificationList 标准规范数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "standard_specification", convertorFields = "specificationType")
    public TorchResponse importStandardSpecification(List<StandardSpecificationVO> standardSpecificationList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(standardSpecificationList) || standardSpecificationList.size() == 0) {
            throw new ServiceException("导入标准规范数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (StandardSpecificationVO vo : standardSpecificationList) {
            try {
                StandardSpecification standardSpecification = new StandardSpecification();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, standardSpecification);
                QueryWrapper<StandardSpecification> wrapper = new QueryWrapper();
                StandardSpecification oldStandardSpecification = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = StandardSpecificationVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<StandardSpecification> oldStandardSpecificationList = standardSpecificationMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldStandardSpecificationList) && oldStandardSpecificationList.size() > 1) {
                        standardSpecificationMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldStandardSpecificationList) && oldStandardSpecificationList.size() == 1) {
                        oldStandardSpecification = oldStandardSpecificationList.get(0);
                    }
                }
                if (StringUtils.isNull(oldStandardSpecification)) {
                    BeanValidators.validateWithException(validator, vo);
                    standardSpecificationMapper.insert(standardSpecification);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、规范编号 " + vo.getSpecificationNumber() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldStandardSpecification, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    standardSpecificationMapper.updateById(oldStandardSpecification);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、规范编号 " + vo.getSpecificationNumber() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、规范编号 " + vo.getSpecificationNumber() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、规范编号 " + vo.getSpecificationNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(StandardSpecificationVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (HuatekTools.isEmpty(vo.getSpecificationType())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>规范类型不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getSpecificationNumber())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>规范编号不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getSpecificationName())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>规范名称不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getStatus())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>状态不能为空!");
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectStandardSpecificationListByIds(List<String> ids) {
        List<StandardSpecificationVO> standardSpecificationList = standardSpecificationMapper.selectStandardSpecificationListByIds(ids);

		TorchResponse<List<StandardSpecificationVO>> response = new TorchResponse<List<StandardSpecificationVO>>();
		response.getData().setData(standardSpecificationList);
		response.setStatus(200);
		response.getData().setCount((long)standardSpecificationList.size());
		return response;
    }



}
