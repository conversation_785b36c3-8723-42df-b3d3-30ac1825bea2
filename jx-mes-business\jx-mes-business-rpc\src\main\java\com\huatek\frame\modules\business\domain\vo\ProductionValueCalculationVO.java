package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.service.dto.ProdValCalcDetailsDTO;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.math.BigDecimal;
import java.io.Serializable;
import java.util.List;

/**
* @description 产值计算VO实体类
* <AUTHOR>
* @date 2025-08-22
**/
@Data
@ApiModel("产值计算VO实体类")
public class ProductionValueCalculationVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
	 * 工单编号
     **/
    @ApiModelProperty("工单编号")
    @Excel(name = "工单编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String workOrderNumber;

    /**
	 * 工单类型
     **/
    @ApiModelProperty("工单类型")
    @Excel(name = "工单类型",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String ticketType;

    /**
	 * 状态
     **/
    @ApiModelProperty("状态")
    @Excel(name = "状态",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String status;

    /**
	 * 类型
     **/
    @ApiModelProperty("类型")
    @Excel(name = "类型",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String type;

    /**
	 * 对账日期
     **/
    @ApiModelProperty("对账日期")
    @Excel(name = "对账日期",
        cellType = Excel.ColumnType.NUMERIC,
        dateFormat = "yyyy-MM-dd",
        type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date settlementDate;

    /**
	 * 产品名称
     **/
    @ApiModelProperty("产品名称")
    @Excel(name = "产品名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productName;

    /**
	 * 产品型号
     **/
    @ApiModelProperty("产品型号")
    @Excel(name = "产品型号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productModel;

    /**
	 * 生产厂家
     **/
    @ApiModelProperty("生产厂家")
    @Excel(name = "生产厂家",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String manufacturer;

    /**
	 * 批次号
     **/
    @ApiModelProperty("批次号")
    @Excel(name = "批次号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String batchNumber;

    /**
	 * 产品分类
     **/
    @ApiModelProperty("产品分类")
    @Excel(name = "产品分类",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productCategory;

    /**
	 * 产品资料名称
     **/
    @ApiModelProperty("产品资料名称")
    @Excel(name = "产品资料名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productInformationName;

    /**
	 * 产品资料
     **/
    @ApiModelProperty("产品资料")
    @Excel(name = "产品资料",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productInformation1;

    /**
	 * 委托单位
     **/
    @ApiModelProperty("委托单位")
    @Excel(name = "委托单位",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String entrustedUnit;

    /**
	 * 产品数量
     **/
    @ApiModelProperty("产品数量")
    @Excel(name = "产品数量",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Integer productQuantity;

    /**
	 * 收费标准名称
     **/
    @ApiModelProperty("收费标准名称")
    @Excel(name = "收费标准名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String chargingStandardName;

    /**
	 * 内部核算价格
     **/
    @ApiModelProperty("内部核算价格")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    @Excel(name = "内部核算价格",
        cellType = Excel.ColumnType.NUMERIC,
        type = Excel.Type.ALL)
    private BigDecimal internalAccountingPrice;

    /**
	 * 折扣
     **/
    @ApiModelProperty("折扣")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    @Excel(name = "折扣",
        cellType = Excel.ColumnType.NUMERIC,
        type = Excel.Type.ALL)
    private BigDecimal discount;

    /**
	 * 客户核算价格
     **/
    @ApiModelProperty("客户核算价格")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    @Excel(name = "客户核算价格",
        cellType = Excel.ColumnType.NUMERIC,
        type = Excel.Type.ALL)
    private BigDecimal customerAccountingPrice;

    /**
	 * 对账价格
     **/
    @ApiModelProperty("对账价格")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    @Excel(name = "对账价格",
        cellType = Excel.ColumnType.NUMERIC,
        type = Excel.Type.ALL)
    private BigDecimal settlementPrice;

    /**
	 * 内部价格分类
     **/
    @ApiModelProperty("内部价格分类")
    @Excel(name = "内部价格分类",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String internalPriceClassification;

    /**
	 * 客户价格分类
     **/
    @ApiModelProperty("客户价格分类")
    @Excel(name = "客户价格分类",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String customerPriceClassification;

    /**
	 * 订单编号
     **/
    @ApiModelProperty("订单编号")
    @Excel(name = "订单编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String orderNumber;

    /**
	 * 结算单位
     **/
    @ApiModelProperty("结算单位")
    @Excel(name = "结算单位",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String settlementUnit;

    /**
	 * 合格数
     **/
    @ApiModelProperty("合格数")
    @Excel(name = "合格数",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Long numberOfQualifiedProductions;

    /**
	 * 不合格数
     **/
    @ApiModelProperty("不合格数")
    @Excel(name = "不合格数",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Long numNonQualProd;

    /**
	 * 不合格工序
     **/
    @ApiModelProperty("不合格工序")
    @Excel(name = "不合格工序",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String nonQualityProcess;

    /**
	 * 试验类型
     **/
    @ApiModelProperty("试验类型")
    @Excel(name = "试验类型",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String testType;

    /**
	 * 委托日期
     **/
    @ApiModelProperty("委托日期")
    @Excel(name = "委托日期",
        cellType = Excel.ColumnType.NUMERIC,
        dateFormat = "yyyy-MM-dd",
        type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateOfEntrustment;

    /**
	 * 订单送检编号
     **/
    @ApiModelProperty("订单送检编号")
    @Excel(name = "订单送检编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String orderInspectionNumber;

    /**
	 * 工单送检编号
     **/
    @ApiModelProperty("工单送检编号")
    @Excel(name = "工单送检编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String workOrderInspectionNumber1;

    /**
	 * 对账单号
     **/
    @ApiModelProperty("对账单号")
    @Excel(name = "对账单号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String billStatementNumber;

    /**
	 * 合同编号
     **/
    @ApiModelProperty("合同编号")
    @Excel(name = "合同编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String contractNumber;

    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private String codexTorchUpdater;

    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;
}