<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ece3a5c4-1e66-4c67-89e5-01661dba50b2" name="Changes" comment="fix:bug">
      <change afterPath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target" afterDir="true" />
      <change beforePath="$PROJECT_DIR$/.idea/compiler.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/compiler.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/encodings.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/encodings.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/jarRepositories.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/jarRepositories.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/huatek-torch-job/torch-job-admin/src/main/resources/bootstrap-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/huatek-torch-job/torch-job-admin/src/main/resources/bootstrap-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/huatek-torch-job/torch-job-executor/src/main/resources/bootstrap-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/huatek-torch-job/torch-job-executor/src/main/resources/bootstrap-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/classes/com/huatek/job/admin/core/model/JobInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/classes/com/huatek/job/admin/core/model/JobInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/classes/com/huatek/job/admin/service/XxlJobForeignService.class" beforeDir="false" afterPath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/classes/com/huatek/job/admin/service/XxlJobForeignService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/torch-job-rpc-2.4.0.jar" beforeDir="false" afterPath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/torch-job-rpc-2.4.0.jar" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/doc/nacos/j-x-m-e-s/j-x-m-e-s-basic-application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/doc/nacos/j-x-m-e-s/j-x-m-e-s-basic-application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/doc/nacos/j-x-m-e-s/j-x-m-e-s-gateway-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/doc/nacos/j-x-m-e-s/j-x-m-e-s-gateway-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/doc/torch-basic-application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/doc/torch-basic-application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/resources/bootstrap-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/resources/bootstrap-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/jx-mes-data-permission-sdk/src/main/java/com/huatek/frame/modules/conf/RedissonConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/jx-mes-data-permission-sdk/src/main/java/com/huatek/frame/modules/conf/RedissonConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/rest/CompleteProductionOrderController.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/rest/CompleteProductionOrderController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/AwaitingProductionOrderServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/AwaitingProductionOrderServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/utils/IDCardExcelExporter.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/utils/IDCardExcelExporter.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-rpc/src/main/java/com/huatek/frame/modules/business/service/AwaitingProductionOrderService.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-rpc/src/main/java/com/huatek/frame/modules/business/service/AwaitingProductionOrderService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-gateway/doc/torch-gateway-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-gateway/doc/torch-gateway-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-gateway/src/main/resources/bootstrap-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-gateway/src/main/resources/bootstrap-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-openapi/target/jx-mes-openapi-1.0.0.jar" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-openapi/target/jx-mes-openapi-1.0.0.jar" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-tool/doc/compass-tool-application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-tool/doc/compass-tool-application-dev.yml" afterDir="false" />
    </list>
    <list id="640866c8-be23-4972-8da4-c676321340e3" name="ignore-on-commit" comment="">
      <change beforePath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/resources/bootstrap-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/resources/bootstrap-dev.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$PROJECT_DIR$/../../../../../../../../../../tools/mavenRep/com/huatek/torch/huatek-torch-common-core/2.1.9/huatek-torch-common-core-2.1.9.jar!/com/huatek/frame/common/context/SecurityContextHolder.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../../../../../../../../../tools/mavenRep/com/huatek/torch/huatek-torch-common-core/2.1.9/huatek-torch-common-core-2.1.9.jar!/com/huatek/frame/common/utils/SecurityUser.class" root0="SKIP_INSPECTION" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="C:\tools\mavenRep" />
        <option name="userSettingsFile" value="C:\tools\apache-maven-3.8.4-bin\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2zJXIm52poQbdHVZYIVkXliym0G" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.huatek-torch-job [clean].executor": "Run",
    "Maven.huatek-torch-job [install].executor": "Run",
    "Maven.jx-mes [clean].executor": "Run",
    "Maven.jx-mes [install].executor": "Run",
    "Maven.jx-mes [validate].executor": "Run",
    "Maven.jx-mes-business-rpc [clean].executor": "Run",
    "Maven.jx-mes-business-rpc [install].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "jdk.selected.JAVA_MODULE": "1.8",
    "last_opened_file_path": "C:/Work/C3S/compass/svn/JunXin/JunXin2025Code/trunk/02Engineering/04Implementation/jx-mes-code/jx-mes-business/jx-mes-business-application/resources",
    "project.structure.last.edited": "模块",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.4",
    "service.view.auto.scroll.to.source": "true",
    "settings.editor.selected.configurable": "editor.preferences.tabs",
    "应用程序.BasicApplication.executor": "Run",
    "应用程序.BusinessApplication.executor": "Debug",
    "应用程序.GatewayApplication.executor": "Run",
    "应用程序.JobAdminApplication.executor": "Run",
    "应用程序.JobExecutorApplication.executor": "Run",
    "应用程序.OutputValueReportServiceImpl.executor": "Run",
    "应用程序.ProductionValueExcelExport.executor": "Run"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\resources" />
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\src\main\java\com\huatek\frame\modules\business\rest" />
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\src\main\java\com\huatek\frame\modules\business\service\mapping" />
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-rpc\src\main\java\com\huatek\frame\modules\business\service\dto" />
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\src\main\java\com\huatek\frame\modules\business\service\impl" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\src\main\java\com\huatek\frame\modules\business" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="com.huatek.frame.modules.business.domain.vo" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.huatek.frame.modules.business.domain.vo" />
      <recent name="com.huatek.frame.modules.business.service.dto" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="Application" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="应用程序.GatewayApplication">
    <configuration default="true" type="Application" factoryName="Application">
      <shortenClasspath name="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BasicApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="MAIN_CLASS_NAME" value="com.huatek.frame.BasicApplication" />
      <module name="jx-mes-basic-application" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.huatek.frame.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BusinessApplication" type="Application" factoryName="Application">
      <option name="MAIN_CLASS_NAME" value="com.huatek.frame.BusinessApplication" />
      <module name="jx-mes-business-application" />
      <shortenClasspath name="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.huatek.frame.GatewayApplication" />
      <module name="jx-mes-gateway" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.huatek.frame.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="JobAdminApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.huatek.job.admin.JobAdminApplication" />
      <module name="torch-job-admin" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.huatek.job.admin.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="JobExecutorApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.huatek.job.executor.JobExecutorApplication" />
      <module name="torch-job-executor" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.huatek.job.executor.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="应用程序.BusinessApplication" />
      <item itemvalue="应用程序.GatewayApplication" />
      <item itemvalue="应用程序.BasicApplication" />
      <item itemvalue="应用程序.JobAdminApplication" />
      <item itemvalue="应用程序.JobExecutorApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.GatewayApplication" />
        <item itemvalue="应用程序.BasicApplication" />
        <item itemvalue="应用程序.JobExecutorApplication" />
        <item itemvalue="应用程序.JobAdminApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code" />
          <option name="myCopyRoot" value="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code" />
          <option name="myCopyRoot" value="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ece3a5c4-1e66-4c67-89e5-01661dba50b2" name="Changes" comment="" />
      <created>1751450073556</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751450073556</updated>
    </task>
    <task id="LOCAL-00003" summary="能力评审">
      <option name="closed" value="true" />
      <created>1754555935740</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1754555935740</updated>
    </task>
    <task id="LOCAL-00004" summary="能力评审-异常反馈">
      <option name="closed" value="true" />
      <created>1754562565051</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1754562565051</updated>
    </task>
    <task id="LOCAL-00005" summary="修改字符串类型">
      <option name="closed" value="true" />
      <created>1755078866749</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1755078866749</updated>
    </task>
    <task id="LOCAL-00006" summary="分单">
      <option name="closed" value="true" />
      <created>1755079226062</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1755079226062</updated>
    </task>
    <task id="LOCAL-00007" summary="外协申请外协审批通过，则状态变更为已外协，试验方式更新为外协&#10;外协验收,则状态变更为进行中">
      <option name="closed" value="true" />
      <created>1755080224564</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1755080224564</updated>
    </task>
    <task id="LOCAL-00008" summary="修改权限关键字">
      <option name="closed" value="true" />
      <created>1755081488728</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1755081488728</updated>
    </task>
    <task id="LOCAL-00009" summary="增加工序相关几个字段">
      <option name="closed" value="true" />
      <created>1755140814847</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1755140814847</updated>
    </task>
    <task id="LOCAL-00010" summary="修改名字">
      <option name="closed" value="true" />
      <created>1755164383222</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1755164383222</updated>
    </task>
    <task id="LOCAL-00011" summary="增加工序编码">
      <option name="closed" value="true" />
      <created>1755165750111</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1755165750111</updated>
    </task>
    <task id="LOCAL-00012" summary="修改字典">
      <option name="closed" value="true" />
      <created>1755166364932</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1755166364932</updated>
    </task>
    <task id="LOCAL-00013" summary="调度可看所有">
      <option name="closed" value="true" />
      <created>1755222598334</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1755222598334</updated>
    </task>
    <task id="LOCAL-00014" summary="注释掉子表的controller">
      <option name="closed" value="true" />
      <created>1755223679593</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1755223679593</updated>
    </task>
    <task id="LOCAL-00015" summary="调整位置">
      <option name="closed" value="true" />
      <created>1755223926027</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1755223926027</updated>
    </task>
    <task id="LOCAL-00016" summary="生产部门审批：对于任务审批人员展示与当前登录人所在班组负责设备的待审核的任务。&#10;可靠性部门审批：对于可靠性任务审批人员展示当前登录人被指派的待审核任务。">
      <option name="closed" value="true" />
      <created>1755228726935</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1755228726935</updated>
    </task>
    <task id="LOCAL-00017" summary="提交,审批通过,审批驳回">
      <option name="closed" value="true" />
      <created>1755237623392</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1755237623392</updated>
    </task>
    <task id="LOCAL-00018" summary="生产任务 详情增加几个子表查询">
      <option name="closed" value="true" />
      <created>1755238468165</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1755238468165</updated>
    </task>
    <task id="LOCAL-00019" summary="调用工单完成的接口">
      <option name="closed" value="true" />
      <created>1755240209657</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1755240209657</updated>
    </task>
    <task id="LOCAL-00020" summary="fix bug">
      <option name="closed" value="true" />
      <created>1755242689462</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1755242689463</updated>
    </task>
    <task id="LOCAL-00021" summary="硬编码改为常量">
      <option name="closed" value="true" />
      <created>1755244781558</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1755244781558</updated>
    </task>
    <task id="LOCAL-00022" summary="新增订单编号">
      <option name="closed" value="true" />
      <created>1755574127884</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1755574127884</updated>
    </task>
    <task id="LOCAL-00023" summary="增加name查询">
      <option name="closed" value="true" />
      <created>1755583376327</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1755583376327</updated>
    </task>
    <task id="LOCAL-00024" summary="增加name查询">
      <option name="closed" value="true" />
      <created>1755583610502</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1755583610502</updated>
    </task>
    <task id="LOCAL-00025" summary="修改bug">
      <option name="closed" value="true" />
      <created>1755588045940</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1755588045941</updated>
    </task>
    <task id="LOCAL-00026" summary="修改bug">
      <option name="closed" value="true" />
      <created>1755588188443</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1755588188443</updated>
    </task>
    <task id="LOCAL-00027" summary="增加jx-mes-business-rpc">
      <option name="closed" value="true" />
      <created>1755683654791</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1755683654791</updated>
    </task>
    <task id="LOCAL-00028" summary="修复定时任务">
      <option name="closed" value="true" />
      <created>1755757706659</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1755757706659</updated>
    </task>
    <task id="LOCAL-00029" summary="异常反馈增加工单编号">
      <option name="closed" value="true" />
      <created>1755767643396</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1755767643396</updated>
    </task>
    <task id="LOCAL-00030" summary="修改bug">
      <option name="closed" value="true" />
      <created>1755854695626</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1755854695626</updated>
    </task>
    <task id="LOCAL-00031" summary="修改bug">
      <option name="closed" value="true" />
      <created>1755854707794</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1755854707794</updated>
    </task>
    <task id="LOCAL-00032" summary="增加对账日期">
      <option name="closed" value="true" />
      <created>1755855277063</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1755855277063</updated>
    </task>
    <task id="LOCAL-00033" summary="核算">
      <option name="closed" value="true" />
      <created>1756122335567</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1756122335567</updated>
    </task>
    <task id="LOCAL-00034" summary="发消息">
      <option name="closed" value="true" />
      <created>1756189391689</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1756189391689</updated>
    </task>
    <task id="LOCAL-00035" summary="发消息">
      <option name="closed" value="true" />
      <created>1756189461756</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1756189461756</updated>
    </task>
    <task id="LOCAL-00036" summary="生成核算数据">
      <option name="closed" value="true" />
      <created>1756194247170</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1756194247170</updated>
    </task>
    <task id="LOCAL-00037" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1756197036809</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1756197036809</updated>
    </task>
    <task id="LOCAL-00038" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1756197953005</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1756197953005</updated>
    </task>
    <task id="LOCAL-00039" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1756212382615</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1756212382615</updated>
    </task>
    <task id="LOCAL-00040" summary="获取技术能力 去掉注释">
      <option name="closed" value="true" />
      <created>1756213021044</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1756213021045</updated>
    </task>
    <task id="LOCAL-00041" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1756263315876</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1756263315876</updated>
    </task>
    <task id="LOCAL-00042" summary="自适应表头">
      <option name="closed" value="true" />
      <created>1756350713575</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1756350713575</updated>
    </task>
    <task id="LOCAL-00043" summary="产值报表定时任务">
      <option name="closed" value="true" />
      <created>1756372901422</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1756372901422</updated>
    </task>
    <task id="LOCAL-00044" summary="遗留赋值问题">
      <option name="closed" value="true" />
      <created>1756374207550</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1756374207550</updated>
    </task>
    <task id="LOCAL-00045" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1756446945445</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1756446945445</updated>
    </task>
    <task id="LOCAL-00046" summary="更新ProductList和ProductManagement的产品分类">
      <option name="closed" value="true" />
      <created>1756447446147</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1756447446147</updated>
    </task>
    <task id="LOCAL-00047" summary="生产任务产品资料 product_information_management || standard_specification">
      <option name="closed" value="true" />
      <created>1756451081635</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1756451081635</updated>
    </task>
    <task id="LOCAL-00048" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1756451804266</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1756451804266</updated>
    </task>
    <task id="LOCAL-00049" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1756727955970</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1756727955970</updated>
    </task>
    <task id="LOCAL-00050" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1756796152955</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1756796152956</updated>
    </task>
    <task id="LOCAL-00051" summary="fix:bug">
      <option name="closed" value="true" />
      <created>1756799740333</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1756799740333</updated>
    </task>
    <option name="localTasksCounter" value="52" />
    <servers />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="注释掉子表的controller" />
    <MESSAGE value="调整位置" />
    <MESSAGE value="生产部门审批：对于任务审批人员展示与当前登录人所在班组负责设备的待审核的任务。&#10;可靠性部门审批：对于可靠性任务审批人员展示当前登录人被指派的待审核任务。" />
    <MESSAGE value="提交,审批通过,审批驳回" />
    <MESSAGE value="生产任务 详情增加几个子表查询" />
    <MESSAGE value="调用工单完成的接口" />
    <MESSAGE value="fix bug" />
    <MESSAGE value="硬编码改为常量" />
    <MESSAGE value="新增订单编号" />
    <MESSAGE value="增加name查询" />
    <MESSAGE value="增加jx-mes-business-rpc" />
    <MESSAGE value="修复定时任务" />
    <MESSAGE value="异常反馈增加工单编号" />
    <MESSAGE value="修改bug" />
    <MESSAGE value="增加对账日期" />
    <MESSAGE value="核算" />
    <MESSAGE value="发消息" />
    <MESSAGE value="生成核算数据" />
    <MESSAGE value="获取技术能力 去掉注释" />
    <MESSAGE value="自适应表头" />
    <MESSAGE value="产值报表定时任务" />
    <MESSAGE value="遗留赋值问题" />
    <MESSAGE value="更新ProductList和ProductManagement的产品分类" />
    <MESSAGE value="生产任务产品资料 product_information_management || standard_specification" />
    <MESSAGE value="fix:bug" />
    <option name="LAST_COMMIT_MESSAGE" value="fix:bug" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/huatek-torch-job/torch-job-core/src/main/java/com/huatek/job/core/executor/XxlJobExecutor.java</url>
          <line>67</line>
          <option name="timeStamp" value="23" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/huatek-torch-job/torch-job-executor/src/main/java/com/huatek/job/executor/service/jobhandler/SampleXxlJob.java</url>
          <line>52</line>
          <option name="timeStamp" value="24" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/common/utils/ProductionValueExcelExport.java</url>
          <line>104</line>
          <option name="timeStamp" value="42" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="Application">
        <watch expression="QueryWrapper wrapper = new QueryWrapper();&#10;&#9;&#9;&#9;wrapper.eq(&quot;work_order&quot;,productionOrderDTO.getId());&#10;&#9;&#9;&#9;CustomerProcessScheme scheme = customerProcessSchemeMapper.selectOne(wrapper);&#10;&#9;&#9;&#9;CustomerExperimentProjectDTO customerExperimentProjectDTO = new CustomerExperimentProjectDTO();&#10;&#9;&#9;&#9;customerExperimentProjectDTO.setCodexTorchMasterFormId(scheme.getId());&#10;&#9;&#9;&#9;wrapper.eq(&quot;CODEX_TORCH_MASTER_FORM_ID&quot;,scheme.getId());&#10;&#9;&#9;&#9;List&lt;CustomerExperimentProjectVO&gt; customerEperimentProjects= customerExperimentProjectMapper.selectCustomerExperimentProjectList(customerExperimentProjectDTO);&#10;&#9;&#9;&#9;" />
      </configuration>
    </watches-manager>
  </component>
</project>