package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.domain.ProductionValueCalculation;
import com.huatek.frame.modules.business.domain.vo.ProductionValueCalculationDuiZhangVO;
import com.huatek.frame.modules.business.domain.vo.ProductionValueCalculationVO;
import com.huatek.frame.modules.business.mapper.ProductionValueCalculationMapper;
import com.huatek.frame.modules.business.service.ProductionValueCalculationService;
import com.huatek.frame.modules.business.service.dto.ProductionValueCalculationContractDTO;
import com.huatek.frame.modules.business.service.dto.ProductionValueCalculationDTO;
import com.huatek.frame.modules.business.service.dto.ProductionValueCalculationDuiZhangDTO;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.ProductionOrder;
import com.huatek.frame.modules.business.domain.EvaluationOrder;
import com.huatek.frame.modules.business.domain.CustomerInformationManagement;
import com.huatek.frame.modules.business.domain.ProductList;
import com.huatek.frame.modules.business.domain.StandardSpecification;
import com.huatek.frame.modules.business.domain.CustomerProcessScheme;
import com.huatek.frame.modules.business.domain.vo.CustomerExperimentProjectVO;
import com.huatek.frame.modules.business.service.dto.CustomerExperimentProjectDTO;
import com.huatek.frame.modules.business.mapper.AwaitingProductionOrderMapper;
import com.huatek.frame.modules.business.mapper.EvaluationOrderMapper;
import com.huatek.frame.modules.business.mapper.CustomerInformationManagementMapper;
import com.huatek.frame.modules.business.mapper.ProductListMapper;
import com.huatek.frame.modules.business.mapper.StandardSpecificationMapper;
import com.huatek.frame.modules.business.mapper.CustomerProcessSchemeMapper;
import com.huatek.frame.modules.business.mapper.CustomerExperimentProjectMapper;
import com.huatek.frame.modules.business.mapper.AccountingStandardMapper;
import com.huatek.frame.modules.business.mapper.AccountingStandardDetailsMapper;
import com.huatek.frame.modules.business.domain.AccountingStandard;
import com.huatek.frame.modules.business.domain.AccountingStandardDetails;
import org.springframework.util.CollectionUtils;



/**
 * 产值计算 ServiceImpl
 * <AUTHOR>
 * @date 2025-08-22
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "productionValueCalculation")
//@RefreshScope
@Slf4j
public class ProductionValueCalculationServiceImpl implements ProductionValueCalculationService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private ProductionValueCalculationMapper productionValueCalculationMapper;
    
    @Autowired
    private AwaitingProductionOrderMapper awaitingProductionOrderMapper;

    @Autowired
    private ProductListMapper productListMapper;

    @Autowired
    private CustomerProcessSchemeMapper customerProcessSchemeMapper;

    @Autowired
    private CustomerExperimentProjectMapper customerExperimentProjectMapper;

    @Autowired
    private AccountingStandardMapper accountingStandardMapper;

    @Autowired
    private AccountingStandardDetailsMapper accountingStandardDetailsMapper;

    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	public ProductionValueCalculationServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<ProductionValueCalculationVO>> findProductionValueCalculationPage(ProductionValueCalculationDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<ProductionValueCalculationVO> productionValueCalculations = productionValueCalculationMapper.selectProductionValueCalculationPage(dto);
		TorchResponse<List<ProductionValueCalculationVO>> response = new TorchResponse<List<ProductionValueCalculationVO>>();
		response.getData().setData(productionValueCalculations);
		response.setStatus(200);
		response.getData().setCount(productionValueCalculations.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(ProductionValueCalculationDTO productionValueCalculationDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(productionValueCalculationDto.getCodexTorchDeleted())) {
            productionValueCalculationDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = productionValueCalculationDto.getId();
		ProductionValueCalculation entity = new ProductionValueCalculation();
        BeanUtils.copyProperties(productionValueCalculationDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
			productionValueCalculationMapper.insert(entity);
		} else {
			productionValueCalculationMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        ProductionValueCalculationVO vo = new ProductionValueCalculationVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<ProductionValueCalculationVO> findProductionValueCalculation(String id) {
		ProductionValueCalculationVO vo = new ProductionValueCalculationVO();
		if (!HuatekTools.isEmpty(id)) {
			ProductionValueCalculation entity = productionValueCalculationMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<ProductionValueCalculationVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
	    List<ProductionValueCalculation> productionValueCalculationList = productionValueCalculationMapper.selectBatchIds(Arrays.asList(ids));
        for (ProductionValueCalculation productionValueCalculation : productionValueCalculationList) {
            productionValueCalculation.setCodexTorchDeleted(Constant.DEFAULT_YES);
            productionValueCalculationMapper.updateById(productionValueCalculation);
        }
		//productionValueCalculationMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "production_value_calculation", convertorFields = "status,type,testType")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<ProductionValueCalculationVO> selectProductionValueCalculationList(ProductionValueCalculationDTO dto) {
        return productionValueCalculationMapper.selectProductionValueCalculationList(dto);
    }

    /**
     * 对账结果数据批量导入
     *
     * @param productionValueCalculationList 对账结果数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "production_value_calculation", convertorFields = "status,type,testType")
    public TorchResponse importProductionValueCalculation(List<ProductionValueCalculationDuiZhangVO> productionValueCalculationList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(productionValueCalculationList) || productionValueCalculationList.size() == 0) {
            throw new ServiceException("对账结果数据批量导入不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (ProductionValueCalculationDuiZhangVO vo : productionValueCalculationList) {
            try {
                // 1. 验证所有必填字段不能为空
                if (validateRequiredFields(vo, failureNum, failureMsg)) {
                    failureNum++;
                    continue;
                }

                // 2. 根据工单编号查询现有数据
                ProductionValueCalculation existingRecord = findExistingRecordByWorkOrder(vo.getWorkOrderNumber());
                if (existingRecord == null) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、工单编号 ").append(vo.getWorkOrderNumber()).append(" 不存在，无法更新");
                    continue;
                }

                // 3. 验证type字段必须等于客户标准
                if (!validateCustomerStandard(existingRecord, failureNum, failureMsg)) { failureNum++; continue; }

                // 4. 执行更新操作
                updateDuizhangs(vo);


                successNum++;
                successMsg.append("<br/>").append(successNum).append("、工单编号 ").append(vo.getWorkOrderNumber()).append(" 更新成功");

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、工单编号 " + vo.getWorkOrderNumber() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error(msg, e);
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private void updateDuizhangs(ProductionValueCalculationDuiZhangVO vo) {
        UpdateWrapper<ProductionValueCalculation> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("work_order_number", vo.getWorkOrderNumber());
        updateWrapper.set("status", DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_YES);
        updateWrapper.set("settlement_price", vo.getSettlementPrice());
        updateWrapper.set("bill_statement_number", vo.getBillStatementNumber());
        updateWrapper.set("settlement_date", new Date());
        updateWrapper.set("codex_torch_updater", SecurityContextHolder.getCurrentUserId());
        updateWrapper.set("codex_torch_update_datetime", new Timestamp(System.currentTimeMillis()));
        productionValueCalculationMapper.update(null, updateWrapper);
    }

    /**
     * 验证必填字段
     * @param vo 对账VO对象
     * @param failureNum 失败数量
     * @param failureMsg 失败消息
     * @return true表示验证失败，false表示验证通过
     */
    private boolean validateRequiredFields(ProductionValueCalculationDuiZhangVO vo, int failureNum, StringBuilder failureMsg) {
        StringBuilder errorMsg = new StringBuilder();

        if (StringUtils.isEmpty(vo.getWorkOrderNumber())) {
            errorMsg.append("工单编号不能为空；");
        }
        if (vo.getSettlementPrice() == null) {
            errorMsg.append("对账价格不能为空；");
        }
        if (StringUtils.isEmpty(vo.getBillStatementNumber())) {
            errorMsg.append("对账单号不能为空；");
        }

        if (errorMsg.length() > 0) {
            failureMsg.append("<br/>").append(failureNum + 1).append("、工单编号 ").append(vo.getWorkOrderNumber()).append(" 验证失败：").append(errorMsg.toString());
            return true;
        }
        return false;
    }

    /**
     * 根据工单编号查询现有记录
     * @param workOrderNumber 工单编号
     * @return 现有记录，如果不存在返回null
     */
    private ProductionValueCalculation findExistingRecordByWorkOrder(String workOrderNumber) {
        if (StringUtils.isEmpty(workOrderNumber)) {
            return null;
        }

        QueryWrapper<ProductionValueCalculation> wrapper = new QueryWrapper<>();
        wrapper.eq("work_order_number", workOrderNumber);
        wrapper.eq("codex_torch_deleted", "0");

        List<ProductionValueCalculation> existingRecords = productionValueCalculationMapper.selectList(wrapper);
        if (!CollectionUtils.isEmpty(existingRecords)) {
            return existingRecords.get(0);
        }
        return null;
    }

    /**
     * 验证客户标准信息
     * @param existingRecord
     * @param failureNum 失败数量
     * @param failureMsg 失败消息
     * @return true表示验证通过，false表示验证失败
     */
    private boolean validateCustomerStandard(ProductionValueCalculation existingRecord, int failureNum, StringBuilder failureMsg) {
        try {
            // 验证type字段必须等于客户标准
            if (!DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_KEHU.equals(existingRecord.getType())) {
                failureMsg.append("<br/>").append(failureNum + 1).append("、工单编号 ").append(existingRecord.getWorkOrderNumber()).append(" 的类型不是客户标准，无法进行对账");
                return false;
            }

            return true;
        } catch (Exception e) {
            failureMsg.append("<br/>").append(failureNum + 1).append("、工单编号 ").append(existingRecord.getWorkOrderNumber()).append(" 客户标准验证失败：").append(e.getMessage());
            log.error("验证客户标准失败", e);
            return false;
        }
    }

    private Boolean linkedDataValidityVerification(ProductionValueCalculationVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectProductionValueCalculationListByIds(List<String> ids) {
        List<ProductionValueCalculationVO> productionValueCalculationList = productionValueCalculationMapper.selectProductionValueCalculationListByIds(ids);

		TorchResponse<List<ProductionValueCalculationVO>> response = new TorchResponse<List<ProductionValueCalculationVO>>();
		response.getData().setData(productionValueCalculationList);
		response.setStatus(200);
		response.getData().setCount((long)productionValueCalculationList.size());
		return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse duiZhang(ProductionValueCalculationDuiZhangDTO productionValueCalculationDuiZhangDTO) {

        List<String> ids = productionValueCalculationDuiZhangDTO.getIds();
        if (HuatekTools.isEmpty(ids)) {

                throw new ServiceException("数据不存在");

        }

        int successCount = 0;
        List<String> failedWorkOrders = new ArrayList<>();
        StringBuilder failureDetails = new StringBuilder();

        for (String id : ids) {
            try {
                ProductionValueCalculation productionValueCalculation = productionValueCalculationMapper.selectById(id);
                if (HuatekTools.isEmpty(productionValueCalculation)) {
                    failedWorkOrders.add("ID:" + id);
                    failureDetails.append("ID:").append(id).append(" 数据不存在; ");
                    continue;
                }

                if (!productionValueCalculation.getType().equals(DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_KEHU)) {
                    failedWorkOrders.add(productionValueCalculation.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(productionValueCalculation.getWorkOrderNumber()).append(" 类型为君信标准，不可对账; ");
                    continue;
                }

                UpdateWrapper<ProductionValueCalculation> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("id", id);
                updateWrapper.set("status", DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_YES);
                updateWrapper.set("settlement_price", productionValueCalculationDuiZhangDTO.getSettlementPrice());
                updateWrapper.set("bill_statement_number", productionValueCalculationDuiZhangDTO.getBillStatementNumber());
                updateWrapper.set("settlement_date", new Date());
                updateWrapper.set("codex_torch_updater", SecurityContextHolder.getCurrentUserId());
                updateWrapper.set("codex_torch_update_datetime", new Timestamp(System.currentTimeMillis()));
                productionValueCalculationMapper.update(null, updateWrapper);

                successCount++;
            } catch (Exception e) {
                ProductionValueCalculation productionValueCalculation = productionValueCalculationMapper.selectById(id);
                String workOrderNumber = productionValueCalculation != null ? productionValueCalculation.getWorkOrderNumber() : "ID:" + id;
                failedWorkOrders.add(workOrderNumber);
                failureDetails.append("工单编号:").append(workOrderNumber).append(" 对账失败:").append(e.getMessage()).append("; ");
                log.error("对账失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            }
        }

        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);

        // 根据操作结果返回不同的消息
        if (ids.size() == 1) {
            // 单个操作
            if (successCount == 1) {
                response.setMessage("对账成功");
            } else {
                response.setMessage("对账失败");
                response.setStatus(Constant.REQUEST_SUCCESS);
            }
        } else {
            // 批量操作
            if (failedWorkOrders.isEmpty()) {
                response.setMessage("批量对账成功，共处理 " + successCount + " 条记录");
            } else {
                response.setMessage("批量对账完成，成功 " + successCount + " 条，失败 " + failedWorkOrders.size() + " 条。失败工单编号: " + String.join(", ", failedWorkOrders));
            }
        }

        log.info(failureDetails.toString());

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse updateContract(ProductionValueCalculationContractDTO productionValueCalculationContractDTO) {
        // 获取ID列表，支持批量操作和单个操作
        List<String> ids = productionValueCalculationContractDTO.getIds();
        if (HuatekTools.isEmpty(ids)) {

                throw new ServiceException("数据不存在");

        }

        int successCount = 0;
        List<String> failedWorkOrders = new ArrayList<>();
        StringBuilder failureDetails = new StringBuilder();

        for (String id : ids) {
            try {
                ProductionValueCalculation productionValueCalculation = productionValueCalculationMapper.selectById(id);
                if (HuatekTools.isEmpty(productionValueCalculation)) {
                    failedWorkOrders.add("ID:" + id);
                    failureDetails.append("ID:").append(id).append(" 数据不存在; ");
                    continue;
                }

                if (!productionValueCalculation.getType().equals(DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_KEHU)) {
                    failedWorkOrders.add(productionValueCalculation.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(productionValueCalculation.getWorkOrderNumber()).append(" 类型为君信标准，不可录入合同; ");
                    continue;
                }

                if (!productionValueCalculation.getStatus().equals(DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_YES)) {
                    failedWorkOrders.add(productionValueCalculation.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(productionValueCalculation.getWorkOrderNumber()).append(" 状态为未对账，不可录入合同; ");
                    continue;
                }

                UpdateWrapper<ProductionValueCalculation> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("id", id);
                updateWrapper.set("contract_number", productionValueCalculationContractDTO.getContractNumber());
                updateWrapper.set("codex_torch_updater", SecurityContextHolder.getCurrentUserId());
                updateWrapper.set("codex_torch_update_datetime", new Timestamp(System.currentTimeMillis()));
                productionValueCalculationMapper.update(null, updateWrapper);

                successCount++;
            } catch (Exception e) {
                ProductionValueCalculation productionValueCalculation = productionValueCalculationMapper.selectById(id);
                String workOrderNumber = productionValueCalculation != null ? productionValueCalculation.getWorkOrderNumber() : "ID:" + id;
                failedWorkOrders.add(workOrderNumber);
                failureDetails.append("工单编号:").append(workOrderNumber).append(" 合同录入失败:").append(e.getMessage()).append("; ");
                log.error("合同录入失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            }
        }

        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);

        // 根据操作结果返回不同的消息
        if (ids.size() == 1) {
            // 单个操作
            if (successCount == 1) {
                response.setMessage("合同录入成功");
            } else {
                response.setMessage("合同录入失败");
                response.setStatus(Constant.REQUEST_SUCCESS);
            }
        } else {
            // 批量操作
            if (failedWorkOrders.isEmpty()) {
                response.setMessage("批量合同录入成功，共处理 " + successCount + " 条记录");
            } else {
                response.setMessage("批量合同录入完成，成功 " + successCount + " 条，失败 " + failedWorkOrders.size() + " 条。失败工单编号: " + String.join(", ", failedWorkOrders));
            }
        }

        log.info(failureDetails.toString());
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse generateCalculation() throws Exception {
        //查询production_order is_calculation='0'的数据
        List<ProductionOrder> productionOrderList = awaitingProductionOrderMapper.selectList(new QueryWrapper<ProductionOrder>()
                .eq("is_calculation", DicConstant.CommonDic.DEFAULT_ZERO)
                .eq("codex_torch_deleted", Constant.DEFAULT_NO)
        );

        if (CollectionUtils.isEmpty(productionOrderList)) {
            TorchResponse response = new TorchResponse();
            response.setStatus(Constant.REQUEST_SUCCESS);
            response.setMessage("没有需要生成核算的工单");
            return response;
        }

        int successCount = 0;
        int failureCount = 0;
        StringBuilder failureDetails = new StringBuilder();

        for (ProductionOrder productionOrder : productionOrderList) {
            try {
                // 获取工单相关信息
                ProductList productList = productListMapper.selectById(productionOrder.getProduct());
                if (productList == null) {
                    failureCount++;
                    failureDetails.append("工单编号:").append(productionOrder.getWorkOrderNumber()).append(" 产品信息不存在; ");
                    continue;
                }

                // 获取客户工序方案信息
                QueryWrapper<CustomerProcessScheme> schemeWrapper = new QueryWrapper<>();
                schemeWrapper.eq("work_order", productionOrder.getId());
                CustomerProcessScheme scheme = customerProcessSchemeMapper.selectOne(schemeWrapper);
                if (scheme == null) {
                    failureCount++;
                    failureDetails.append("工单编号:").append(productionOrder.getWorkOrderNumber()).append(" 客户工序方案不存在; ");
                    continue;
                }

                // 获取试验类型
                String testType = productList.getTestType();

                // 获取工序信息 - 参考issueProductionOrderTask的逻辑
                CustomerExperimentProjectDTO customerExperimentProjectDTO = new CustomerExperimentProjectDTO();
                customerExperimentProjectDTO.setCodexTorchMasterFormId(scheme.getId());
                List<CustomerExperimentProjectVO> customerExperimentProjects = customerExperimentProjectMapper.selectCustomerExperimentProjectList(customerExperimentProjectDTO);

                if (CollectionUtils.isEmpty(customerExperimentProjects)) {
                    failureCount++;
                    failureDetails.append("工单编号:").append(productionOrder.getWorkOrderNumber()).append(" 客户试验项目不存在; ");
                    continue;
                }

                // 为每个工序生成核算记录
                for (CustomerExperimentProjectVO experimentProject : customerExperimentProjects) {
                    String processId = experimentProject.getProcessCode3();

                    // 生成两条核算记录：君信标准和客户标准
                    generateCalculationRecords(productionOrder, productList, scheme, testType, processId, experimentProject);
                }

                // 更新工单的is_calculation状态
                UpdateWrapper<ProductionOrder> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("id", productionOrder.getId());
                updateWrapper.set("is_calculation", DicConstant.CommonDic.DEFAULT_ONE);
                updateWrapper.set("codex_torch_updater", SecurityContextHolder.getCurrentUserId());
                updateWrapper.set("codex_torch_update_datetime", new Timestamp(System.currentTimeMillis()));
                awaitingProductionOrderMapper.update(null, updateWrapper);

                successCount++;
            } catch (Exception e) {
                failureCount++;
                failureDetails.append("工单编号:").append(productionOrder.getWorkOrderNumber()).append(" 生成核算失败:").append(e.getMessage()).append("; ");
                log.error("生成核算失败，工单ID: {}, 错误: {}", productionOrder.getId(), e.getMessage(), e);
            }
        }

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);

        if (failureCount == 0) {
            response.setMessage("核算信息生成成功，共处理 " + successCount + " 个工单");
        } else {
            response.setMessage("核算信息生成完成，成功 " + successCount + " 个工单，失败 " + failureCount + " 个工单。失败详情: " + failureDetails.toString());
        }

        return response;
    }



    /**
     * 生成核算记录（君信标准和客户标准各一条）
     */
    private void generateCalculationRecords(ProductionOrder productionOrder, ProductList productList,
                                          CustomerProcessScheme scheme, String testType, String processId,
                                          CustomerExperimentProjectVO experimentProject) {

        // 生成君信标准记录
        ProductionValueCalculation junxinRecord = createBaseCalculationRecord(productionOrder, productList, scheme, testType, processId, experimentProject);
        junxinRecord.setType(DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_TYPE_JUNXIN);

        // 查找君信标准的核算标准并计算价格 - 使用产品分类作为内部分类
        calculatePriceByStandard(junxinRecord, testType, processId, productList.getProductCategory(), true);

        productionValueCalculationMapper.insert(junxinRecord);

        // 生成客户标准记录
        ProductionValueCalculation customerRecord = createBaseCalculationRecord(productionOrder, productList, scheme, testType, processId, experimentProject);
        customerRecord.setType(DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_KEHU);

        // 查找客户标准的核算标准并计算价格 - 使用产品分类作为客户器件分类
        calculatePriceByStandard(customerRecord, testType, processId, productList.getProductCategory(), false);

        productionValueCalculationMapper.insert(customerRecord);
    }

    /**
     * 创建基础核算记录
     */
    private ProductionValueCalculation createBaseCalculationRecord(ProductionOrder productionOrder, ProductList productList,
                                                                 CustomerProcessScheme scheme, String testType, String processId,
                                                                 CustomerExperimentProjectVO experimentProject) {
        ProductionValueCalculation record = new ProductionValueCalculation();

        // 基础信息
        record.setWorkOrderNumber(productionOrder.getWorkOrderNumber());
        record.setOrderNumber(productionOrder.getOrderNumber());
        record.setProductName(productList.getProductName());
        record.setProductModel(productList.getProductModel());
        record.setBatchNumber(productList.getProductionBatch());
        record.setProductCategory(productList.getProductCategory());
        record.setProductInformationName(productList.getProductName());
        record.setEntrustedUnit(scheme.getEntrustedUnit());
        record.setProductQuantity(productionOrder.getQuantity());
        record.setTestType(testType);
        record.setStatus(DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_NO); // 未对账

        return record;
    }

    /**
     * 根据核算标准计算价格
     * @param record 核算记录
     * @param testType 试验类型
     * @param processId 工序ID
     * @param productCategory 产品分类（用作内部分类或客户器件分类）
     * @param isInternal 是否为内部标准
     */
    private void calculatePriceByStandard(ProductionValueCalculation record, String testType, String processId,
                                        String productCategory, boolean isInternal) {
        try {
            // 1. 根据试验类型和收费标准类型查询主表accounting_standard
            String chargeStandardType = isInternal ? DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_TYPE_JUNXIN :
                                                   DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_KEHU;

            QueryWrapper<AccountingStandard> standardWrapper = new QueryWrapper<>();
            standardWrapper.eq("test_type", testType);
            standardWrapper.eq("charge_standard_type", chargeStandardType);
            standardWrapper.eq("codex_torch_deleted", Constant.DEFAULT_NO);
            standardWrapper.orderByDesc("codex_torch_create_datetime"); // 获取最新的标准

            List<AccountingStandard> accountingStandards = accountingStandardMapper.selectList(standardWrapper);

            if (CollectionUtils.isEmpty(accountingStandards)) {
                log.warn("未找到匹配的核算标准，试验类型: {}, 标准类型: {}", testType, chargeStandardType);
                setDefaultPrice(record,productCategory, isInternal);
                return;
            }

            AccountingStandard accountingStandard = accountingStandards.get(0); // 取最新的标准

            // 2. 根据工序id和产品分类查询子表accounting_standard_details
            QueryWrapper<AccountingStandardDetails> detailsWrapper = new QueryWrapper<>();
            detailsWrapper.eq("charge_standard_number", accountingStandard.getChargeStandardNumber());
            detailsWrapper.eq("experiment_project", processId);
            detailsWrapper.eq("product_category", productCategory); // 产品分类使用产品分类
            detailsWrapper.eq("codex_torch_deleted", Constant.DEFAULT_NO);

            List<AccountingStandardDetails> standardDetails = accountingStandardDetailsMapper.selectList(detailsWrapper);

            if (CollectionUtils.isEmpty(standardDetails)) {
                log.warn("未找到匹配的核算标准明细，收费标准编号: {}, 工序ID: {}, 产品分类: {}",
                        accountingStandard.getChargeStandardNumber(), processId, record.getProductCategory());
                setDefaultPrice(record,productCategory, isInternal);
                return;
            }

            AccountingStandardDetails standardDetail = standardDetails.get(0);

            // 3. 计算价格
            BigDecimal calculatedPrice = calculatePrice(standardDetail, record.getProductQuantity());

            // 4. 应用折扣
            if (accountingStandard.getDiscount() != null && accountingStandard.getDiscount().compareTo(BigDecimal.ZERO) > 0) {
                calculatedPrice = calculatedPrice.multiply(accountingStandard.getDiscount());
            }

            // 5. 设置计算结果
            if (isInternal) {
                record.setInternalPriceClassification(productCategory); // 内部分类使用产品分类
                record.setInternalAccountingPrice(calculatedPrice);
                record.setChargingStandardName(accountingStandard.getChargingStandardName());
            } else {
                record.setCustomerPriceClassification(productCategory); // 客户器件分类使用产品分类
                record.setCustomerAccountingPrice(calculatedPrice);
                record.setChargingStandardName(accountingStandard.getChargingStandardName());
                record.setDiscount(accountingStandard.getDiscount());
            }

        } catch (Exception e) {
            log.error("计算价格失败，试验类型: {}, 工序ID: {}, 是否内部标准: {}", testType, processId, isInternal, e);
            setDefaultPrice(record, productCategory, isInternal);
        }
    }

    /**
     * 设置默认价格
     */
    private void setDefaultPrice(ProductionValueCalculation record, String productCategory, boolean isInternal) {
        if (isInternal) {
            record.setInternalPriceClassification(productCategory); // 内部分类使用产品分类
            record.setInternalAccountingPrice(BigDecimal.ZERO);
            record.setChargingStandardName("君信标准");
        } else {
            record.setCustomerPriceClassification(productCategory); // 客户器件分类使用产品分类
            record.setCustomerAccountingPrice(BigDecimal.ZERO);
            record.setChargingStandardName("客户标准");
        }
    }

    /**
     * 根据标准明细和数量计算价格
     */
    private BigDecimal calculatePrice(AccountingStandardDetails standardDetail, Integer productQuantity) {
        BigDecimal basePrice = standardDetail.getBasePrice();
        if (basePrice == null) {
            return BigDecimal.ZERO;
        }

        // 检查最低数量要求
        Integer minimumQuantity = standardDetail.getMinimumQuantityRequirement();
        if (minimumQuantity != null && productQuantity < minimumQuantity) {
            // 低于要求数量，使用计算倍数
            BigDecimal multiplier = standardDetail.getCfbrq();
            if (multiplier != null && multiplier.compareTo(BigDecimal.ZERO) > 0) {
                return basePrice.multiply(multiplier);
            }
        }

        // 根据收费形式计算
        String chargingMethod = standardDetail.getChargingMethod();
        if ("按件收费".equals(chargingMethod)) {
            return basePrice.multiply(new BigDecimal(productQuantity));
        } else if ("按批收费".equals(chargingMethod)) {
            return basePrice; // 按批收费不考虑数量
        } else {
            // 默认按件收费
            return basePrice.multiply(new BigDecimal(productQuantity));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse recalculateCustomerPrice(List<String> ids) {
        if (HuatekTools.isEmpty(ids)) {
            throw new ServiceException("数据不存在");
        }

        int successCount = 0;
        List<String> failedWorkOrders = new ArrayList<>();
        StringBuilder failureDetails = new StringBuilder();

        for (String id : ids) {
            try {
                ProductionValueCalculation record = productionValueCalculationMapper.selectById(id);
                if (HuatekTools.isEmpty(record)) {
                    failedWorkOrders.add("ID:" + id);
                    failureDetails.append("ID:").append(id).append(" 数据不存在; ");
                    continue;
                }

                // 验证类型必须为客户标准
                if (!DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_KEHU.equals(record.getType())) {
                    failedWorkOrders.add(record.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(record.getWorkOrderNumber()).append(" 类型不是客户标准，无法重新核算客户价格; ");
                    continue;
                }

                // 重新计算客户价格
                BigDecimal newCustomerPrice = calculateLatestCustomerPrice(record);

                // 如果无客户价格核算标准，使用内部核算价格
                if (newCustomerPrice == null || newCustomerPrice.compareTo(BigDecimal.ZERO) == 0) {
                    newCustomerPrice = record.getInternalAccountingPrice();
                }

                // 更新客户核算价格
                UpdateWrapper<ProductionValueCalculation> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("id", id);
                updateWrapper.set("customer_accounting_price", newCustomerPrice);
                updateWrapper.set("codex_torch_updater", SecurityContextHolder.getCurrentUserId());
                updateWrapper.set("codex_torch_update_datetime", new Timestamp(System.currentTimeMillis()));
                productionValueCalculationMapper.update(null, updateWrapper);

                successCount++;
            } catch (Exception e) {
                ProductionValueCalculation record = productionValueCalculationMapper.selectById(id);
                String workOrderNumber = record != null ? record.getWorkOrderNumber() : "ID:" + id;
                failedWorkOrders.add(workOrderNumber);
                failureDetails.append("工单编号:").append(workOrderNumber).append(" 客户价格重新核算失败:").append(e.getMessage()).append("; ");
                log.error("客户价格重新核算失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            }
        }

        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);

        // 根据操作结果返回不同的消息
        if (ids.size() == 1) {
            // 单个操作
            if (successCount == 1) {
                response.setMessage("客户价格重新核算成功");
            } else {
                response.setMessage("客户价格重新核算失败");
            }
        } else {
            // 批量操作
            if (failedWorkOrders.isEmpty()) {
                response.setMessage("批量客户价格重新核算成功，共处理 " + successCount + " 条记录");
            } else {
                response.setMessage("批量客户价格重新核算完成，成功 " + successCount + " 条，失败 " + failedWorkOrders.size() + " 条。失败工单编号: " + String.join(", ", failedWorkOrders));
            }
        }

        log.info(failureDetails.toString());
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse recalculateInternalPrice(List<String> ids) {
        if (HuatekTools.isEmpty(ids)) {
            throw new ServiceException("数据不存在");
        }

        int successCount = 0;
        List<String> failedWorkOrders = new ArrayList<>();
        StringBuilder failureDetails = new StringBuilder();
        List<String> noResultWorkOrders = new ArrayList<>(); // 记录未计算出结果的工单

        for (String id : ids) {
            try {
                ProductionValueCalculation record = productionValueCalculationMapper.selectById(id);
                if (HuatekTools.isEmpty(record)) {
                    failedWorkOrders.add("ID:" + id);
                    failureDetails.append("ID:").append(id).append(" 数据不存在; ");
                    continue;
                }

                // 验证类型必须为君信标准
                if (!DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_TYPE_JUNXIN.equals(record.getType())) {
                    failedWorkOrders.add(record.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(record.getWorkOrderNumber()).append(" 类型不是君信标准，无法重新核算内部价格; ");
                    continue;
                }

                // 重新计算内部价格
                BigDecimal newInternalPrice = calculateLatestInternalPrice(record);

                if (newInternalPrice == null || newInternalPrice.compareTo(BigDecimal.ZERO) == 0) {
                    // 系统未计算出结果，记录工单号用于后续通知
                    noResultWorkOrders.add(record.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(record.getWorkOrderNumber()).append(" 系统未计算出内部价格结果; ");
                    continue;
                }

                // 更新内部核算价格
                UpdateWrapper<ProductionValueCalculation> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("id", id);
                updateWrapper.set("internal_accounting_price", newInternalPrice);
                updateWrapper.set("codex_torch_updater", SecurityContextHolder.getCurrentUserId());
                updateWrapper.set("codex_torch_update_datetime", new Timestamp(System.currentTimeMillis()));
                productionValueCalculationMapper.update(null, updateWrapper);

                successCount++;
            } catch (Exception e) {
                ProductionValueCalculation record = productionValueCalculationMapper.selectById(id);
                String workOrderNumber = record != null ? record.getWorkOrderNumber() : "ID:" + id;
                failedWorkOrders.add(workOrderNumber);
                failureDetails.append("工单编号:").append(workOrderNumber).append(" 内部价格重新核算失败:").append(e.getMessage()).append("; ");
                log.error("内部价格重新核算失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            }
        }

        // 如果有未计算出结果的工单，发送消息给市场结算角色
        if (!noResultWorkOrders.isEmpty()) {
            sendMessageToMarketSettlementRole(noResultWorkOrders);
        }

        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);

        // 根据操作结果返回不同的消息
        if (ids.size() == 1) {
            // 单个操作
            if (successCount == 1) {
                response.setMessage("内部价格重新核算成功");
            } else {
                response.setMessage("内部价格重新核算失败");
            }
        } else {
            // 批量操作
            String message = "批量内部价格重新核算完成，成功 " + successCount + " 条";
            if (!failedWorkOrders.isEmpty()) {
                message += "，失败 " + failedWorkOrders.size() + " 条";
            }
            if (!noResultWorkOrders.isEmpty()) {
                message += "，未计算出结果 " + noResultWorkOrders.size() + " 条（已通知市场结算角色）";
            }
            response.setMessage(message);
        }

        log.info(failureDetails.toString());
        return response;
    }

    /**
     * 根据最新核算标准计算客户价格
     * @param record 核算记录
     * @return 计算出的客户价格
     */
    private BigDecimal calculateLatestCustomerPrice(ProductionValueCalculation record) {
        try {
            // 获取工序ID - 需要根据工单编号查询相关信息
            String processId = getProcessIdByWorkOrder(record.getWorkOrderNumber());
            if (StringUtils.isEmpty(processId)) {
                log.warn("无法获取工序ID，工单编号: {}", record.getWorkOrderNumber());
                return record.getCustomerAccountingPrice();
            }

            // 创建临时记录用于计算
            ProductionValueCalculation tempRecord = new ProductionValueCalculation();
            BeanUtils.copyProperties(record, tempRecord);

            // 重新计算客户价格 - 使用产品分类作为客户器件分类
            calculatePriceByStandard(tempRecord, record.getTestType(), processId, record.getProductCategory(), false);

            return tempRecord.getCustomerAccountingPrice();

        } catch (Exception e) {
            log.error("计算最新客户价格失败，工单编号: {}", record.getWorkOrderNumber(), e);
            return record.getCustomerAccountingPrice();
        }
    }

    /**
     * 根据最新核算标准计算内部价格
     * @param record 核算记录
     * @return 计算出的内部价格
     */
    private BigDecimal calculateLatestInternalPrice(ProductionValueCalculation record) {
        try {
            // 获取工序ID - 需要根据工单编号查询相关信息
            String processId = getProcessIdByWorkOrder(record.getWorkOrderNumber());
            if (StringUtils.isEmpty(processId)) {
                log.warn("无法获取工序ID，工单编号: {}", record.getWorkOrderNumber());
                return record.getInternalAccountingPrice();
            }

            // 创建临时记录用于计算
            ProductionValueCalculation tempRecord = new ProductionValueCalculation();
            BeanUtils.copyProperties(record, tempRecord);

            // 重新计算内部价格 - 使用产品分类作为内部分类
            calculatePriceByStandard(tempRecord, record.getTestType(), processId, record.getProductCategory(), true);

            return tempRecord.getInternalAccountingPrice();

        } catch (Exception e) {
            log.error("计算最新内部价格失败，工单编号: {}", record.getWorkOrderNumber(), e);
            return record.getInternalAccountingPrice();
        }
    }

    /**
     * 根据工单编号获取工序ID
     * @param workOrderNumber 工单编号
     * @return 工序ID
     */
    private String getProcessIdByWorkOrder(String workOrderNumber) {
        try {
            // 查询工单信息
            QueryWrapper<ProductionOrder> orderWrapper = new QueryWrapper<>();
            orderWrapper.eq("work_order_number", workOrderNumber);
            orderWrapper.eq("codex_torch_deleted", Constant.DEFAULT_NO);
            ProductionOrder productionOrder = awaitingProductionOrderMapper.selectOne(orderWrapper);

            if (productionOrder == null) {
                return null;
            }

            // 查询客户工序方案
            QueryWrapper<CustomerProcessScheme> schemeWrapper = new QueryWrapper<>();
            schemeWrapper.eq("work_order", productionOrder.getId());
            CustomerProcessScheme scheme = customerProcessSchemeMapper.selectOne(schemeWrapper);

            if (scheme == null) {
                return null;
            }

            // 查询客户试验项目，取第一个工序ID
            CustomerExperimentProjectDTO dto = new CustomerExperimentProjectDTO();
            dto.setCodexTorchMasterFormId(scheme.getId());
            List<CustomerExperimentProjectVO> projects = customerExperimentProjectMapper.selectCustomerExperimentProjectList(dto);

            if (!CollectionUtils.isEmpty(projects)) {
                return projects.get(0).getProcessCode3();
            }

            return null;
        } catch (Exception e) {
            log.error("根据工单编号获取工序ID失败: {}", workOrderNumber, e);
            return null;
        }
    }

    /**
     * 发送消息给市场结算角色
     * @param workOrderNumbers 未计算出结果的工单编号列表
     */
    private void sendMessageToMarketSettlementRole(List<String> workOrderNumbers) {
        try {
            // TODO: 实现消息发送逻辑
            // 这里需要根据实际的消息系统实现
            // 发送消息给市场结算角色，告知这些工单未能计算出内部价格

            String message = "以下工单未能计算出内部价格，请人工处理：" + String.join(", ", workOrderNumbers);
            log.warn("发送消息给市场结算角色: {}", message);

            // 实际实现时可能需要调用消息服务或者插入消息表
        } catch (Exception e) {
            log.error("发送消息给市场结算角色失败", e);
        }
    }

}
