package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.domain.ProductionValueCalculation;
import com.huatek.frame.modules.business.domain.vo.ProductionValueCalculationDuiZhangVO;
import com.huatek.frame.modules.business.domain.vo.ProductionValueCalculationVO;
import com.huatek.frame.modules.business.mapper.ProductionValueCalculationMapper;
import com.huatek.frame.modules.business.service.ProductionValueCalculationService;
import com.huatek.frame.modules.business.service.dto.ProductionValueCalculationContractDTO;
import com.huatek.frame.modules.business.service.dto.ProductionValueCalculationDTO;
import com.huatek.frame.modules.business.service.dto.ProductionValueCalculationDuiZhangDTO;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.ProductionOrder;
import com.huatek.frame.modules.business.domain.EvaluationOrder;
import com.huatek.frame.modules.business.domain.CustomerInformationManagement;
import com.huatek.frame.modules.business.domain.ProductList;
import com.huatek.frame.modules.business.domain.StandardSpecification;
import com.huatek.frame.modules.business.mapper.AwaitingProductionOrderMapper;
import com.huatek.frame.modules.business.mapper.EvaluationOrderMapper;
import com.huatek.frame.modules.business.mapper.CustomerInformationManagementMapper;
import com.huatek.frame.modules.business.mapper.ProductListMapper;
import com.huatek.frame.modules.business.mapper.StandardSpecificationMapper;
import org.springframework.util.CollectionUtils;



/**
 * 产值计算 ServiceImpl
 * <AUTHOR>
 * @date 2025-08-22
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "productionValueCalculation")
//@RefreshScope
@Slf4j
public class ProductionValueCalculationServiceImpl implements ProductionValueCalculationService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private ProductionValueCalculationMapper productionValueCalculationMapper;
    
    @Autowired
    private AwaitingProductionOrderMapper awaitingProductionOrderMapper;



    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	public ProductionValueCalculationServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<ProductionValueCalculationVO>> findProductionValueCalculationPage(ProductionValueCalculationDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<ProductionValueCalculationVO> productionValueCalculations = productionValueCalculationMapper.selectProductionValueCalculationPage(dto);
		TorchResponse<List<ProductionValueCalculationVO>> response = new TorchResponse<List<ProductionValueCalculationVO>>();
		response.getData().setData(productionValueCalculations);
		response.setStatus(200);
		response.getData().setCount(productionValueCalculations.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(ProductionValueCalculationDTO productionValueCalculationDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(productionValueCalculationDto.getCodexTorchDeleted())) {
            productionValueCalculationDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = productionValueCalculationDto.getId();
		ProductionValueCalculation entity = new ProductionValueCalculation();
        BeanUtils.copyProperties(productionValueCalculationDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
			productionValueCalculationMapper.insert(entity);
		} else {
			productionValueCalculationMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        ProductionValueCalculationVO vo = new ProductionValueCalculationVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<ProductionValueCalculationVO> findProductionValueCalculation(String id) {
		ProductionValueCalculationVO vo = new ProductionValueCalculationVO();
		if (!HuatekTools.isEmpty(id)) {
			ProductionValueCalculation entity = productionValueCalculationMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<ProductionValueCalculationVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
	    List<ProductionValueCalculation> productionValueCalculationList = productionValueCalculationMapper.selectBatchIds(Arrays.asList(ids));
        for (ProductionValueCalculation productionValueCalculation : productionValueCalculationList) {
            productionValueCalculation.setCodexTorchDeleted(Constant.DEFAULT_YES);
            productionValueCalculationMapper.updateById(productionValueCalculation);
        }
		//productionValueCalculationMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "production_value_calculation", convertorFields = "status,type,testType")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<ProductionValueCalculationVO> selectProductionValueCalculationList(ProductionValueCalculationDTO dto) {
        return productionValueCalculationMapper.selectProductionValueCalculationList(dto);
    }

    /**
     * 对账结果数据批量导入
     *
     * @param productionValueCalculationList 对账结果数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "production_value_calculation", convertorFields = "status,type,testType")
    public TorchResponse importProductionValueCalculation(List<ProductionValueCalculationDuiZhangVO> productionValueCalculationList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(productionValueCalculationList) || productionValueCalculationList.size() == 0) {
            throw new ServiceException("对账结果数据批量导入不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (ProductionValueCalculationDuiZhangVO vo : productionValueCalculationList) {
            try {
                // 1. 验证所有必填字段不能为空
                if (validateRequiredFields(vo, failureNum, failureMsg)) {
                    failureNum++;
                    continue;
                }

                // 2. 根据工单编号查询现有数据
                ProductionValueCalculation existingRecord = findExistingRecordByWorkOrder(vo.getWorkOrderNumber());
                if (existingRecord == null) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、工单编号 ").append(vo.getWorkOrderNumber()).append(" 不存在，无法更新");
                    continue;
                }

                // 3. 验证type字段必须等于客户标准
                if (!validateCustomerStandard(existingRecord, failureNum, failureMsg)) { failureNum++; continue; }

                // 4. 执行更新操作
                updateDuizhangs(vo);


                successNum++;
                successMsg.append("<br/>").append(successNum).append("、工单编号 ").append(vo.getWorkOrderNumber()).append(" 更新成功");

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、工单编号 " + vo.getWorkOrderNumber() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error(msg, e);
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private void updateDuizhangs(ProductionValueCalculationDuiZhangVO vo) {
        UpdateWrapper<ProductionValueCalculation> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("work_order_number", vo.getWorkOrderNumber());
        updateWrapper.set("status", DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_YES);
        updateWrapper.set("settlement_price", vo.getSettlementPrice());
        updateWrapper.set("bill_statement_number", vo.getBillStatementNumber());
        updateWrapper.set("settlement_date", new Date());
        updateWrapper.set("codex_torch_updater", SecurityContextHolder.getCurrentUserId());
        updateWrapper.set("codex_torch_update_datetime", new Timestamp(System.currentTimeMillis()));
        productionValueCalculationMapper.update(null, updateWrapper);
    }

    /**
     * 验证必填字段
     * @param vo 对账VO对象
     * @param failureNum 失败数量
     * @param failureMsg 失败消息
     * @return true表示验证失败，false表示验证通过
     */
    private boolean validateRequiredFields(ProductionValueCalculationDuiZhangVO vo, int failureNum, StringBuilder failureMsg) {
        StringBuilder errorMsg = new StringBuilder();

        if (StringUtils.isEmpty(vo.getWorkOrderNumber())) {
            errorMsg.append("工单编号不能为空；");
        }
        if (vo.getSettlementPrice() == null) {
            errorMsg.append("对账价格不能为空；");
        }
        if (StringUtils.isEmpty(vo.getBillStatementNumber())) {
            errorMsg.append("对账单号不能为空；");
        }

        if (errorMsg.length() > 0) {
            failureMsg.append("<br/>").append(failureNum + 1).append("、工单编号 ").append(vo.getWorkOrderNumber()).append(" 验证失败：").append(errorMsg.toString());
            return true;
        }
        return false;
    }

    /**
     * 根据工单编号查询现有记录
     * @param workOrderNumber 工单编号
     * @return 现有记录，如果不存在返回null
     */
    private ProductionValueCalculation findExistingRecordByWorkOrder(String workOrderNumber) {
        if (StringUtils.isEmpty(workOrderNumber)) {
            return null;
        }

        QueryWrapper<ProductionValueCalculation> wrapper = new QueryWrapper<>();
        wrapper.eq("work_order_number", workOrderNumber);
        wrapper.eq("codex_torch_deleted", "0");

        List<ProductionValueCalculation> existingRecords = productionValueCalculationMapper.selectList(wrapper);
        if (!CollectionUtils.isEmpty(existingRecords)) {
            return existingRecords.get(0);
        }
        return null;
    }

    /**
     * 验证客户标准信息
     * @param existingRecord
     * @param failureNum 失败数量
     * @param failureMsg 失败消息
     * @return true表示验证通过，false表示验证失败
     */
    private boolean validateCustomerStandard(ProductionValueCalculation existingRecord, int failureNum, StringBuilder failureMsg) {
        try {
            // 验证type字段必须等于客户标准
            if (!DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_KEHU.equals(existingRecord.getType())) {
                failureMsg.append("<br/>").append(failureNum + 1).append("、工单编号 ").append(existingRecord.getWorkOrderNumber()).append(" 的类型不是客户标准，无法进行对账");
                return false;
            }

            return true;
        } catch (Exception e) {
            failureMsg.append("<br/>").append(failureNum + 1).append("、工单编号 ").append(existingRecord.getWorkOrderNumber()).append(" 客户标准验证失败：").append(e.getMessage());
            log.error("验证客户标准失败", e);
            return false;
        }
    }

    private Boolean linkedDataValidityVerification(ProductionValueCalculationVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectProductionValueCalculationListByIds(List<String> ids) {
        List<ProductionValueCalculationVO> productionValueCalculationList = productionValueCalculationMapper.selectProductionValueCalculationListByIds(ids);

		TorchResponse<List<ProductionValueCalculationVO>> response = new TorchResponse<List<ProductionValueCalculationVO>>();
		response.getData().setData(productionValueCalculationList);
		response.setStatus(200);
		response.getData().setCount((long)productionValueCalculationList.size());
		return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse duiZhang(ProductionValueCalculationDuiZhangDTO productionValueCalculationDuiZhangDTO) {

        List<String> ids = productionValueCalculationDuiZhangDTO.getIds();
        if (HuatekTools.isEmpty(ids)) {

                throw new ServiceException("数据不存在");

        }

        int successCount = 0;
        List<String> failedWorkOrders = new ArrayList<>();
        StringBuilder failureDetails = new StringBuilder();

        for (String id : ids) {
            try {
                ProductionValueCalculation productionValueCalculation = productionValueCalculationMapper.selectById(id);
                if (HuatekTools.isEmpty(productionValueCalculation)) {
                    failedWorkOrders.add("ID:" + id);
                    failureDetails.append("ID:").append(id).append(" 数据不存在; ");
                    continue;
                }

                if (!productionValueCalculation.getType().equals(DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_KEHU)) {
                    failedWorkOrders.add(productionValueCalculation.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(productionValueCalculation.getWorkOrderNumber()).append(" 类型为君信标准，不可对账; ");
                    continue;
                }

                UpdateWrapper<ProductionValueCalculation> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("id", id);
                updateWrapper.set("status", DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_YES);
                updateWrapper.set("settlement_price", productionValueCalculationDuiZhangDTO.getSettlementPrice());
                updateWrapper.set("bill_statement_number", productionValueCalculationDuiZhangDTO.getBillStatementNumber());
                updateWrapper.set("settlement_date", new Date());
                updateWrapper.set("codex_torch_updater", SecurityContextHolder.getCurrentUserId());
                updateWrapper.set("codex_torch_update_datetime", new Timestamp(System.currentTimeMillis()));
                productionValueCalculationMapper.update(null, updateWrapper);

                successCount++;
            } catch (Exception e) {
                ProductionValueCalculation productionValueCalculation = productionValueCalculationMapper.selectById(id);
                String workOrderNumber = productionValueCalculation != null ? productionValueCalculation.getWorkOrderNumber() : "ID:" + id;
                failedWorkOrders.add(workOrderNumber);
                failureDetails.append("工单编号:").append(workOrderNumber).append(" 对账失败:").append(e.getMessage()).append("; ");
                log.error("对账失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            }
        }

        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);

        // 根据操作结果返回不同的消息
        if (ids.size() == 1) {
            // 单个操作
            if (successCount == 1) {
                response.setMessage("对账成功");
            } else {
                response.setMessage("对账失败");
                response.setStatus(Constant.REQUEST_SUCCESS);
            }
        } else {
            // 批量操作
            if (failedWorkOrders.isEmpty()) {
                response.setMessage("批量对账成功，共处理 " + successCount + " 条记录");
            } else {
                response.setMessage("批量对账完成，成功 " + successCount + " 条，失败 " + failedWorkOrders.size() + " 条。失败工单编号: " + String.join(", ", failedWorkOrders));
            }
        }

        log.info(failureDetails.toString());

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse updateContract(ProductionValueCalculationContractDTO productionValueCalculationContractDTO) {
        // 获取ID列表，支持批量操作和单个操作
        List<String> ids = productionValueCalculationContractDTO.getIds();
        if (HuatekTools.isEmpty(ids)) {

                throw new ServiceException("数据不存在");

        }

        int successCount = 0;
        List<String> failedWorkOrders = new ArrayList<>();
        StringBuilder failureDetails = new StringBuilder();

        for (String id : ids) {
            try {
                ProductionValueCalculation productionValueCalculation = productionValueCalculationMapper.selectById(id);
                if (HuatekTools.isEmpty(productionValueCalculation)) {
                    failedWorkOrders.add("ID:" + id);
                    failureDetails.append("ID:").append(id).append(" 数据不存在; ");
                    continue;
                }

                if (!productionValueCalculation.getType().equals(DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_KEHU)) {
                    failedWorkOrders.add(productionValueCalculation.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(productionValueCalculation.getWorkOrderNumber()).append(" 类型为君信标准，不可录入合同; ");
                    continue;
                }

                if (!productionValueCalculation.getStatus().equals(DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_YES)) {
                    failedWorkOrders.add(productionValueCalculation.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(productionValueCalculation.getWorkOrderNumber()).append(" 状态为未对账，不可录入合同; ");
                    continue;
                }

                UpdateWrapper<ProductionValueCalculation> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("id", id);
                updateWrapper.set("contract_number", productionValueCalculationContractDTO.getContractNumber());
                updateWrapper.set("codex_torch_updater", SecurityContextHolder.getCurrentUserId());
                updateWrapper.set("codex_torch_update_datetime", new Timestamp(System.currentTimeMillis()));
                productionValueCalculationMapper.update(null, updateWrapper);

                successCount++;
            } catch (Exception e) {
                ProductionValueCalculation productionValueCalculation = productionValueCalculationMapper.selectById(id);
                String workOrderNumber = productionValueCalculation != null ? productionValueCalculation.getWorkOrderNumber() : "ID:" + id;
                failedWorkOrders.add(workOrderNumber);
                failureDetails.append("工单编号:").append(workOrderNumber).append(" 合同录入失败:").append(e.getMessage()).append("; ");
                log.error("合同录入失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            }
        }

        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);

        // 根据操作结果返回不同的消息
        if (ids.size() == 1) {
            // 单个操作
            if (successCount == 1) {
                response.setMessage("合同录入成功");
            } else {
                response.setMessage("合同录入失败");
                response.setStatus(Constant.REQUEST_SUCCESS);
            }
        } else {
            // 批量操作
            if (failedWorkOrders.isEmpty()) {
                response.setMessage("批量合同录入成功，共处理 " + successCount + " 条记录");
            } else {
                response.setMessage("批量合同录入完成，成功 " + successCount + " 条，失败 " + failedWorkOrders.size() + " 条。失败工单编号: " + String.join(", ", failedWorkOrders));
            }
        }

        log.info(failureDetails.toString());
        return response;
    }

    @Override
    public TorchResponse generateCalculation() throws Exception {
        //查询production_order is_calculation='0'的数据
        List<ProductionOrder> productionOrderList = awaitingProductionOrderMapper.selectList(new QueryWrapper<ProductionOrder>()
                .eq("is_calculation", DicConstant.CommonDic.DEFAULT_ZERO)
                .eq("codex_torch_deleted", Constant.DEFAULT_NO)
        );

        //参考issueProductionOrderTask  获取试验类型/工序id/和/工序分类


        return null;
    }

}
