2025-08-25 09:30:30,378 INFO basic [main] com.huatek.frame.BasicApplication [SpringApplication.java : 655] The following profiles are active: dev
2025-08-25 09:30:39,492 INFO basic [main] c.alibaba.spring.util.BeanRegistrar [BeanRegistrar.java : 67] The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
2025-08-25 09:30:39,497 INFO basic [main] c.alibaba.spring.util.BeanRegistrar [BeanRegistrar.java : 67] The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
2025-08-25 09:30:39,510 INFO basic [main] c.alibaba.spring.util.BeanRegistrar [BeanRegistrar.java : 67] The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboLifecycleComponentApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboLifecycleComponentApplicationListener] has been registered.
2025-08-25 09:30:39,514 INFO basic [main] c.alibaba.spring.util.BeanRegistrar [BeanRegistrar.java : 67] The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2025-08-25 09:30:39,539 INFO basic [main] c.alibaba.spring.util.BeanRegistrar [BeanRegistrar.java : 67] The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
2025-08-25 09:30:40,408 INFO basic [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 249] Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-25 09:30:40,428 INFO basic [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 127] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-25 09:30:40,923 INFO basic [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 187] Finished Spring Data repository scanning in 375ms. Found 0 Redis repository interfaces.
2025-08-25 09:30:42,072 INFO basic [main] c.a.s.b.f.a.ConfigurationBeanBindingRegistrar [ConfigurationBeanBindingRegistrar.java : 139] The configuration bean definition [name : org.apache.dubbo.config.ApplicationConfig#0, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2025-08-25 09:30:42,073 INFO basic [main] c.alibaba.spring.util.BeanRegistrar [BeanRegistrar.java : 67] The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
2025-08-25 09:30:42,075 INFO basic [main] c.a.s.b.f.a.ConfigurationBeanBindingRegistrar [ConfigurationBeanBindingRegistrar.java : 139] The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2025-08-25 09:30:42,076 INFO basic [main] c.a.s.b.f.a.ConfigurationBeanBindingRegistrar [ConfigurationBeanBindingRegistrar.java : 139] The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2025-08-25 09:30:42,757 INFO basic [main] c.alibaba.spring.util.BeanRegistrar [BeanRegistrar.java : 67] The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2025-08-25 09:30:44,738 INFO basic [main] o.s.c.a.ConfigurationClassPostProcessor [ConfigurationClassPostProcessor.java : 403] Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboRelaxedBinding2AutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2025-08-25 09:30:44,739 INFO basic [main] o.s.c.a.ConfigurationClassPostProcessor [ConfigurationClassPostProcessor.java : 403] Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2025-08-25 09:30:45,180 INFO basic [main] o.s.cloud.context.scope.GenericScope [GenericScope.java : 295] BeanFactory id=41a7af0d-02c2-3f80-a2c6-f3a84da14cbf
2025-08-25 09:30:45,449 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'asyncTaskProperties' of type [com.huatek.frame.common.utils.thread.AsyncTaskProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:45,470 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'asyncTaskExecutePool' of type [com.huatek.frame.common.utils.thread.AsyncTaskExecutePool$$EnhancerBySpringCGLIB$$33eecec7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:47,791 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:47,867 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$1ebdf863] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:47,926 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:47,977 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'redisConfig' of type [com.huatek.frame.common.conf.RedisConfig$$EnhancerBySpringCGLIB$$1636c411] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:48,386 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.autoconfigure.DubboOpenFeignAutoConfiguration' of type [com.alibaba.cloud.dubbo.autoconfigure.DubboOpenFeignAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:48,422 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'dubbo.cloud-com.alibaba.cloud.dubbo.env.DubboCloudProperties' of type [com.alibaba.cloud.dubbo.env.DubboCloudProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:48,438 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.autoconfigure.DubboServiceAutoConfiguration' of type [com.alibaba.cloud.dubbo.autoconfigure.DubboServiceAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:48,451 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'dubboGenericServiceFactory' of type [com.alibaba.cloud.dubbo.service.DubboGenericServiceFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:48,467 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.util.JSONUtils' of type [com.alibaba.cloud.dubbo.util.JSONUtils] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:48,482 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.util.DubboMetadataUtils' of type [com.alibaba.cloud.dubbo.util.DubboMetadataUtils] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:48,521 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'metadataJsonResolver' of type [com.alibaba.cloud.dubbo.metadata.resolver.DubboServiceBeanMetadataResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:48,564 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'relaxedDubboConfigBinder' of type [org.apache.dubbo.spring.boot.autoconfigure.BinderDubboConfigBinder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:48,574 INFO basic [main] c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor [ConfigurationBeanBindingPostProcessor.java : 159] The configuration bean [<dubbo:application name="jx-mes-basic-application" hostname="03-225" qosEnable="false" />] have been binding by the configuration properties [{name=jx-mes-basic-application, qos-enable=false}]
2025-08-25 09:30:48,579 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.apache.dubbo.config.ApplicationConfig#0' of type [org.apache.dubbo.config.ApplicationConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:48,616 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'dubboProtocolConfigSupplier' of type [com.alibaba.cloud.dubbo.metadata.DubboProtocolConfigSupplier] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:48,623 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.service.DubboMetadataServiceExporter' of type [com.alibaba.cloud.dubbo.service.DubboMetadataServiceExporter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:48,626 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.autoconfigure.DubboMetadataAutoConfiguration' of type [com.alibaba.cloud.dubbo.autoconfigure.DubboMetadataAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:48,639 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'metadataServiceInstanceSelector' of type [com.alibaba.cloud.dubbo.metadata.repository.RandomServiceInstanceSelector] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:48,655 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClientAutoConfiguration' of type [org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClientAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:48,676 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.nacos.discovery.NacosDiscoveryClientConfiguration' of type [com.alibaba.cloud.nacos.discovery.NacosDiscoveryClientConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:48,726 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.nacos.discovery.NacosDiscoveryAutoConfiguration' of type [com.alibaba.cloud.nacos.discovery.NacosDiscoveryAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:48,774 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.cloud.commons.util.UtilAutoConfiguration' of type [org.springframework.cloud.commons.util.UtilAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:48,796 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'inetUtilsProperties' of type [org.springframework.cloud.commons.util.InetUtilsProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:48,805 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'inetUtils' of type [org.springframework.cloud.commons.util.InetUtils] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:48,821 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.nacos.NacosServiceAutoConfiguration' of type [com.alibaba.cloud.nacos.NacosServiceAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:48,843 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'nacosServiceManager' of type [com.alibaba.cloud.nacos.NacosServiceManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:50,047 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'nacosProperties' of type [com.alibaba.cloud.nacos.NacosDiscoveryProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:50,067 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'nacosServiceDiscovery' of type [com.alibaba.cloud.nacos.discovery.NacosServiceDiscovery] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:50,081 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'nacosDiscoveryClient' of type [com.alibaba.cloud.nacos.discovery.NacosDiscoveryClient] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:50,164 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'server-org.springframework.boot.autoconfigure.web.ServerProperties' of type [org.springframework.boot.autoconfigure.web.ServerProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:50,172 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration' of type [org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:51,317 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'simpleDiscoveryProperties' of type [org.springframework.cloud.client.discovery.simple.SimpleDiscoveryProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:51,332 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'simpleDiscoveryClient' of type [org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClient] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:51,382 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'compositeDiscoveryClient' of type [org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClient] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:51,399 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.service.DubboMetadataServiceProxy' of type [com.alibaba.cloud.dubbo.service.DubboMetadataServiceProxy] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:51,442 INFO basic [main] com.alibaba.nacos.client.naming [InitUtils.java : 65] initializer namespace from System Property :null
2025-08-25 09:30:51,445 INFO basic [main] com.alibaba.nacos.client.naming [InitUtils.java : 74] initializer namespace from System Environment :null
2025-08-25 09:30:51,447 INFO basic [main] com.alibaba.nacos.client.naming [InitUtils.java : 84] initializer namespace from System Property :null
2025-08-25 09:30:52,107 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.metadata.repository.DubboServiceMetadataRepository' of type [com.alibaba.cloud.dubbo.metadata.repository.DubboServiceMetadataRepository] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:52,254 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.service.parameter.RequestParamServiceParameterResolver' of type [com.alibaba.cloud.dubbo.service.parameter.RequestParamServiceParameterResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:52,347 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration' of type [org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:52,381 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration' of type [org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:52,437 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'stringHttpMessageConverter' of type [org.springframework.http.converter.StringHttpMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:52,473 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration' of type [org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:52,489 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:52,525 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:52,556 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:52,586 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:52,605 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:52,642 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:52,717 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:52,751 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:52,797 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:52,821 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:52,849 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:52,869 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:53,015 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:53,358 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'mappingJackson2HttpMessageConverter' of type [org.springframework.http.converter.json.MappingJackson2HttpMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:53,477 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'messageConverters' of type [org.springframework.boot.autoconfigure.http.HttpMessageConverters] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:53,495 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.service.parameter.RequestBodyServiceParameterResolver' of type [com.alibaba.cloud.dubbo.service.parameter.RequestBodyServiceParameterResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:53,640 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.service.parameter.RequestHeaderServiceParameterResolver' of type [com.alibaba.cloud.dubbo.service.parameter.RequestHeaderServiceParameterResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:53,717 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.service.parameter.PathVariableServiceParameterResolver' of type [com.alibaba.cloud.dubbo.service.parameter.PathVariableServiceParameterResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:53,723 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.service.DubboGenericServiceExecutionContextFactory' of type [com.alibaba.cloud.dubbo.service.DubboGenericServiceExecutionContextFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:54,546 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:55,097 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:30:58,240 INFO basic [main] o.s.b.w.e.tomcat.TomcatWebServer [TomcatWebServer.java : 108] Tomcat initialized with port(s): 8882 (http)
2025-08-25 09:30:58,312 INFO basic [main] o.a.coyote.http11.Http11NioProtocol [DirectJDKLog.java : 173] Initializing ProtocolHandler ["http-nio-8882"]
2025-08-25 09:30:58,314 INFO basic [main] o.a.catalina.core.StandardService [DirectJDKLog.java : 173] Starting service [Tomcat]
2025-08-25 09:30:58,316 INFO basic [main] o.a.catalina.core.StandardEngine [DirectJDKLog.java : 173] Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-08-25 09:30:59,048 INFO basic [main] o.a.c.c.C.[.[localhost].[/basic] [DirectJDKLog.java : 173] Initializing Spring embedded WebApplicationContext
2025-08-25 09:30:59,049 INFO basic [main] o.s.b.w.s.c.ServletWebServerApplicationContext [ServletWebServerApplicationContext.java : 285] Root WebApplicationContext: initialization completed in 28604 ms
2025-08-25 09:30:59,614 INFO basic [main] o.c.b.s.b.s.r.CamundaJerseyResourceConfig [CamundaJerseyResourceConfig.java : 38] Configuring camunda rest api.
2025-08-25 09:30:59,856 INFO basic [main] o.c.b.s.b.s.r.CamundaJerseyResourceConfig [CamundaJerseyResourceConfig.java : 44] Finished configuring camunda rest api.
2025-08-25 09:31:09,814 INFO basic [main] c.alibaba.druid.pool.DruidDataSource [DruidDataSource.java : 1003] {dataSource-1,master} inited
2025-08-25 09:31:10,441 INFO basic [main] c.alibaba.druid.pool.DruidDataSource [DruidDataSource.java : 1003] {dataSource-2,slave} inited
2025-08-25 09:31:10,442 INFO basic [main] c.b.d.d.DynamicRoutingDataSource [DynamicRoutingDataSource.java : 154] dynamic-datasource - add a datasource named [slave] success
2025-08-25 09:31:10,443 INFO basic [main] c.b.d.d.DynamicRoutingDataSource [DynamicRoutingDataSource.java : 154] dynamic-datasource - add a datasource named [master] success
2025-08-25 09:31:10,444 INFO basic [main] c.b.d.d.DynamicRoutingDataSource [DynamicRoutingDataSource.java : 234] dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-25 09:31:17,730 INFO basic [main] org.quartz.impl.StdSchedulerFactory [StdSchedulerFactory.java : 1220] Using default implementation for ThreadExecutor
2025-08-25 09:31:17,769 INFO basic [main] o.quartz.core.SchedulerSignalerImpl [SchedulerSignalerImpl.java : 61] Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-25 09:31:17,769 INFO basic [main] org.quartz.core.QuartzScheduler [QuartzScheduler.java : 229] Quartz Scheduler v.2.3.2 created.
2025-08-25 09:31:17,771 INFO basic [main] org.quartz.simpl.RAMJobStore [RAMJobStore.java : 155] RAMJobStore initialized.
2025-08-25 09:31:17,774 INFO basic [main] org.quartz.core.QuartzScheduler [QuartzScheduler.java : 294] Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-25 09:31:17,774 INFO basic [main] org.quartz.impl.StdSchedulerFactory [StdSchedulerFactory.java : 1374] Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-25 09:31:17,775 INFO basic [main] org.quartz.impl.StdSchedulerFactory [StdSchedulerFactory.java : 1378] Quartz scheduler version: 2.3.2
2025-08-25 09:31:17,776 INFO basic [main] org.quartz.core.QuartzScheduler [QuartzScheduler.java : 2293] JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@e2696e8
2025-08-25 09:31:19,775 INFO basic [main] c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor [ConfigurationBeanBindingPostProcessor.java : 159] The configuration bean [<dubbo:registry address="spring-cloud://localhost" protocol="spring-cloud" port="0" />] have been binding by the configuration properties [{address=spring-cloud://localhost}]
2025-08-25 09:31:19,792 INFO basic [main] c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor [ConfigurationBeanBindingPostProcessor.java : 159] The configuration bean [<dubbo:protocol name="dubbo" port="-1" />] have been binding by the configuration properties [{name=dubbo, port=-1}]
2025-08-25 09:31:19,805 INFO basic [main] o.a.d.c.s.b.f.a.ReferenceBeanBuilder [AnnotatedInterfaceConfigBeanBuilder.java : 84] The configBean[type:ReferenceBean] has been built.
2025-08-25 09:31:23,159 INFO basic [main] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 253] The metadata of Dubbo services has been initialized
2025-08-25 09:31:23,175 INFO basic [main] c.a.c.d.registry.DubboCloudRegistry [DubboCloudRegistry.java : 156] DubboCloudRegistry preInit Done.
2025-08-25 09:31:25,667 INFO basic [main] o.a.d.c.s.b.f.a.ReferenceBeanBuilder [AnnotatedInterfaceConfigBeanBuilder.java : 84] The configBean[type:ReferenceBean] has been built.
2025-08-25 09:31:27,911 INFO basic [main] o.a.d.c.s.b.f.a.ReferenceBeanBuilder [AnnotatedInterfaceConfigBeanBuilder.java : 84] The configBean[type:ReferenceBean] has been built.
2025-08-25 09:31:31,601 INFO basic [main] org.camunda.bpm.spring.boot [BaseLogger.java : 132] STARTER-SB040 Setting up jobExecutor with corePoolSize=3, maxPoolSize:10
2025-08-25 09:31:31,687 INFO basic [main] o.s.s.c.ThreadPoolTaskExecutor [ExecutorConfigurationSupport.java : 181] Initializing ExecutorService 'camundaTaskExecutor'
2025-08-25 09:31:33,099 INFO basic [main] org.camunda.bpm.engine.cfg [BaseLogger.java : 132] ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaJobConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, SpringBootSpinProcessEnginePlugin]' activated on process engine 'default'
2025-08-25 09:31:34,493 INFO basic [main] org.camunda.bpm.spring.boot [BaseLogger.java : 132] STARTER-SB021 Auto-Deploying resources: [file [C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-basic\jx-mes-basic-application\target\classes\bpm\one-way-two-approver-process.bpmn], file [C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-basic\jx-mes-basic-application\target\classes\bpm\triway-one-approval-process.bpmn], file [C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-basic\jx-mes-basic-application\target\classes\bpm\triway-three-approver-process.bpmn], file [C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-basic\jx-mes-basic-application\target\classes\bpm\one-way-five-approver-process.bpmn], file [C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-basic\jx-mes-basic-application\target\classes\bpm\one-way-four-approver-process.bpmn], file [C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-basic\jx-mes-basic-application\target\classes\bpm\triway-five-approver-process.bpmn], file [C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-basic\jx-mes-basic-application\target\classes\bpm\triway-simple-approval-process.bpmn], file [C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-basic\jx-mes-basic-application\target\classes\bpm\simple-approval-process.bpmn], file [C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-basic\jx-mes-basic-application\target\classes\bpm\triway-two-approver-process.bpmn], file [C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-basic\jx-mes-basic-application\target\classes\bpm\one-way-three-approver-process.bpmn], file [C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-basic\jx-mes-basic-application\target\classes\bpm\triway-four-approver-process.bpmn]]
2025-08-25 09:31:34,508 INFO basic [main] o.c.b.s.b.s.e.EventPublisherPlugin [EventPublisherPlugin.java : 57] EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-08-25 09:31:34,509 INFO basic [main] o.c.b.s.b.s.e.EventPublisherPlugin [EventPublisherPlugin.java : 59] EVENTING-003: Task events will be published as Spring Events.
2025-08-25 09:31:34,509 INFO basic [main] o.c.b.s.b.s.e.EventPublisherPlugin [EventPublisherPlugin.java : 65] EVENTING-005: Execution events will be published as Spring Events.
2025-08-25 09:31:34,513 INFO basic [main] o.c.b.s.b.s.e.EventPublisherPlugin [EventPublisherPlugin.java : 74] EVENTING-007: History events will be published as Spring events.
2025-08-25 09:31:34,531 INFO basic [main] org.camunda.spin [BaseLogger.java : 132] SPIN-01010 Discovered Spin data format provider: org.camunda.spin.impl.json.jackson.format.JacksonJsonDataFormatProvider[name = application/json]
2025-08-25 09:31:35,500 INFO basic [main] org.camunda.spin [BaseLogger.java : 132] SPIN-01010 Discovered Spin data format provider: org.camunda.spin.impl.xml.dom.format.DomXmlDataFormatProvider[name = application/xml]
2025-08-25 09:31:35,538 INFO basic [main] org.camunda.spin [BaseLogger.java : 132] SPIN-01009 Discovered Spin data format: org.camunda.spin.impl.xml.dom.format.DomXmlDataFormat[name = application/xml]
2025-08-25 09:31:35,540 INFO basic [main] org.camunda.spin [BaseLogger.java : 132] SPIN-01009 Discovered Spin data format: org.camunda.spin.impl.json.jackson.format.JacksonJsonDataFormat[name = application/json]
2025-08-25 09:31:35,912 INFO basic [main] org.camunda.bpm.dmn.feel.scala [BaseLogger.java : 132] FEEL/SCALA-01001 Spin value mapper detected
2025-08-25 09:31:36,564 INFO basic [main] org.camunda.feel.FeelEngine [FeelEngine.scala : 123] Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@3ed1446c, org.camunda.spin.plugin.impl.feel.integration.SpinValueMapper@2e9bff08)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@55e75ce0, clock: SystemClock, configuration: Configuration(false)]
2025-08-25 09:31:42,284 INFO basic [main] org.camunda.bpm.connect [BaseLogger.java : 132] CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-08-25 09:31:42,291 INFO basic [main] org.camunda.bpm.connect [BaseLogger.java : 132] CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-08-25 09:31:46,692 INFO basic [main] org.camunda.bpm.engine [BaseLogger.java : 132] ENGINE-00001 Process Engine default created.
2025-08-25 09:31:47,852 INFO basic [main] o.a.d.c.s.b.f.a.ReferenceBeanBuilder [AnnotatedInterfaceConfigBeanBuilder.java : 84] The configBean[type:ReferenceBean] has been built.
2025-08-25 09:31:49,798 INFO basic [main] o.a.d.c.s.b.f.a.ReferenceBeanBuilder [AnnotatedInterfaceConfigBeanBuilder.java : 84] The configBean[type:ReferenceBean] has been built.
2025-08-25 09:31:51,934 INFO basic [main] o.a.d.c.s.b.f.a.ReferenceBeanBuilder [AnnotatedInterfaceConfigBeanBuilder.java : 84] The configBean[type:ReferenceBean] has been built.
2025-08-25 09:31:54,331 INFO basic [main] c.a.c.s.SentinelWebAutoConfiguration [SentinelWebAutoConfiguration.java : 80] [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-25 09:31:56,703 INFO basic [main] org.redisson.Version [Version.java : 41] Redisson 3.17.4
2025-08-25 09:31:58,268 INFO basic [redisson-netty-6-7] o.r.c.p.MasterPubSubConnectionPool [ConnectionPool.java : 158] 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-25 09:31:58,373 INFO basic [redisson-netty-6-19] o.r.c.pool.MasterConnectionPool [ConnectionPool.java : 158] 24 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-25 09:31:58,754 INFO basic [main] s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping [WebMvcPropertySourcedRequestMappingHandlerMapping.java : 69] Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-08-25 09:31:59,226 INFO basic [main] o.s.s.c.ThreadPoolTaskExecutor [ExecutorConfigurationSupport.java : 181] Initializing ExecutorService
2025-08-25 09:31:59,233 INFO basic [main] o.s.s.c.ThreadPoolTaskExecutor [ExecutorConfigurationSupport.java : 181] Initializing ExecutorService 'opsLogExecutor'
2025-08-25 09:31:59,794 INFO basic [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-25 09:31:59,816 INFO basic [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-25 09:32:03,146 INFO basic [main] o.s.s.c.ThreadPoolTaskScheduler [ExecutorConfigurationSupport.java : 181] Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-08-25 09:32:03,733 INFO basic [main] o.c.b.s.b.s.w.f.LazyInitRegistration [LazyInitRegistration.java : 66] lazy initialized org.camunda.bpm.spring.boot.starter.webapp.filter.LazyProcessEnginesFilter@1a06602f
2025-08-25 09:32:03,984 INFO basic [main] o.c.b.s.b.s.w.f.LazyInitRegistration [LazyInitRegistration.java : 66] lazy initialized org.camunda.bpm.spring.boot.starter.webapp.filter.LazySecurityFilter@3b56947a
2025-08-25 09:32:06,189 INFO basic [main] o.a.coyote.http11.Http11NioProtocol [DirectJDKLog.java : 173] Starting ProtocolHandler ["http-nio-8882"]
2025-08-25 09:32:06,279 INFO basic [main] o.s.b.w.e.tomcat.TomcatWebServer [TomcatWebServer.java : 220] Tomcat started on port(s): 8882 (http) with context path '/basic'
2025-08-25 09:32:08,051 INFO basic [main] com.alibaba.nacos.client.naming [BeatReactor.java : 81] [BEAT] adding beat: BeatInfo{port=8882, ip='************', weight=1.0, serviceName='jx-mes@@jx-mes-basic-application', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-08-25 09:32:08,054 INFO basic [main] com.alibaba.nacos.client.naming [NamingProxy.java : 230] [REGISTER-SERVICE] public registering service jx-mes@@jx-mes-basic-application with instance: Instance{instanceId='null', ip='************', port=8882, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-08-25 09:32:08,720 INFO basic [main] c.a.c.n.r.NacosServiceRegistry [NacosServiceRegistry.java : 75] nacos registry, jx-mes jx-mes-basic-application ************:8882 register finished
2025-08-25 09:32:08,723 INFO basic [main] s.d.s.w.p.DocumentationPluginsBootstrapper [DocumentationPluginsBootstrapper.java : 93] Documentation plugins bootstrapped
2025-08-25 09:32:08,732 INFO basic [main] s.d.s.w.p.DocumentationPluginsBootstrapper [AbstractDocumentationPluginsBootstrapper.java : 79] Found 1 custom documentation plugin(s)
2025-08-25 09:32:08,871 INFO basic [main] s.d.s.w.s.ApiListingReferenceScanner [ApiListingReferenceScanner.java : 44] Scanning for api listing references
2025-08-25 09:32:09,295 INFO basic [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [HostReactor.java : 232] new ips(1) service: jx-mes@@jx-mes-basic-application@@DEFAULT -> [{"instanceId":"************#8882#DEFAULT#jx-mes@@jx-mes-basic-application","ip":"************","port":8882,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"jx-mes@@jx-mes-basic-application","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-08-25 09:32:09,325 INFO basic [com.alibaba.nacos.client.naming.updater] com.alibaba.nacos.client.naming [HostReactor.java : 271] current ips:(1) service: jx-mes@@jx-mes-basic-application@@DEFAULT -> [{"instanceId":"************#8882#DEFAULT#jx-mes@@jx-mes-basic-application","ip":"************","port":8882,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"jx-mes@@jx-mes-basic-application","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-08-25 09:32:09,423 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_1
2025-08-25 09:32:09,426 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_1
2025-08-25 09:32:09,471 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_1
2025-08-25 09:32:09,479 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_2
2025-08-25 09:32:09,554 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: saveOrUpdateUsingPOST_1
2025-08-25 09:32:09,627 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_1
2025-08-25 09:32:09,633 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_2
2025-08-25 09:32:09,652 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_2
2025-08-25 09:32:09,743 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_2
2025-08-25 09:32:09,747 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_3
2025-08-25 09:32:09,751 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_3
2025-08-25 09:32:09,772 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_3
2025-08-25 09:32:09,780 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryNoPageUsingPOST_1
2025-08-25 09:32:09,833 INFO basic [com.alibaba.nacos.naming.push.receiver] com.alibaba.nacos.client.naming [PushReceiver.java : 97] received push data: {"type":"dom","data":"{\"hosts\":[{\"ip\":\"************\",\"port\":8882,\"valid\":true,\"healthy\":true,\"marked\":false,\"instanceId\":\"************#8882#DEFAULT#jx-mes@@jx-mes-basic-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"enabled\":true,\"weight\":1.0,\"clusterName\":\"DEFAULT\",\"serviceName\":\"jx-mes@@jx-mes-basic-application\",\"ephemeral\":true}],\"dom\":\"jx-mes@@jx-mes-basic-application\",\"name\":\"jx-mes@@jx-mes-basic-application\",\"cacheMillis\":10000,\"lastRefTime\":1756085529800,\"checksum\":\"3c355c3df0c21ad0f5560d925e3e92f7\",\"useSpecifiedURL\":false,\"clusters\":\"DEFAULT\",\"env\":\"\",\"metadata\":{}}","lastRefTime":227057658145700} from /************
2025-08-25 09:32:09,848 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_4
2025-08-25 09:32:09,855 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_4
2025-08-25 09:32:09,869 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: saveOrUpdateUsingPOST_2
2025-08-25 09:32:09,934 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_3
2025-08-25 09:32:09,937 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_5
2025-08-25 09:32:09,940 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_5
2025-08-25 09:32:09,963 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_4
2025-08-25 09:32:09,969 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryNoPageUsingPOST_2
2025-08-25 09:32:09,981 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: menusUsingPOST_1
2025-08-25 09:32:10,141 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryAllUsingPOST_1
2025-08-25 09:32:10,171 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryAllUsingPOST_2
2025-08-25 09:32:10,200 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_6
2025-08-25 09:32:10,236 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_6
2025-08-25 09:32:10,281 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_7
2025-08-25 09:32:10,353 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_4
2025-08-25 09:32:10,365 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_7
2025-08-25 09:32:10,413 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_8
2025-08-25 09:32:10,438 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_5
2025-08-25 09:32:10,480 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_5
2025-08-25 09:32:10,485 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_9
2025-08-25 09:32:10,499 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_1
2025-08-25 09:32:10,534 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_6
2025-08-25 09:32:10,548 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_6
2025-08-25 09:32:10,553 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_10
2025-08-25 09:32:10,637 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_8
2025-08-25 09:32:10,747 INFO basic [main] o.s.s.quartz.SchedulerFactoryBean [SchedulerFactoryBean.java : 727] Starting Quartz Scheduler now
2025-08-25 09:32:10,749 INFO basic [main] org.quartz.core.QuartzScheduler [QuartzScheduler.java : 547] Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-25 09:32:17,856 INFO basic [main] com.huatek.frame.BasicApplication [StartupInfoLogger.java : 61] Started BasicApplication in 113.881 seconds (JVM running for 116.882)
2025-08-25 09:32:18,834 INFO basic [main] c.a.c.d.s.DubboMetadataServiceExporter [DubboMetadataServiceExporter.java : 85] The Dubbo service[<dubbo:service unexported="false" exported="true" />] has been exported.
2025-08-25 09:32:18,840 INFO basic [main] com.alibaba.nacos.client.naming [BeatReactor.java : 81] [BEAT] adding beat: BeatInfo{port=8882, ip='************', weight=1.0, serviceName='jx-mes@@jx-mes-basic-application', cluster='DEFAULT', metadata={dubbo.metadata-service.urls=[ "dubbo://************:20880/com.alibaba.cloud.dubbo.service.DubboMetadataService?anyhost=true&application=jx-mes-basic-application&bind.ip=************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&group=jx-mes-basic-application&interface=com.alibaba.cloud.dubbo.service.DubboMetadataService&methods=getAllServiceKeys,getServiceRestMetadata,getExportedURLs,getAllExportedURLs&pid=15748&qos.enable=false&release=2.7.8&revision=2.2.6.RELEASE&side=provider&timestamp=1756085537927&version=1.0.0" ], dubbo.metadata.revision=6C355C5967B045589A83251BF61D7297, dubbo.protocols.dubbo.port=20880, preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-08-25 09:32:18,841 INFO basic [main] com.alibaba.nacos.client.naming [NamingProxy.java : 230] [REGISTER-SERVICE] public registering service jx-mes@@jx-mes-basic-application with instance: Instance{instanceId='null', ip='************', port=8882, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={dubbo.metadata-service.urls=[ "dubbo://************:20880/com.alibaba.cloud.dubbo.service.DubboMetadataService?anyhost=true&application=jx-mes-basic-application&bind.ip=************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&group=jx-mes-basic-application&interface=com.alibaba.cloud.dubbo.service.DubboMetadataService&methods=getAllServiceKeys,getServiceRestMetadata,getExportedURLs,getAllExportedURLs&pid=15748&qos.enable=false&release=2.7.8&revision=2.2.6.RELEASE&side=provider&timestamp=1756085537927&version=1.0.0" ], dubbo.metadata.revision=6C355C5967B045589A83251BF61D7297, dubbo.protocols.dubbo.port=20880, preserved.register.source=SPRING_CLOUD}}
2025-08-25 09:32:18,853 INFO basic [main] c.a.c.n.r.NacosServiceRegistry [NacosServiceRegistry.java : 75] nacos registry, jx-mes jx-mes-basic-application ************:8882 register finished
2025-08-25 09:32:18,858 INFO basic [main] org.camunda.bpm.engine.jobexecutor [BaseLogger.java : 132] ENGINE-14014 Starting up the JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor].
2025-08-25 09:32:18,863 INFO basic [JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor]] org.camunda.bpm.engine.jobexecutor [BaseLogger.java : 132] ENGINE-14018 JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor] starting to acquire jobs
2025-08-25 09:32:18,871 INFO basic [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 169] [fixed-127.0.0.1_8848] [subscribe] jx-mes-basic-application.yml+jx-mes
2025-08-25 09:32:18,874 INFO basic [main] c.a.n.client.config.impl.CacheData [CacheData.java : 92] [fixed-127.0.0.1_8848] [add-listener] ok, tenant=, dataId=jx-mes-basic-application.yml, group=jx-mes, cnt=1
2025-08-25 09:32:18,875 INFO basic [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 169] [fixed-127.0.0.1_8848] [subscribe] jx-mes-basic-application-dev.yml+jx-mes
2025-08-25 09:32:18,876 INFO basic [main] c.a.n.client.config.impl.CacheData [CacheData.java : 92] [fixed-127.0.0.1_8848] [add-listener] ok, tenant=, dataId=jx-mes-basic-application-dev.yml, group=jx-mes, cnt=1
2025-08-25 09:32:18,877 INFO basic [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 169] [fixed-127.0.0.1_8848] [subscribe] jx-mes-basic-application+jx-mes
2025-08-25 09:32:18,877 INFO basic [main] c.a.n.client.config.impl.CacheData [CacheData.java : 92] [fixed-127.0.0.1_8848] [add-listener] ok, tenant=, dataId=jx-mes-basic-application, group=jx-mes, cnt=1
2025-08-25 09:32:19,862 INFO basic [com.alibaba.nacos.naming.push.receiver] com.alibaba.nacos.client.naming [PushReceiver.java : 97] received push data: {"type":"dom","data":"{\"hosts\":[{\"ip\":\"************\",\"port\":8882,\"valid\":true,\"healthy\":true,\"marked\":false,\"instanceId\":\"************#8882#DEFAULT#jx-mes@@jx-mes-basic-application\",\"metadata\":{\"dubbo.metadata-service.urls\":\"[ \\\"dubbo://************:20880/com.alibaba.cloud.dubbo.service.DubboMetadataService?anyhost=true&application=jx-mes-basic-application&bind.ip=************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&group=jx-mes-basic-application&interface=com.alibaba.cloud.dubbo.service.DubboMetadataService&methods=getAllServiceKeys,getServiceRestMetadata,getExportedURLs,getAllExportedURLs&pid=15748&qos.enable=false&release=2.7.8&revision=2.2.6.RELEASE&side=provider&timestamp=1756085537927&version=1.0.0\\\" ]\",\"dubbo.metadata.revision\":\"6C355C5967B045589A83251BF61D7297\",\"dubbo.protocols.dubbo.port\":\"20880\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"enabled\":true,\"weight\":1.0,\"clusterName\":\"DEFAULT\",\"serviceName\":\"jx-mes@@jx-mes-basic-application\",\"ephemeral\":true}],\"dom\":\"jx-mes@@jx-mes-basic-application\",\"name\":\"jx-mes@@jx-mes-basic-application\",\"cacheMillis\":10000,\"lastRefTime\":1756085539859,\"checksum\":\"3c355c3df0c21ad0f5560d925e3e92f7\",\"useSpecifiedURL\":false,\"clusters\":\"DEFAULT\",\"env\":\"\",\"metadata\":{}}","lastRefTime":227067717327500} from /************
2025-08-25 09:32:19,864 INFO basic [com.alibaba.nacos.naming.push.receiver] com.alibaba.nacos.client.naming [BeatReactor.java : 81] [BEAT] adding beat: BeatInfo{port=8882, ip='************', weight=1.0, serviceName='jx-mes@@jx-mes-basic-application', cluster='DEFAULT', metadata={dubbo.metadata-service.urls=[ "dubbo://************:20880/com.alibaba.cloud.dubbo.service.DubboMetadataService?anyhost=true&application=jx-mes-basic-application&bind.ip=************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&group=jx-mes-basic-application&interface=com.alibaba.cloud.dubbo.service.DubboMetadataService&methods=getAllServiceKeys,getServiceRestMetadata,getExportedURLs,getAllExportedURLs&pid=15748&qos.enable=false&release=2.7.8&revision=2.2.6.RELEASE&side=provider&timestamp=1756085537927&version=1.0.0" ], dubbo.metadata.revision=6C355C5967B045589A83251BF61D7297, dubbo.protocols.dubbo.port=20880, preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-08-25 09:32:19,865 INFO basic [com.alibaba.nacos.naming.push.receiver] com.alibaba.nacos.client.naming [HostReactor.java : 245] modified ips(1) service: jx-mes@@jx-mes-basic-application@@DEFAULT -> [{"instanceId":"************#8882#DEFAULT#jx-mes@@jx-mes-basic-application","ip":"************","port":8882,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"jx-mes@@jx-mes-basic-application","metadata":{"dubbo.metadata-service.urls":"[ \"dubbo://************:20880/com.alibaba.cloud.dubbo.service.DubboMetadataService?anyhost=true&application=jx-mes-basic-application&bind.ip=************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&group=jx-mes-basic-application&interface=com.alibaba.cloud.dubbo.service.DubboMetadataService&methods=getAllServiceKeys,getServiceRestMetadata,getExportedURLs,getAllExportedURLs&pid=15748&qos.enable=false&release=2.7.8&revision=2.2.6.RELEASE&side=provider&timestamp=1756085537927&version=1.0.0\" ]","dubbo.metadata.revision":"6C355C5967B045589A83251BF61D7297","dubbo.protocols.dubbo.port":"20880","preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-08-25 09:32:19,869 INFO basic [com.alibaba.nacos.naming.push.receiver] com.alibaba.nacos.client.naming [HostReactor.java : 271] current ips:(1) service: jx-mes@@jx-mes-basic-application@@DEFAULT -> [{"instanceId":"************#8882#DEFAULT#jx-mes@@jx-mes-basic-application","ip":"************","port":8882,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"jx-mes@@jx-mes-basic-application","metadata":{"dubbo.metadata-service.urls":"[ \"dubbo://************:20880/com.alibaba.cloud.dubbo.service.DubboMetadataService?anyhost=true&application=jx-mes-basic-application&bind.ip=************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&group=jx-mes-basic-application&interface=com.alibaba.cloud.dubbo.service.DubboMetadataService&methods=getAllServiceKeys,getServiceRestMetadata,getExportedURLs,getAllExportedURLs&pid=15748&qos.enable=false&release=2.7.8&revision=2.2.6.RELEASE&side=provider&timestamp=1756085537927&version=1.0.0\" ]","dubbo.metadata.revision":"6C355C5967B045589A83251BF61D7297","dubbo.protocols.dubbo.port":"20880","preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-08-25 09:33:36,319 INFO basic [Nacos-Watch-Task-Scheduler-1] com.alibaba.nacos.client.naming [HostReactor.java : 232] new ips(1) service: jx-mes@@jx-mes-gateway -> [{"instanceId":"************#8881#DEFAULT#jx-mes@@jx-mes-gateway","ip":"************","port":8881,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"jx-mes@@jx-mes-gateway","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-08-25 09:33:36,321 INFO basic [Nacos-Watch-Task-Scheduler-1] com.alibaba.nacos.client.naming [HostReactor.java : 271] current ips:(1) service: jx-mes@@jx-mes-gateway -> [{"instanceId":"************#8881#DEFAULT#jx-mes@@jx-mes-gateway","ip":"************","port":8881,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"jx-mes@@jx-mes-gateway","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-08-25 09:33:36,321 INFO basic [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] c.a.c.d.a.DubboServiceDiscoveryAutoConfiguration [DubboServiceDiscoveryAutoConfiguration.java : 171] The event of the service instances[name : jx-mes-gateway , size : 1] change is about to be dispatched
2025-08-25 09:35:34,192 INFO basic [main] com.huatek.frame.BusinessApplication [SpringApplication.java : 655] The following profiles are active: dev
2025-08-25 09:35:38,832 INFO basic [main] c.alibaba.spring.util.BeanRegistrar [BeanRegistrar.java : 67] The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.ReferenceAnnotationBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [referenceAnnotationBeanPostProcessor] has been registered.
2025-08-25 09:35:38,833 INFO basic [main] c.alibaba.spring.util.BeanRegistrar [BeanRegistrar.java : 67] The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.annotation.DubboConfigAliasPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigAliasPostProcessor] has been registered.
2025-08-25 09:35:38,835 INFO basic [main] c.alibaba.spring.util.BeanRegistrar [BeanRegistrar.java : 67] The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboLifecycleComponentApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboLifecycleComponentApplicationListener] has been registered.
2025-08-25 09:35:38,836 INFO basic [main] c.alibaba.spring.util.BeanRegistrar [BeanRegistrar.java : 67] The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2025-08-25 09:35:38,838 INFO basic [main] c.alibaba.spring.util.BeanRegistrar [BeanRegistrar.java : 67] The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.beans.factory.config.DubboConfigDefaultPropertyValueBeanPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboConfigDefaultPropertyValueBeanPostProcessor] has been registered.
2025-08-25 09:35:38,983 INFO basic [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 249] Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-25 09:35:38,993 INFO basic [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 127] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-25 09:35:39,264 INFO basic [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 187] Finished Spring Data repository scanning in 223ms. Found 0 Redis repository interfaces.
2025-08-25 09:35:39,598 INFO basic [main] c.a.s.b.f.a.ConfigurationBeanBindingRegistrar [ConfigurationBeanBindingRegistrar.java : 139] The configuration bean definition [name : org.apache.dubbo.config.ApplicationConfig#0, content : Root bean: class [org.apache.dubbo.config.ApplicationConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2025-08-25 09:35:39,599 INFO basic [main] c.alibaba.spring.util.BeanRegistrar [BeanRegistrar.java : 67] The Infrastructure bean definition [Root bean: class [com.alibaba.spring.beans.factory.annotation.ConfigurationBeanBindingPostProcessor]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [configurationBeanBindingPostProcessor] has been registered.
2025-08-25 09:35:39,599 INFO basic [main] c.a.s.b.f.a.ConfigurationBeanBindingRegistrar [ConfigurationBeanBindingRegistrar.java : 139] The configuration bean definition [name : org.apache.dubbo.config.RegistryConfig#0, content : Root bean: class [org.apache.dubbo.config.RegistryConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2025-08-25 09:35:39,599 INFO basic [main] c.a.s.b.f.a.ConfigurationBeanBindingRegistrar [ConfigurationBeanBindingRegistrar.java : 139] The configuration bean definition [name : org.apache.dubbo.config.ProtocolConfig#0, content : Root bean: class [org.apache.dubbo.config.ProtocolConfig]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=null] has been registered.
2025-08-25 09:35:39,815 INFO basic [main] c.alibaba.spring.util.BeanRegistrar [BeanRegistrar.java : 67] The Infrastructure bean definition [Root bean: class [org.apache.dubbo.config.spring.context.DubboBootstrapApplicationListener]; scope=; abstract=false; lazyInit=null; autowireMode=0; dependencyCheck=0; autowireCandidate=true; primary=false; factoryBeanName=null; factoryMethodName=null; initMethodName=null; destroyMethodName=nullwith name [dubboBootstrapApplicationListener] has been registered.
2025-08-25 09:35:41,008 INFO basic [main] o.s.c.a.ConfigurationClassPostProcessor [ConfigurationClassPostProcessor.java : 403] Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboRelaxedBinding2AutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2025-08-25 09:35:41,008 INFO basic [main] o.s.c.a.ConfigurationClassPostProcessor [ConfigurationClassPostProcessor.java : 403] Cannot enhance @Configuration bean definition 'org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration' since its singleton instance has been created too early. The typical cause is a non-static @Bean method with a BeanDefinitionRegistryPostProcessor return type: Consider declaring such methods as 'static'.
2025-08-25 09:35:41,265 INFO basic [main] o.s.cloud.context.scope.GenericScope [GenericScope.java : 295] BeanFactory id=4dd7dbc1-32f7-3934-9b10-157de7a18257
2025-08-25 09:35:41,437 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'asyncTaskProperties' of type [com.huatek.frame.common.utils.thread.AsyncTaskProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:41,453 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'asyncTaskExecutePool' of type [com.huatek.frame.common.utils.thread.AsyncTaskExecutePool$$EnhancerBySpringCGLIB$$fd32a68e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:42,563 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:42,596 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$e801d02a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:42,672 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:42,732 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'redisConfig' of type [com.huatek.frame.common.conf.RedisConfig$$EnhancerBySpringCGLIB$$df7a9bd8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:43,088 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.autoconfigure.DubboOpenFeignAutoConfiguration' of type [com.alibaba.cloud.dubbo.autoconfigure.DubboOpenFeignAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:43,122 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'dubbo.cloud-com.alibaba.cloud.dubbo.env.DubboCloudProperties' of type [com.alibaba.cloud.dubbo.env.DubboCloudProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:43,138 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.autoconfigure.DubboServiceAutoConfiguration' of type [com.alibaba.cloud.dubbo.autoconfigure.DubboServiceAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:43,148 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'dubboGenericServiceFactory' of type [com.alibaba.cloud.dubbo.service.DubboGenericServiceFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:43,159 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.util.JSONUtils' of type [com.alibaba.cloud.dubbo.util.JSONUtils] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:43,167 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.util.DubboMetadataUtils' of type [com.alibaba.cloud.dubbo.util.DubboMetadataUtils] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:43,209 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'metadataJsonResolver' of type [com.alibaba.cloud.dubbo.metadata.resolver.DubboServiceBeanMetadataResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:43,257 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'relaxedDubboConfigBinder' of type [org.apache.dubbo.spring.boot.autoconfigure.BinderDubboConfigBinder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:43,269 INFO basic [main] c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor [ConfigurationBeanBindingPostProcessor.java : 159] The configuration bean [<dubbo:application name="jx-mes-business-application" hostname="03-225" qosEnable="false" />] have been binding by the configuration properties [{name=jx-mes-business-application, qos-enable=false}]
2025-08-25 09:35:43,274 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.apache.dubbo.config.ApplicationConfig#0' of type [org.apache.dubbo.config.ApplicationConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:43,304 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'dubboProtocolConfigSupplier' of type [com.alibaba.cloud.dubbo.metadata.DubboProtocolConfigSupplier] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:43,312 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.service.DubboMetadataServiceExporter' of type [com.alibaba.cloud.dubbo.service.DubboMetadataServiceExporter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:43,314 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.autoconfigure.DubboMetadataAutoConfiguration' of type [com.alibaba.cloud.dubbo.autoconfigure.DubboMetadataAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:43,330 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'metadataServiceInstanceSelector' of type [com.alibaba.cloud.dubbo.metadata.repository.RandomServiceInstanceSelector] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:43,349 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClientAutoConfiguration' of type [org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClientAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:43,366 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.nacos.discovery.NacosDiscoveryClientConfiguration' of type [com.alibaba.cloud.nacos.discovery.NacosDiscoveryClientConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:43,383 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.nacos.discovery.NacosDiscoveryAutoConfiguration' of type [com.alibaba.cloud.nacos.discovery.NacosDiscoveryAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:43,418 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.cloud.commons.util.UtilAutoConfiguration' of type [org.springframework.cloud.commons.util.UtilAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:43,439 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'inetUtilsProperties' of type [org.springframework.cloud.commons.util.InetUtilsProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:43,450 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'inetUtils' of type [org.springframework.cloud.commons.util.InetUtils] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:43,468 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.nacos.NacosServiceAutoConfiguration' of type [com.alibaba.cloud.nacos.NacosServiceAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:43,478 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'nacosServiceManager' of type [com.alibaba.cloud.nacos.NacosServiceManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:44,346 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'nacosProperties' of type [com.alibaba.cloud.nacos.NacosDiscoveryProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:44,365 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'nacosServiceDiscovery' of type [com.alibaba.cloud.nacos.discovery.NacosServiceDiscovery] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:44,383 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'nacosDiscoveryClient' of type [com.alibaba.cloud.nacos.discovery.NacosDiscoveryClient] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:44,530 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'server-org.springframework.boot.autoconfigure.web.ServerProperties' of type [org.springframework.boot.autoconfigure.web.ServerProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:44,546 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration' of type [org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:45,761 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'simpleDiscoveryProperties' of type [org.springframework.cloud.client.discovery.simple.SimpleDiscoveryProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:45,777 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'simpleDiscoveryClient' of type [org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClient] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:45,795 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'compositeDiscoveryClient' of type [org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClient] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:45,809 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.service.DubboMetadataServiceProxy' of type [com.alibaba.cloud.dubbo.service.DubboMetadataServiceProxy] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:45,833 INFO basic [main] com.alibaba.nacos.client.naming [InitUtils.java : 65] initializer namespace from System Property :null
2025-08-25 09:35:45,836 INFO basic [main] com.alibaba.nacos.client.naming [InitUtils.java : 74] initializer namespace from System Environment :null
2025-08-25 09:35:45,838 INFO basic [main] com.alibaba.nacos.client.naming [InitUtils.java : 84] initializer namespace from System Property :null
2025-08-25 09:35:46,093 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.metadata.repository.DubboServiceMetadataRepository' of type [com.alibaba.cloud.dubbo.metadata.repository.DubboServiceMetadataRepository] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,194 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.service.parameter.RequestParamServiceParameterResolver' of type [com.alibaba.cloud.dubbo.service.parameter.RequestParamServiceParameterResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,242 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration' of type [org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,260 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration' of type [org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,287 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'stringHttpMessageConverter' of type [org.springframework.http.converter.StringHttpMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,307 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration' of type [org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,318 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,333 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,355 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,387 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,405 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,424 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,487 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,513 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,582 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,622 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,652 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,671 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,784 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,892 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'mappingJackson2HttpMessageConverter' of type [org.springframework.http.converter.json.MappingJackson2HttpMessageConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,930 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'messageConverters' of type [org.springframework.boot.autoconfigure.http.HttpMessageConverters] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,940 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.service.parameter.RequestBodyServiceParameterResolver' of type [com.alibaba.cloud.dubbo.service.parameter.RequestBodyServiceParameterResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,963 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.service.parameter.RequestHeaderServiceParameterResolver' of type [com.alibaba.cloud.dubbo.service.parameter.RequestHeaderServiceParameterResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,984 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.service.parameter.PathVariableServiceParameterResolver' of type [com.alibaba.cloud.dubbo.service.parameter.PathVariableServiceParameterResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:46,988 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.dubbo.service.DubboGenericServiceExecutionContextFactory' of type [com.alibaba.cloud.dubbo.service.DubboGenericServiceExecutionContextFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:47,213 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:47,345 INFO basic [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:35:48,560 INFO basic [main] o.s.b.w.e.tomcat.TomcatWebServer [TomcatWebServer.java : 108] Tomcat initialized with port(s): 8886 (http)
2025-08-25 09:35:48,598 INFO basic [main] o.a.coyote.http11.Http11NioProtocol [DirectJDKLog.java : 173] Initializing ProtocolHandler ["http-nio-8886"]
2025-08-25 09:35:48,600 INFO basic [main] o.a.catalina.core.StandardService [DirectJDKLog.java : 173] Starting service [Tomcat]
2025-08-25 09:35:48,600 INFO basic [main] o.a.catalina.core.StandardEngine [DirectJDKLog.java : 173] Starting Servlet engine: [Apache Tomcat/9.0.46]
2025-08-25 09:35:48,812 INFO basic [main] o.a.c.c.C.[.[localhost].[/business] [DirectJDKLog.java : 173] Initializing Spring embedded WebApplicationContext
2025-08-25 09:35:48,813 INFO basic [main] o.s.b.w.s.c.ServletWebServerApplicationContext [ServletWebServerApplicationContext.java : 285] Root WebApplicationContext: initialization completed in 14578 ms
2025-08-25 09:35:49,094 INFO basic [main] o.c.b.s.b.s.r.CamundaJerseyResourceConfig [CamundaJerseyResourceConfig.java : 38] Configuring camunda rest api.
2025-08-25 09:35:49,201 INFO basic [main] o.c.b.s.b.s.r.CamundaJerseyResourceConfig [CamundaJerseyResourceConfig.java : 44] Finished configuring camunda rest api.
2025-08-25 09:35:51,261 INFO basic [main] org.redisson.Version [Version.java : 41] Redisson 3.17.4
2025-08-25 09:35:53,525 INFO basic [redisson-netty-2-7] o.r.c.p.MasterPubSubConnectionPool [ConnectionPool.java : 158] 1 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-25 09:35:53,613 INFO basic [redisson-netty-2-19] o.r.c.pool.MasterConnectionPool [ConnectionPool.java : 158] 24 connections initialized for 127.0.0.1/127.0.0.1:6379
2025-08-25 09:36:00,066 INFO basic [main] c.alibaba.druid.pool.DruidDataSource [DruidDataSource.java : 1003] {dataSource-1,master} inited
2025-08-25 09:36:01,041 INFO basic [main] c.alibaba.druid.pool.DruidDataSource [DruidDataSource.java : 1003] {dataSource-2,slave} inited
2025-08-25 09:36:01,042 INFO basic [main] c.b.d.d.DynamicRoutingDataSource [DynamicRoutingDataSource.java : 154] dynamic-datasource - add a datasource named [slave] success
2025-08-25 09:36:01,043 INFO basic [main] c.b.d.d.DynamicRoutingDataSource [DynamicRoutingDataSource.java : 154] dynamic-datasource - add a datasource named [master] success
2025-08-25 09:36:01,043 INFO basic [main] c.b.d.d.DynamicRoutingDataSource [DynamicRoutingDataSource.java : 234] dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-25 09:36:04,627 INFO basic [http-nio-8882-exec-2] o.a.c.c.C.[.[localhost].[/basic] [DirectJDKLog.java : 173] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-25 09:36:04,628 INFO basic [http-nio-8882-exec-2] o.s.web.servlet.DispatcherServlet [FrameworkServlet.java : 525] Initializing Servlet 'dispatcherServlet'
2025-08-25 09:36:04,749 INFO basic [http-nio-8882-exec-2] o.s.web.servlet.DispatcherServlet [FrameworkServlet.java : 547] Completed initialization in 119 ms
2025-08-25 09:36:05,812 INFO basic [http-nio-8882-exec-2] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"用户资料","logType":"INFO","method":"com.huatek.frame.modules.system.rest.UserController.profile()","params":"[\"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ7XCJncm91cElkXCI6XCIxOTQzNjE3MzEwOTk2Njk3MDg4XCIsXCJpZFwiOlwiZjUyYjFmOTMyOWNiNGNkNjhkMmZjNjYwZDljMmFhYzVcIixcInVzZXJOYW1lXCI6XCJhZG1pblwiLFwidXNlckNvZGVcIjpcImFkbWluXCJ9IiwianRpIjoiYTM4MGFkMjYtY2FjNi00Y2RlLTk0MGEtOWQwNWFiYjgwYjllIn0.HA4ks_-uI7BMNI9giUr-txFqoEgmD85BnWTRv7ElkjYKixZ33mkI9W2uh3RTsoUzU2nZXjsJfVfe0I38CzXI-g\"]","platformType":"","requestIp":"************","tableName":"","times":308,"userName":"admin"}
2025-08-25 09:36:05,813 INFO basic [http-nio-8882-exec-3] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"系统_设置","logType":"INFO","method":"com.huatek.frame.modules.system.rest.ConfigController.query()","params":"[]","platformType":"Torch 平台","requestIp":"************","tableName":"sys_config","times":302,"userName":"admin"}
2025-08-25 09:36:05,820 INFO basic [http-nio-8882-exec-1] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"主题设置详情","logType":"INFO","method":"com.huatek.frame.modules.system.rest.ThemeConfigController.detail()","params":"[]","platformType":"","requestIp":"************","tableName":"","times":308,"userName":"admin"}
2025-08-25 09:36:06,254 INFO basic [http-nio-8882-exec-4] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"获取用户许可资源","logType":"INFO","method":"com.huatek.frame.modules.system.rest.OauthController.menus()","params":"[\"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ7XCJncm91cElkXCI6XCIxOTQzNjE3MzEwOTk2Njk3MDg4XCIsXCJpZFwiOlwiZjUyYjFmOTMyOWNiNGNkNjhkMmZjNjYwZDljMmFhYzVcIixcInVzZXJOYW1lXCI6XCJhZG1pblwiLFwidXNlckNvZGVcIjpcImFkbWluXCJ9IiwianRpIjoiYTM4MGFkMjYtY2FjNi00Y2RlLTk0MGEtOWQwNWFiYjgwYjllIn0.HA4ks_-uI7BMNI9giUr-txFqoEgmD85BnWTRv7ElkjYKixZ33mkI9W2uh3RTsoUzU2nZXjsJfVfe0I38CzXI-g\"]","platformType":"","requestIp":"************","tableName":"","times":7,"userName":"admin"}
2025-08-25 09:36:15,444 INFO basic [main] c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor [ConfigurationBeanBindingPostProcessor.java : 159] The configuration bean [<dubbo:registry address="spring-cloud://localhost" protocol="spring-cloud" port="0" />] have been binding by the configuration properties [{address=spring-cloud://localhost}]
2025-08-25 09:36:15,480 INFO basic [main] c.a.s.b.f.a.ConfigurationBeanBindingPostProcessor [ConfigurationBeanBindingPostProcessor.java : 159] The configuration bean [<dubbo:protocol name="dubbo" port="-1" />] have been binding by the configuration properties [{name=dubbo, port=-1}]
2025-08-25 09:36:15,514 INFO basic [main] o.a.d.c.s.b.f.a.ReferenceBeanBuilder [AnnotatedInterfaceConfigBeanBuilder.java : 84] The configBean[type:ReferenceBean] has been built.
2025-08-25 09:36:15,627 INFO basic [main] o.a.d.c.s.b.f.a.ReferenceBeanBuilder [AnnotatedInterfaceConfigBeanBuilder.java : 84] The configBean[type:ReferenceBean] has been built.
2025-08-25 09:36:16,783 INFO basic [main] com.alibaba.nacos.client.naming [HostReactor.java : 232] new ips(1) service: jx-mes@@jx-mes-basic-application -> [{"instanceId":"************#8882#DEFAULT#jx-mes@@jx-mes-basic-application","ip":"************","port":8882,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"jx-mes@@jx-mes-basic-application","metadata":{"dubbo.metadata-service.urls":"[ \"dubbo://************:20880/com.alibaba.cloud.dubbo.service.DubboMetadataService?anyhost=true&application=jx-mes-basic-application&bind.ip=************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&group=jx-mes-basic-application&interface=com.alibaba.cloud.dubbo.service.DubboMetadataService&methods=getAllServiceKeys,getServiceRestMetadata,getExportedURLs,getAllExportedURLs&pid=15748&qos.enable=false&release=2.7.8&revision=2.2.6.RELEASE&side=provider&timestamp=1756085537927&version=1.0.0\" ]","dubbo.metadata.revision":"6C355C5967B045589A83251BF61D7297","dubbo.protocols.dubbo.port":"20880","preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-08-25 09:36:16,802 INFO basic [main] com.alibaba.nacos.client.naming [HostReactor.java : 271] current ips:(1) service: jx-mes@@jx-mes-basic-application -> [{"instanceId":"************#8882#DEFAULT#jx-mes@@jx-mes-basic-application","ip":"************","port":8882,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"jx-mes@@jx-mes-basic-application","metadata":{"dubbo.metadata-service.urls":"[ \"dubbo://************:20880/com.alibaba.cloud.dubbo.service.DubboMetadataService?anyhost=true&application=jx-mes-basic-application&bind.ip=************&bind.port=20880&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&group=jx-mes-basic-application&interface=com.alibaba.cloud.dubbo.service.DubboMetadataService&methods=getAllServiceKeys,getServiceRestMetadata,getExportedURLs,getAllExportedURLs&pid=15748&qos.enable=false&release=2.7.8&revision=2.2.6.RELEASE&side=provider&timestamp=1756085537927&version=1.0.0\" ]","dubbo.metadata.revision":"6C355C5967B045589A83251BF61D7297","dubbo.protocols.dubbo.port":"20880","preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-08-25 09:36:16,827 INFO basic [main] com.alibaba.nacos.client.naming [HostReactor.java : 232] new ips(1) service: jx-mes@@jx-mes-gateway -> [{"instanceId":"************#8881#DEFAULT#jx-mes@@jx-mes-gateway","ip":"************","port":8881,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"jx-mes@@jx-mes-gateway","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-08-25 09:36:16,829 INFO basic [main] com.alibaba.nacos.client.naming [HostReactor.java : 271] current ips:(1) service: jx-mes@@jx-mes-gateway -> [{"instanceId":"************#8881#DEFAULT#jx-mes@@jx-mes-gateway","ip":"************","port":8881,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"jx-mes@@jx-mes-gateway","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-08-25 09:36:16,840 INFO basic [main] c.a.c.d.s.DubboMetadataServiceProxy [DubboMetadataServiceProxy.java : 187] The metadata of Dubbo service[name : jx-mes-basic-application] is about to be initialized
2025-08-25 09:36:17,664 INFO basic [main] c.a.c.d.m.r.DubboServiceMetadataRepository [DubboServiceMetadataRepository.java : 253] The metadata of Dubbo services has been initialized
2025-08-25 09:36:17,664 INFO basic [main] c.a.c.d.registry.DubboCloudRegistry [DubboCloudRegistry.java : 156] DubboCloudRegistry preInit Done.
2025-08-25 09:36:17,718 INFO basic [main] o.a.d.c.s.b.f.a.ReferenceBeanBuilder [AnnotatedInterfaceConfigBeanBuilder.java : 84] The configBean[type:ReferenceBean] has been built.
2025-08-25 09:36:18,740 INFO basic [main] org.camunda.bpm.spring.boot [BaseLogger.java : 132] STARTER-SB040 Setting up jobExecutor with corePoolSize=3, maxPoolSize:10
2025-08-25 09:36:18,773 INFO basic [main] o.s.s.c.ThreadPoolTaskExecutor [ExecutorConfigurationSupport.java : 181] Initializing ExecutorService 'camundaTaskExecutor'
2025-08-25 09:36:19,428 INFO basic [main] org.camunda.bpm.engine.cfg [BaseLogger.java : 132] ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaJobConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, failedJobConfiguration, eventPublisherPlugin, SpringBootSpinProcessEnginePlugin]' activated on process engine 'default'
2025-08-25 09:36:20,252 INFO basic [main] org.camunda.bpm.spring.boot [BaseLogger.java : 132] STARTER-SB021 Auto-Deploying resources: []
2025-08-25 09:36:20,259 INFO basic [main] o.c.b.s.b.s.e.EventPublisherPlugin [EventPublisherPlugin.java : 57] EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-08-25 09:36:20,259 INFO basic [main] o.c.b.s.b.s.e.EventPublisherPlugin [EventPublisherPlugin.java : 59] EVENTING-003: Task events will be published as Spring Events.
2025-08-25 09:36:20,260 INFO basic [main] o.c.b.s.b.s.e.EventPublisherPlugin [EventPublisherPlugin.java : 65] EVENTING-005: Execution events will be published as Spring Events.
2025-08-25 09:36:20,265 INFO basic [main] o.c.b.s.b.s.e.EventPublisherPlugin [EventPublisherPlugin.java : 74] EVENTING-007: History events will be published as Spring events.
2025-08-25 09:36:20,277 INFO basic [main] org.camunda.spin [BaseLogger.java : 132] SPIN-01010 Discovered Spin data format provider: org.camunda.spin.impl.json.jackson.format.JacksonJsonDataFormatProvider[name = application/json]
2025-08-25 09:36:20,977 INFO basic [main] org.camunda.spin [BaseLogger.java : 132] SPIN-01010 Discovered Spin data format provider: org.camunda.spin.impl.xml.dom.format.DomXmlDataFormatProvider[name = application/xml]
2025-08-25 09:36:20,999 INFO basic [main] org.camunda.spin [BaseLogger.java : 132] SPIN-01009 Discovered Spin data format: org.camunda.spin.impl.xml.dom.format.DomXmlDataFormat[name = application/xml]
2025-08-25 09:36:21,001 INFO basic [main] org.camunda.spin [BaseLogger.java : 132] SPIN-01009 Discovered Spin data format: org.camunda.spin.impl.json.jackson.format.JacksonJsonDataFormat[name = application/json]
2025-08-25 09:36:21,692 INFO basic [main] org.camunda.bpm.dmn.feel.scala [BaseLogger.java : 132] FEEL/SCALA-01001 Spin value mapper detected
2025-08-25 09:36:22,186 INFO basic [main] org.camunda.feel.FeelEngine [FeelEngine.scala : 123] Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@42c5d861, org.camunda.spin.plugin.impl.feel.integration.SpinValueMapper@2d2690f6)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@387c5d79, clock: SystemClock, configuration: Configuration(false)]
2025-08-25 09:36:26,560 INFO basic [http-nio-8882-exec-6] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"表单页筛选器配置详情","logType":"INFO","method":"com.huatek.frame.modules.system.rest.FormFilterConfigController.detail()","params":"[\"CapabilityDevelopment\",\"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ7XCJncm91cElkXCI6XCIxOTQzNjE3MzEwOTk2Njk3MDg4XCIsXCJpZFwiOlwiZjUyYjFmOTMyOWNiNGNkNjhkMmZjNjYwZDljMmFhYzVcIixcInVzZXJOYW1lXCI6XCJhZG1pblwiLFwidXNlckNvZGVcIjpcImFkbWluXCJ9IiwianRpIjoiYTM4MGFkMjYtY2FjNi00Y2RlLTk0MGEtOWQwNWFiYjgwYjllIn0.HA4ks_-uI7BMNI9giUr-txFqoEgmD85BnWTRv7ElkjYKixZ33mkI9W2uh3RTsoUzU2nZXjsJfVfe0I38CzXI-g\"]","platformType":"","requestIp":"************","tableName":"","times":25,"userName":"admin"}
2025-08-25 09:36:26,955 INFO basic [http-nio-8882-exec-7] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"capability_development_developmentTeam\"]","platformType":"","requestIp":"************","tableName":"","times":4,"userName":"admin"}
2025-08-25 09:36:27,014 INFO basic [http-nio-8882-exec-5] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"capability_asset_capabilityType\"]","platformType":"","requestIp":"************","tableName":"","times":3,"userName":"admin"}
2025-08-25 09:36:27,085 INFO basic [http-nio-8882-exec-3] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"capability_development_status\"]","platformType":"","requestIp":"************","tableName":"","times":4,"userName":"admin"}
2025-08-25 09:36:27,152 INFO basic [http-nio-8882-exec-2] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"capability_development_source\"]","platformType":"","requestIp":"************","tableName":"","times":4,"userName":"admin"}
2025-08-25 09:36:27,229 INFO basic [http-nio-8882-exec-1] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"capability_asset_applicableTestType\"]","platformType":"","requestIp":"************","tableName":"","times":4,"userName":"admin"}
2025-08-25 09:36:27,299 INFO basic [http-nio-8882-exec-4] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"equipment_inventory_deviceCategory\"]","platformType":"","requestIp":"************","tableName":"","times":4,"userName":"admin"}
2025-08-25 09:36:28,972 INFO basic [main] org.camunda.bpm.connect [BaseLogger.java : 132] CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-08-25 09:36:28,979 INFO basic [main] org.camunda.bpm.connect [BaseLogger.java : 132] CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-08-25 09:36:31,121 INFO basic [main] org.camunda.bpm.engine [BaseLogger.java : 132] ENGINE-00001 Process Engine default created.
2025-08-25 09:36:32,601 INFO basic [main] o.a.d.c.s.b.f.a.ReferenceBeanBuilder [AnnotatedInterfaceConfigBeanBuilder.java : 84] The configBean[type:ReferenceBean] has been built.
2025-08-25 09:36:33,438 INFO basic [main] o.a.d.c.s.b.f.a.ReferenceBeanBuilder [AnnotatedInterfaceConfigBeanBuilder.java : 84] The configBean[type:ReferenceBean] has been built.
2025-08-25 09:36:35,185 INFO basic [main] o.a.d.c.s.b.f.a.ReferenceBeanBuilder [AnnotatedInterfaceConfigBeanBuilder.java : 84] The configBean[type:ReferenceBean] has been built.
2025-08-25 09:36:36,722 INFO basic [main] o.a.d.c.s.b.f.a.ReferenceBeanBuilder [AnnotatedInterfaceConfigBeanBuilder.java : 84] The configBean[type:ReferenceBean] has been built.
2025-08-25 09:36:36,776 INFO basic [main] o.a.d.c.s.b.f.a.ReferenceBeanBuilder [AnnotatedInterfaceConfigBeanBuilder.java : 84] The configBean[type:ReferenceBean] has been built.
2025-08-25 09:36:37,177 INFO basic [main] o.a.d.c.s.b.f.a.ReferenceBeanBuilder [AnnotatedInterfaceConfigBeanBuilder.java : 84] The configBean[type:ReferenceBean] has been built.
2025-08-25 09:36:38,433 INFO basic [main] c.a.c.s.SentinelWebAutoConfiguration [SentinelWebAutoConfiguration.java : 80] [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
2025-08-25 09:36:39,311 INFO basic [main] s.d.s.w.WebMvcPropertySourcedRequestMappingHandlerMapping [WebMvcPropertySourcedRequestMappingHandlerMapping.java : 69] Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
2025-08-25 09:36:39,812 INFO basic [main] o.s.s.c.ThreadPoolTaskExecutor [ExecutorConfigurationSupport.java : 181] Initializing ExecutorService
2025-08-25 09:36:39,815 INFO basic [main] o.s.s.c.ThreadPoolTaskExecutor [ExecutorConfigurationSupport.java : 181] Initializing ExecutorService 'opsLogExecutor'
2025-08-25 09:36:40,445 INFO basic [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-25 09:36:40,469 INFO basic [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-25 09:36:41,900 INFO basic [http-nio-8882-exec-10] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"表单页筛选器配置详情","logType":"INFO","method":"com.huatek.frame.modules.system.rest.FormFilterConfigController.detail()","params":"[\"ProductionTask\",\"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ7XCJncm91cElkXCI6XCIxOTQzNjE3MzEwOTk2Njk3MDg4XCIsXCJpZFwiOlwiZjUyYjFmOTMyOWNiNGNkNjhkMmZjNjYwZDljMmFhYzVcIixcInVzZXJOYW1lXCI6XCJhZG1pblwiLFwidXNlckNvZGVcIjpcImFkbWluXCJ9IiwianRpIjoiYTM4MGFkMjYtY2FjNi00Y2RlLTk0MGEtOWQwNWFiYjgwYjllIn0.HA4ks_-uI7BMNI9giUr-txFqoEgmD85BnWTRv7ElkjYKixZ33mkI9W2uh3RTsoUzU2nZXjsJfVfe0I38CzXI-g\"]","platformType":"","requestIp":"************","tableName":"","times":6,"userName":"admin"}
2025-08-25 09:36:42,214 INFO basic [http-nio-8882-exec-9] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"production_task_status\"]","platformType":"","requestIp":"************","tableName":"","times":6,"userName":"admin"}
2025-08-25 09:36:42,871 INFO basic [main] o.s.s.c.ThreadPoolTaskScheduler [ExecutorConfigurationSupport.java : 181] Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-08-25 09:36:43,204 INFO basic [main] o.c.b.s.b.s.w.f.LazyInitRegistration [LazyInitRegistration.java : 66] lazy initialized org.camunda.bpm.spring.boot.starter.webapp.filter.LazySecurityFilter@3b399f5a
2025-08-25 09:36:43,206 INFO basic [main] o.c.b.s.b.s.w.f.LazyInitRegistration [LazyInitRegistration.java : 66] lazy initialized org.camunda.bpm.spring.boot.starter.webapp.filter.LazyProcessEnginesFilter@4e13af1b
2025-08-25 09:36:43,839 INFO basic [main] org.quartz.impl.StdSchedulerFactory [StdSchedulerFactory.java : 1220] Using default implementation for ThreadExecutor
2025-08-25 09:36:43,873 INFO basic [main] o.quartz.core.SchedulerSignalerImpl [SchedulerSignalerImpl.java : 61] Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-25 09:36:43,874 INFO basic [main] org.quartz.core.QuartzScheduler [QuartzScheduler.java : 229] Quartz Scheduler v.2.3.2 created.
2025-08-25 09:36:43,875 INFO basic [main] org.quartz.simpl.RAMJobStore [RAMJobStore.java : 155] RAMJobStore initialized.
2025-08-25 09:36:43,878 INFO basic [main] org.quartz.core.QuartzScheduler [QuartzScheduler.java : 294] Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-25 09:36:43,879 INFO basic [main] org.quartz.impl.StdSchedulerFactory [StdSchedulerFactory.java : 1374] Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-25 09:36:43,879 INFO basic [main] org.quartz.impl.StdSchedulerFactory [StdSchedulerFactory.java : 1378] Quartz scheduler version: 2.3.2
2025-08-25 09:36:43,879 INFO basic [main] org.quartz.core.QuartzScheduler [QuartzScheduler.java : 2293] JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@43632eb0
2025-08-25 09:36:45,133 INFO basic [main] o.a.coyote.http11.Http11NioProtocol [DirectJDKLog.java : 173] Starting ProtocolHandler ["http-nio-8886"]
2025-08-25 09:36:45,204 INFO basic [main] o.s.b.w.e.tomcat.TomcatWebServer [TomcatWebServer.java : 220] Tomcat started on port(s): 8886 (http) with context path '/business'
2025-08-25 09:36:46,161 INFO basic [main] com.alibaba.nacos.client.naming [BeatReactor.java : 81] [BEAT] adding beat: BeatInfo{port=8886, ip='************', weight=1.0, serviceName='jx-mes@@jx-mes-business-application', cluster='DEFAULT', metadata={preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-08-25 09:36:46,164 INFO basic [main] com.alibaba.nacos.client.naming [NamingProxy.java : 230] [REGISTER-SERVICE] public registering service jx-mes@@jx-mes-business-application with instance: Instance{instanceId='null', ip='************', port=8886, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={preserved.register.source=SPRING_CLOUD}}
2025-08-25 09:36:46,175 INFO basic [main] c.a.c.n.r.NacosServiceRegistry [NacosServiceRegistry.java : 75] nacos registry, jx-mes jx-mes-business-application ************:8886 register finished
2025-08-25 09:36:46,177 INFO basic [main] s.d.s.w.p.DocumentationPluginsBootstrapper [DocumentationPluginsBootstrapper.java : 93] Documentation plugins bootstrapped
2025-08-25 09:36:46,188 INFO basic [main] s.d.s.w.p.DocumentationPluginsBootstrapper [AbstractDocumentationPluginsBootstrapper.java : 79] Found 2 custom documentation plugin(s)
2025-08-25 09:36:46,592 INFO basic [main] s.d.s.w.s.ApiListingReferenceScanner [ApiListingReferenceScanner.java : 44] Scanning for api listing references
2025-08-25 09:36:47,165 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_1
2025-08-25 09:36:47,191 INFO basic [com.alibaba.nacos.naming.push.receiver] com.alibaba.nacos.client.naming [PushReceiver.java : 97] received push data: {"type":"dom","data":"{\"hosts\":[{\"ip\":\"************\",\"port\":8886,\"valid\":true,\"healthy\":true,\"marked\":false,\"instanceId\":\"************#8886#DEFAULT#jx-mes@@jx-mes-business-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"enabled\":true,\"weight\":1.0,\"clusterName\":\"DEFAULT\",\"serviceName\":\"jx-mes@@jx-mes-business-application\",\"ephemeral\":true}],\"dom\":\"jx-mes@@jx-mes-business-application\",\"name\":\"jx-mes@@jx-mes-business-application\",\"cacheMillis\":10000,\"lastRefTime\":1756085807190,\"checksum\":\"a051f5e4f8fd33d2583908ca91d88999\",\"useSpecifiedURL\":false,\"clusters\":\"DEFAULT\",\"env\":\"\",\"metadata\":{}}","lastRefTime":227335046420500} from /************
2025-08-25 09:36:47,197 INFO basic [com.alibaba.nacos.naming.push.receiver] com.alibaba.nacos.client.naming [HostReactor.java : 232] new ips(1) service: jx-mes@@jx-mes-business-application@@DEFAULT -> [{"instanceId":"************#8886#DEFAULT#jx-mes@@jx-mes-business-application","ip":"************","port":8886,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"jx-mes@@jx-mes-business-application","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-08-25 09:36:47,202 INFO basic [com.alibaba.nacos.naming.push.receiver] com.alibaba.nacos.client.naming [HostReactor.java : 271] current ips:(1) service: jx-mes@@jx-mes-business-application@@DEFAULT -> [{"instanceId":"************#8886#DEFAULT#jx-mes@@jx-mes-business-application","ip":"************","port":8886,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"jx-mes@@jx-mes-business-application","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-08-25 09:36:47,207 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_1
2025-08-25 09:36:47,211 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_1
2025-08-25 09:36:47,307 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_1
2025-08-25 09:36:47,479 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_1
2025-08-25 09:36:47,483 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_1
2025-08-25 09:36:47,490 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_2
2025-08-25 09:36:47,510 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_2
2025-08-25 09:36:47,513 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_2
2025-08-25 09:36:47,528 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getLinkageDataUsingGET_1
2025-08-25 09:36:47,532 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_2
2025-08-25 09:36:47,537 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_1
2025-08-25 09:36:47,572 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_2
2025-08-25 09:36:47,574 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_2
2025-08-25 09:36:47,576 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_1
2025-08-25 09:36:47,596 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_3
2025-08-25 09:36:47,601 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_3
2025-08-25 09:36:47,605 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_3
2025-08-25 09:36:47,627 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getLinkageDataUsingGET_2
2025-08-25 09:36:47,630 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_3
2025-08-25 09:36:47,633 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_2
2025-08-25 09:36:47,709 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_3
2025-08-25 09:36:47,713 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_3
2025-08-25 09:36:47,716 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_2
2025-08-25 09:36:47,773 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_4
2025-08-25 09:36:47,777 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_4
2025-08-25 09:36:47,788 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_4
2025-08-25 09:36:47,800 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_4
2025-08-25 09:36:47,807 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_3
2025-08-25 09:36:47,852 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_4
2025-08-25 09:36:47,856 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_4
2025-08-25 09:36:47,858 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_3
2025-08-25 09:36:47,879 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_5
2025-08-25 09:36:47,894 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_5
2025-08-25 09:36:47,899 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_5
2025-08-25 09:36:47,907 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_5
2025-08-25 09:36:47,912 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_4
2025-08-25 09:36:47,948 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_5
2025-08-25 09:36:47,955 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_5
2025-08-25 09:36:47,958 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_4
2025-08-25 09:36:47,989 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_6
2025-08-25 09:36:47,993 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_6
2025-08-25 09:36:47,997 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_6
2025-08-25 09:36:48,007 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_6
2025-08-25 09:36:48,013 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_5
2025-08-25 09:36:48,043 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_6
2025-08-25 09:36:48,073 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_6
2025-08-25 09:36:48,075 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_5
2025-08-25 09:36:48,085 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_7
2025-08-25 09:36:48,089 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_7
2025-08-25 09:36:48,093 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getLinkageDataUsingGET_3
2025-08-25 09:36:48,097 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_7
2025-08-25 09:36:48,260 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: viewUsingGET_1
2025-08-25 09:36:48,264 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_7
2025-08-25 09:36:48,311 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_8
2025-08-25 09:36:48,315 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_7
2025-08-25 09:36:48,320 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_8
2025-08-25 09:36:48,328 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_8
2025-08-25 09:36:48,332 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_6
2025-08-25 09:36:48,363 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_8
2025-08-25 09:36:48,369 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_7
2025-08-25 09:36:48,372 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_6
2025-08-25 09:36:48,380 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_9
2025-08-25 09:36:48,384 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_8
2025-08-25 09:36:48,388 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_9
2025-08-25 09:36:48,393 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_9
2025-08-25 09:36:48,446 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_9
2025-08-25 09:36:48,450 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_8
2025-08-25 09:36:48,463 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_10
2025-08-25 09:36:48,467 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_9
2025-08-25 09:36:48,472 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_10
2025-08-25 09:36:48,480 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_10
2025-08-25 09:36:48,490 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_7
2025-08-25 09:36:48,512 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_10
2025-08-25 09:36:48,515 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_9
2025-08-25 09:36:48,522 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_7
2025-08-25 09:36:48,561 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_11
2025-08-25 09:36:48,570 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_10
2025-08-25 09:36:48,575 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_11
2025-08-25 09:36:48,582 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_11
2025-08-25 09:36:48,640 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_8
2025-08-25 09:36:48,682 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_11
2025-08-25 09:36:48,689 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_10
2025-08-25 09:36:48,692 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_8
2025-08-25 09:36:48,710 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_12
2025-08-25 09:36:48,715 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_11
2025-08-25 09:36:48,722 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_12
2025-08-25 09:36:48,727 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_12
2025-08-25 09:36:48,731 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_9
2025-08-25 09:36:48,755 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_12
2025-08-25 09:36:48,760 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_11
2025-08-25 09:36:48,763 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_9
2025-08-25 09:36:48,809 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_13
2025-08-25 09:36:48,816 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_12
2025-08-25 09:36:48,821 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_13
2025-08-25 09:36:48,830 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_13
2025-08-25 09:36:48,833 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_10
2025-08-25 09:36:48,839 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: submitMasterDetailsUsingPOST_1
2025-08-25 09:36:48,859 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_13
2025-08-25 09:36:48,863 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_12
2025-08-25 09:36:48,865 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_10
2025-08-25 09:36:48,910 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_14
2025-08-25 09:36:48,913 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_13
2025-08-25 09:36:48,916 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_14
2025-08-25 09:36:48,921 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: generateIdUsingPOST_1
2025-08-25 09:36:48,928 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getLinkageDataUsingGET_4
2025-08-25 09:36:48,932 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_14
2025-08-25 09:36:48,939 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_11
2025-08-25 09:36:48,945 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: submitMasterDetailsUsingPOST_2
2025-08-25 09:36:48,978 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_14
2025-08-25 09:36:48,982 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_13
2025-08-25 09:36:48,987 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_11
2025-08-25 09:36:49,023 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: findAllUsingPOST_1
2025-08-25 09:36:49,031 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_15
2025-08-25 09:36:49,036 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_14
2025-08-25 09:36:49,039 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_15
2025-08-25 09:36:49,044 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getLinkageDataUsingGET_5
2025-08-25 09:36:49,046 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_15
2025-08-25 09:36:49,049 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_12
2025-08-25 09:36:49,065 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_15
2025-08-25 09:36:49,070 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_14
2025-08-25 09:36:49,072 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_12
2025-08-25 09:36:49,090 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_16
2025-08-25 09:36:49,093 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_15
2025-08-25 09:36:49,097 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_16
2025-08-25 09:36:49,104 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_16
2025-08-25 09:36:49,108 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_13
2025-08-25 09:36:49,130 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_16
2025-08-25 09:36:49,136 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_15
2025-08-25 09:36:49,138 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_13
2025-08-25 09:36:49,147 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_17
2025-08-25 09:36:49,149 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_16
2025-08-25 09:36:49,156 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_17
2025-08-25 09:36:49,166 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_17
2025-08-25 09:36:49,172 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_14
2025-08-25 09:36:49,183 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_17
2025-08-25 09:36:49,189 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_16
2025-08-25 09:36:49,191 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_14
2025-08-25 09:36:49,210 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_18
2025-08-25 09:36:49,221 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_17
2025-08-25 09:36:49,225 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_18
2025-08-25 09:36:49,231 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_18
2025-08-25 09:36:49,237 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_15
2025-08-25 09:36:49,254 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_18
2025-08-25 09:36:49,257 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_17
2025-08-25 09:36:49,259 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_15
2025-08-25 09:36:49,269 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_19
2025-08-25 09:36:49,272 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_18
2025-08-25 09:36:49,275 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_19
2025-08-25 09:36:49,277 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_19
2025-08-25 09:36:49,288 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_16
2025-08-25 09:36:49,300 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_19
2025-08-25 09:36:49,305 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_18
2025-08-25 09:36:49,307 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_16
2025-08-25 09:36:49,318 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_20
2025-08-25 09:36:49,324 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: applyUsingPOST_1
2025-08-25 09:36:49,328 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: approveUsingPOST_1
2025-08-25 09:36:49,345 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_19
2025-08-25 09:36:49,371 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_20
2025-08-25 09:36:49,382 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_17
2025-08-25 09:36:49,424 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_20
2025-08-25 09:36:49,439 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_20
2025-08-25 09:36:49,465 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_19
2025-08-25 09:36:49,470 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_17
2025-08-25 09:36:49,504 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_21
2025-08-25 09:36:49,507 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_20
2025-08-25 09:36:49,510 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_21
2025-08-25 09:36:49,520 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getLinkageDataUsingGET_6
2025-08-25 09:36:49,524 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_21
2025-08-25 09:36:49,530 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_18
2025-08-25 09:36:49,535 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: submitMasterDetailsUsingPOST_3
2025-08-25 09:36:49,552 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_21
2025-08-25 09:36:49,557 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_20
2025-08-25 09:36:49,561 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_18
2025-08-25 09:36:49,579 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_22
2025-08-25 09:36:49,582 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_21
2025-08-25 09:36:49,589 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_22
2025-08-25 09:36:49,594 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_22
2025-08-25 09:36:49,606 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_19
2025-08-25 09:36:49,624 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_22
2025-08-25 09:36:49,628 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_21
2025-08-25 09:36:49,632 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_19
2025-08-25 09:36:49,659 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_23
2025-08-25 09:36:49,664 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: cascadeUsingGET_1
2025-08-25 09:36:49,674 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_22
2025-08-25 09:36:49,677 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_23
2025-08-25 09:36:49,682 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_23
2025-08-25 09:36:49,692 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_20
2025-08-25 09:36:49,707 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_23
2025-08-25 09:36:49,712 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_22
2025-08-25 09:36:49,715 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_20
2025-08-25 09:36:49,734 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_24
2025-08-25 09:36:49,737 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_23
2025-08-25 09:36:49,741 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_24
2025-08-25 09:36:49,744 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_24
2025-08-25 09:36:49,750 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_21
2025-08-25 09:36:49,768 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_24
2025-08-25 09:36:49,772 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_23
2025-08-25 09:36:49,775 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_21
2025-08-25 09:36:49,787 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_25
2025-08-25 09:36:49,792 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_24
2025-08-25 09:36:49,795 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_25
2025-08-25 09:36:49,804 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_25
2025-08-25 09:36:49,812 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_22
2025-08-25 09:36:49,845 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_25
2025-08-25 09:36:49,850 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_24
2025-08-25 09:36:49,855 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_22
2025-08-25 09:36:49,878 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_26
2025-08-25 09:36:49,884 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_25
2025-08-25 09:36:49,890 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_26
2025-08-25 09:36:49,893 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_26
2025-08-25 09:36:49,901 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_23
2025-08-25 09:36:49,914 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_26
2025-08-25 09:36:49,919 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_25
2025-08-25 09:36:49,921 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_23
2025-08-25 09:36:49,941 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_27
2025-08-25 09:36:49,945 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_26
2025-08-25 09:36:49,952 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_27
2025-08-25 09:36:49,956 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_27
2025-08-25 09:36:49,964 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_24
2025-08-25 09:36:49,991 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_27
2025-08-25 09:36:50,027 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_26
2025-08-25 09:36:50,030 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_24
2025-08-25 09:36:50,048 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_28
2025-08-25 09:36:50,065 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_27
2025-08-25 09:36:50,076 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_28
2025-08-25 09:36:50,081 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_28
2025-08-25 09:36:50,094 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_25
2025-08-25 09:36:50,128 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_28
2025-08-25 09:36:50,137 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_27
2025-08-25 09:36:50,139 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_25
2025-08-25 09:36:50,155 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_29
2025-08-25 09:36:50,159 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_28
2025-08-25 09:36:50,162 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_29
2025-08-25 09:36:50,169 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_29
2025-08-25 09:36:50,176 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_26
2025-08-25 09:36:50,191 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_29
2025-08-25 09:36:50,196 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_28
2025-08-25 09:36:50,198 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_26
2025-08-25 09:36:50,214 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_30
2025-08-25 09:36:50,224 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: cancelPdaWarningUsingPOST_1
2025-08-25 09:36:50,231 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: cancelProductionOrderUsingPOST_1
2025-08-25 09:36:50,242 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: connotScreenedUsingPOST_1
2025-08-25 09:36:50,247 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: copyProductionOrderUsingPOST_1
2025-08-25 09:36:50,271 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_30
2025-08-25 09:36:50,278 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getAwaitingProductionOrderListByIdsUsingPOST_1
2025-08-25 09:36:50,282 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getLinkageDataUsingGET_7
2025-08-25 09:36:50,293 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_30
2025-08-25 09:36:50,304 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: issueProductionOrderTaskUsingPOST_1
2025-08-25 09:36:50,311 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: outSourcingApplyUsingPOST_1
2025-08-25 09:36:50,322 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: pauseProductionOrderUsingPOST_1
2025-08-25 09:36:50,327 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: productionOrderInstoreUsingPOST_1
2025-08-25 09:36:50,331 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: restoreProductionOrderUsingPOST_1
2025-08-25 09:36:50,338 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: setResponsiblePersonUsingPOST_1
2025-08-25 09:36:50,342 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: splitProductionOrderUsingPOST_1
2025-08-25 09:36:50,346 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: viewUsingGET_2
2025-08-25 09:36:50,352 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_30
2025-08-25 09:36:50,372 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: abnormalfeedbackUsingPOST_1
2025-08-25 09:36:50,377 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_31
2025-08-25 09:36:50,401 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_29
2025-08-25 09:36:50,406 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_31
2025-08-25 09:36:50,415 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getLinkageDataUsingGET_8
2025-08-25 09:36:50,425 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_31
2025-08-25 09:36:50,447 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_27
2025-08-25 09:36:50,644 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_31
2025-08-25 09:36:50,729 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_29
2025-08-25 09:36:50,736 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_27
2025-08-25 09:36:50,743 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_32
2025-08-25 09:36:50,769 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_33
2025-08-25 09:36:50,774 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_30
2025-08-25 09:36:50,786 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_32
2025-08-25 09:36:50,795 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_28
2025-08-25 09:36:50,831 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_32
2025-08-25 09:36:50,836 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_30
2025-08-25 09:36:50,838 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_28
2025-08-25 09:36:50,873 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_34
2025-08-25 09:36:50,877 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: applyUsingPOST_2
2025-08-25 09:36:50,889 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: approveUsingPOST_2
2025-08-25 09:36:50,896 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_31
2025-08-25 09:36:50,905 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_32
2025-08-25 09:36:50,911 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getLinkageDataUsingGET_9
2025-08-25 09:36:50,922 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_33
2025-08-25 09:36:50,932 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_29
2025-08-25 09:36:50,963 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_33
2025-08-25 09:36:50,971 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_31
2025-08-25 09:36:50,975 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_29
2025-08-25 09:36:50,997 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_35
2025-08-25 09:36:51,004 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_32
2025-08-25 09:36:51,008 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_33
2025-08-25 09:36:51,023 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_34
2025-08-25 09:36:51,029 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_30
2025-08-25 09:36:51,051 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: submitMasterDetailsUsingPOST_4
2025-08-25 09:36:51,070 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_34
2025-08-25 09:36:51,074 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_32
2025-08-25 09:36:51,078 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_30
2025-08-25 09:36:51,090 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_36
2025-08-25 09:36:51,094 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_33
2025-08-25 09:36:51,098 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_34
2025-08-25 09:36:51,104 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getLinkageDataUsingGET_10
2025-08-25 09:36:51,107 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_35
2025-08-25 09:36:51,114 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_31
2025-08-25 09:36:51,138 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_35
2025-08-25 09:36:51,143 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_33
2025-08-25 09:36:51,145 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_31
2025-08-25 09:36:51,171 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_37
2025-08-25 09:36:51,181 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_34
2025-08-25 09:36:51,188 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_35
2025-08-25 09:36:51,194 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getLinkageDataUsingGET_11
2025-08-25 09:36:51,197 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_36
2025-08-25 09:36:51,207 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_32
2025-08-25 09:36:51,211 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: submitMasterDetailsUsingPOST_5
2025-08-25 09:36:51,226 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_36
2025-08-25 09:36:51,230 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_34
2025-08-25 09:36:51,232 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_32
2025-08-25 09:36:51,246 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_38
2025-08-25 09:36:51,249 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_35
2025-08-25 09:36:51,254 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_36
2025-08-25 09:36:51,256 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_37
2025-08-25 09:36:51,261 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_33
2025-08-25 09:36:51,271 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_37
2025-08-25 09:36:51,276 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_35
2025-08-25 09:36:51,279 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_33
2025-08-25 09:36:51,293 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_39
2025-08-25 09:36:51,297 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_36
2025-08-25 09:36:51,304 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_37
2025-08-25 09:36:51,308 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_38
2025-08-25 09:36:51,319 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_34
2025-08-25 09:36:51,332 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_38
2025-08-25 09:36:51,339 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_36
2025-08-25 09:36:51,342 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_34
2025-08-25 09:36:51,358 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_40
2025-08-25 09:36:51,364 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_37
2025-08-25 09:36:51,372 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_38
2025-08-25 09:36:51,377 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_39
2025-08-25 09:36:51,389 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_35
2025-08-25 09:36:51,403 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_39
2025-08-25 09:36:51,406 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_37
2025-08-25 09:36:51,408 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_35
2025-08-25 09:36:51,414 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_41
2025-08-25 09:36:51,418 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_38
2025-08-25 09:36:51,421 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_39
2025-08-25 09:36:51,424 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_40
2025-08-25 09:36:51,430 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_36
2025-08-25 09:36:51,442 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_40
2025-08-25 09:36:51,447 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_38
2025-08-25 09:36:51,451 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_36
2025-08-25 09:36:51,459 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_42
2025-08-25 09:36:51,463 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_39
2025-08-25 09:36:51,468 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_40
2025-08-25 09:36:51,471 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_41
2025-08-25 09:36:51,480 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_37
2025-08-25 09:36:51,493 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_41
2025-08-25 09:36:51,497 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_39
2025-08-25 09:36:51,502 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_37
2025-08-25 09:36:51,553 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_42
2025-08-25 09:36:51,558 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_40
2025-08-25 09:36:51,576 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: addUsingPOST_43
2025-08-25 09:36:51,581 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: deleteUsingPOST_40
2025-08-25 09:36:51,588 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: detailUsingGET_41
2025-08-25 09:36:51,593 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: getOptionsListUsingGET_42
2025-08-25 09:36:51,608 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importDataUsingPOST_38
2025-08-25 09:36:51,622 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: queryUsingPOST_43
2025-08-25 09:36:51,625 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: exportUsingPOST_41
2025-08-25 09:36:51,627 INFO basic [main] s.d.s.w.r.o.CachingOperationNameGenerator [CachingOperationNameGenerator.java : 41] Generating unique operation named: importTemplateUsingPOST_38
2025-08-25 09:36:51,704 INFO basic [main] s.d.s.w.s.ApiListingReferenceScanner [ApiListingReferenceScanner.java : 44] Scanning for api listing references
2025-08-25 09:36:51,707 INFO basic [main] o.s.s.quartz.SchedulerFactoryBean [SchedulerFactoryBean.java : 727] Starting Quartz Scheduler now
2025-08-25 09:36:51,708 INFO basic [main] org.quartz.core.QuartzScheduler [QuartzScheduler.java : 547] Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-25 09:37:02,271 INFO basic [http-nio-8886-exec-3] o.a.c.c.C.[.[localhost].[/business] [DirectJDKLog.java : 173] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-25 09:37:02,272 INFO basic [http-nio-8886-exec-3] o.s.web.servlet.DispatcherServlet [FrameworkServlet.java : 525] Initializing Servlet 'dispatcherServlet'
2025-08-25 09:37:02,346 INFO basic [http-nio-8886-exec-3] o.s.web.servlet.DispatcherServlet [FrameworkServlet.java : 547] Completed initialization in 74 ms
2025-08-25 09:37:03,737 INFO basic [http-nio-8886-exec-3] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"生产任务列表","logType":"INFO","method":"com.huatek.frame.modules.business.rest.ProductionTaskController.query()","params":"[{\"attachment\":\"\",\"batchNumber\":\"\",\"belongingTeam2\":\"\",\"codexTorchCreatorId\":\"\",\"codexTorchDeleted\":\"\",\"codexTorchGroupId\":\"\",\"codexTorchUpdater\":\"\",\"comment\":\"\",\"department\":\"\",\"entrustedUnit\":\"\",\"failureMode\":\"\",\"grouping\":\"\",\"humidity\":\"\",\"id\":\"\",\"judgmentCriteria\":\"\",\"limit\":10,\"manufacturer\":\"\",\"nonConformityNumber\":\"\",\"operationCard\":\"\",\"page\":1,\"params\":{},\"pdaWarning\":\"\",\"processName2\":\"\",\"productCategory\":\"\",\"productInformation1\":\"\",\"productModel\":\"\",\"productName\":\"\",\"reportWorkRemarks\":\"\",\"reporter4\":\"\",\"status\":\"\",\"taskNumber\":\"\",\"technicalCompetencyNumber\":\"\",\"temperature\":\"\",\"testBasis\":\"\",\"testConditions\":\"\",\"testMethodology\":\"\",\"testResultSummary\":\"\",\"testType\":\"\",\"ticketLevel\":\"\",\"toDoOrAll\":\"0\",\"workOrderNumber\":\"\"}]","platformType":"","requestIp":"************","tableName":"","times":329,"userName":"admin"}
2025-08-25 09:37:06,456 INFO basic [Nacos-Watch-Task-Scheduler-1] com.alibaba.nacos.client.naming [HostReactor.java : 232] new ips(1) service: jx-mes@@jx-mes-business-application -> [{"instanceId":"************#8886#DEFAULT#jx-mes@@jx-mes-business-application","ip":"************","port":8886,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"jx-mes@@jx-mes-business-application","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-08-25 09:37:06,456 INFO basic [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] c.a.c.d.a.DubboServiceDiscoveryAutoConfiguration [DubboServiceDiscoveryAutoConfiguration.java : 171] The event of the service instances[name : jx-mes-business-application , size : 1] change is about to be dispatched
2025-08-25 09:37:06,458 INFO basic [Nacos-Watch-Task-Scheduler-1] com.alibaba.nacos.client.naming [HostReactor.java : 271] current ips:(1) service: jx-mes@@jx-mes-business-application -> [{"instanceId":"************#8886#DEFAULT#jx-mes@@jx-mes-business-application","ip":"************","port":8886,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"jx-mes@@jx-mes-business-application","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-08-25 09:37:28,893 INFO basic [http-nio-8882-exec-5] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"capability_verification_status\"]","platformType":"","requestIp":"************","tableName":"","times":2,"userName":"admin"}
2025-08-25 09:37:28,898 INFO basic [http-nio-8882-exec-7] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"capability_verification_confirmationOfCapability\"]","platformType":"","requestIp":"************","tableName":"","times":7,"userName":"admin"}
2025-08-25 09:37:28,921 INFO basic [http-nio-8882-exec-2] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"表单页筛选器配置详情","logType":"INFO","method":"com.huatek.frame.modules.system.rest.FormFilterConfigController.detail()","params":"[\"CapabilityVerification\",\"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ7XCJncm91cElkXCI6XCIxOTQzNjE3MzEwOTk2Njk3MDg4XCIsXCJpZFwiOlwiZjUyYjFmOTMyOWNiNGNkNjhkMmZjNjYwZDljMmFhYzVcIixcInVzZXJOYW1lXCI6XCJhZG1pblwiLFwidXNlckNvZGVcIjpcImFkbWluXCJ9IiwianRpIjoiYTM4MGFkMjYtY2FjNi00Y2RlLTk0MGEtOWQwNWFiYjgwYjllIn0.HA4ks_-uI7BMNI9giUr-txFqoEgmD85BnWTRv7ElkjYKixZ33mkI9W2uh3RTsoUzU2nZXjsJfVfe0I38CzXI-g\"]","platformType":"","requestIp":"************","tableName":"","times":11,"userName":"admin"}
2025-08-25 09:37:28,930 INFO basic [http-nio-8882-exec-3] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"capability_verification_verificationResult\"]","platformType":"","requestIp":"************","tableName":"","times":20,"userName":"admin"}
2025-08-25 09:37:29,131 INFO basic [http-nio-8886-exec-4] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"能力核验列表","logType":"INFO","method":"com.huatek.frame.modules.business.rest.CapabilityVerificationController.query()","params":"[{\"capabilityFeedback\":\"\",\"capabilityVerificationNumber\":\"\",\"codexTorchCreatorId\":\"\",\"codexTorchDeleted\":\"\",\"codexTorchGroupId\":\"\",\"codexTorchUpdater\":\"\",\"comment\":\"\",\"confirmationOfCapability\":\"\",\"entrustedUnit\":\"\",\"feedbacker\":\"\",\"id\":\"\",\"limit\":10,\"manufacturer\":\"\",\"page\":1,\"params\":{},\"productModel\":\"\",\"productName\":\"\",\"status\":\"\",\"verificationResult\":\"\"}]","platformType":"","requestIp":"************","tableName":"","times":144,"userName":"admin"}
2025-08-25 09:37:29,202 INFO basic [http-nio-8882-exec-1] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"capability_verification_confirmationOfCapability\"]","platformType":"","requestIp":"************","tableName":"","times":8,"userName":"admin"}
2025-08-25 09:37:29,475 INFO basic [http-nio-8882-exec-4] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"capability_verification_verificationResult\"]","platformType":"","requestIp":"************","tableName":"","times":6,"userName":"admin"}
2025-08-25 09:37:29,636 INFO basic [http-nio-8882-exec-8] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"capability_verification_status\"]","platformType":"","requestIp":"************","tableName":"","times":5,"userName":"admin"}
2025-08-25 09:37:29,706 INFO basic [http-nio-8882-exec-10] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"capability_review_feedbackProcessing\"]","platformType":"","requestIp":"************","tableName":"","times":5,"userName":"admin"}
2025-08-25 09:37:33,801 INFO basic [main] com.huatek.frame.BusinessApplication [StartupInfoLogger.java : 61] Started BusinessApplication in 125.998 seconds (JVM running for 128.783)
2025-08-25 09:37:34,639 INFO basic [main] c.a.c.d.s.DubboMetadataServiceExporter [DubboMetadataServiceExporter.java : 85] The Dubbo service[<dubbo:service unexported="false" exported="true" />] has been exported.
2025-08-25 09:37:34,653 INFO basic [main] com.alibaba.nacos.client.naming [BeatReactor.java : 81] [BEAT] adding beat: BeatInfo{port=8886, ip='************', weight=1.0, serviceName='jx-mes@@jx-mes-business-application', cluster='DEFAULT', metadata={dubbo.metadata-service.urls=[ "dubbo://************:20881/com.alibaba.cloud.dubbo.service.DubboMetadataService?anyhost=true&application=jx-mes-business-application&bind.ip=************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&group=jx-mes-business-application&interface=com.alibaba.cloud.dubbo.service.DubboMetadataService&methods=getAllServiceKeys,getServiceRestMetadata,getExportedURLs,getAllExportedURLs&pid=11684&qos.enable=false&release=2.7.8&revision=2.2.6.RELEASE&side=provider&timestamp=1756085853809&version=1.0.0" ], dubbo.metadata.revision=41607C456C7C7824724BE94229E770C5, dubbo.protocols.dubbo.port=20881, preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-08-25 09:37:34,653 INFO basic [main] com.alibaba.nacos.client.naming [NamingProxy.java : 230] [REGISTER-SERVICE] public registering service jx-mes@@jx-mes-business-application with instance: Instance{instanceId='null', ip='************', port=8886, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={dubbo.metadata-service.urls=[ "dubbo://************:20881/com.alibaba.cloud.dubbo.service.DubboMetadataService?anyhost=true&application=jx-mes-business-application&bind.ip=************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&group=jx-mes-business-application&interface=com.alibaba.cloud.dubbo.service.DubboMetadataService&methods=getAllServiceKeys,getServiceRestMetadata,getExportedURLs,getAllExportedURLs&pid=11684&qos.enable=false&release=2.7.8&revision=2.2.6.RELEASE&side=provider&timestamp=1756085853809&version=1.0.0" ], dubbo.metadata.revision=41607C456C7C7824724BE94229E770C5, dubbo.protocols.dubbo.port=20881, preserved.register.source=SPRING_CLOUD}}
2025-08-25 09:37:34,659 INFO basic [main] c.a.c.n.r.NacosServiceRegistry [NacosServiceRegistry.java : 75] nacos registry, jx-mes jx-mes-business-application ************:8886 register finished
2025-08-25 09:37:34,665 INFO basic [main] org.camunda.bpm.engine.jobexecutor [BaseLogger.java : 132] ENGINE-14014 Starting up the JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor].
2025-08-25 09:37:34,673 INFO basic [JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor]] org.camunda.bpm.engine.jobexecutor [BaseLogger.java : 132] ENGINE-14018 JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor] starting to acquire jobs
2025-08-25 09:37:34,683 INFO basic [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 169] [fixed-127.0.0.1_8848] [subscribe] jx-mes-business-application.yml+jx-mes
2025-08-25 09:37:34,689 INFO basic [main] c.a.n.client.config.impl.CacheData [CacheData.java : 92] [fixed-127.0.0.1_8848] [add-listener] ok, tenant=, dataId=jx-mes-business-application.yml, group=jx-mes, cnt=1
2025-08-25 09:37:34,690 INFO basic [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 169] [fixed-127.0.0.1_8848] [subscribe] jx-mes-business-application+jx-mes
2025-08-25 09:37:34,690 INFO basic [main] c.a.n.client.config.impl.CacheData [CacheData.java : 92] [fixed-127.0.0.1_8848] [add-listener] ok, tenant=, dataId=jx-mes-business-application, group=jx-mes, cnt=1
2025-08-25 09:37:34,693 INFO basic [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 169] [fixed-127.0.0.1_8848] [subscribe] jx-mes-business-application-dev.yml+jx-mes
2025-08-25 09:37:34,694 INFO basic [main] c.a.n.client.config.impl.CacheData [CacheData.java : 92] [fixed-127.0.0.1_8848] [add-listener] ok, tenant=, dataId=jx-mes-business-application-dev.yml, group=jx-mes, cnt=1
2025-08-25 09:37:35,674 INFO basic [com.alibaba.nacos.naming.push.receiver] com.alibaba.nacos.client.naming [PushReceiver.java : 97] received push data: {"type":"dom","data":"{\"hosts\":[{\"ip\":\"************\",\"port\":8886,\"valid\":true,\"healthy\":true,\"marked\":false,\"instanceId\":\"************#8886#DEFAULT#jx-mes@@jx-mes-business-application\",\"metadata\":{\"dubbo.metadata-service.urls\":\"[ \\\"dubbo://************:20881/com.alibaba.cloud.dubbo.service.DubboMetadataService?anyhost=true&application=jx-mes-business-application&bind.ip=************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&group=jx-mes-business-application&interface=com.alibaba.cloud.dubbo.service.DubboMetadataService&methods=getAllServiceKeys,getServiceRestMetadata,getExportedURLs,getAllExportedURLs&pid=11684&qos.enable=false&release=2.7.8&revision=2.2.6.RELEASE&side=provider&timestamp=1756085853809&version=1.0.0\\\" ]\",\"dubbo.metadata.revision\":\"41607C456C7C7824724BE94229E770C5\",\"dubbo.protocols.dubbo.port\":\"20881\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"enabled\":true,\"weight\":1.0,\"clusterName\":\"DEFAULT\",\"serviceName\":\"jx-mes@@jx-mes-business-application\",\"ephemeral\":true}],\"dom\":\"jx-mes@@jx-mes-business-application\",\"name\":\"jx-mes@@jx-mes-business-application\",\"cacheMillis\":10000,\"lastRefTime\":1756085855672,\"checksum\":\"a051f5e4f8fd33d2583908ca91d88999\",\"useSpecifiedURL\":false,\"clusters\":\"\",\"env\":\"\",\"metadata\":{}}","lastRefTime":227383529604200} from /************
2025-08-25 09:37:35,675 INFO basic [com.alibaba.nacos.naming.push.receiver] com.alibaba.nacos.client.naming [HostReactor.java : 245] modified ips(1) service: jx-mes@@jx-mes-business-application -> [{"instanceId":"************#8886#DEFAULT#jx-mes@@jx-mes-business-application","ip":"************","port":8886,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"jx-mes@@jx-mes-business-application","metadata":{"dubbo.metadata-service.urls":"[ \"dubbo://************:20881/com.alibaba.cloud.dubbo.service.DubboMetadataService?anyhost=true&application=jx-mes-business-application&bind.ip=************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&group=jx-mes-business-application&interface=com.alibaba.cloud.dubbo.service.DubboMetadataService&methods=getAllServiceKeys,getServiceRestMetadata,getExportedURLs,getAllExportedURLs&pid=11684&qos.enable=false&release=2.7.8&revision=2.2.6.RELEASE&side=provider&timestamp=1756085853809&version=1.0.0\" ]","dubbo.metadata.revision":"41607C456C7C7824724BE94229E770C5","dubbo.protocols.dubbo.port":"20881","preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-08-25 09:37:35,676 INFO basic [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] c.a.c.d.a.DubboServiceDiscoveryAutoConfiguration [DubboServiceDiscoveryAutoConfiguration.java : 171] The event of the service instances[name : jx-mes-business-application , size : 1] change is about to be dispatched
2025-08-25 09:37:35,677 INFO basic [com.alibaba.nacos.naming.push.receiver] com.alibaba.nacos.client.naming [PushReceiver.java : 97] received push data: {"type":"dom","data":"{\"hosts\":[{\"ip\":\"************\",\"port\":8886,\"valid\":true,\"healthy\":true,\"marked\":false,\"instanceId\":\"************#8886#DEFAULT#jx-mes@@jx-mes-business-application\",\"metadata\":{\"dubbo.metadata-service.urls\":\"[ \\\"dubbo://************:20881/com.alibaba.cloud.dubbo.service.DubboMetadataService?anyhost=true&application=jx-mes-business-application&bind.ip=************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&group=jx-mes-business-application&interface=com.alibaba.cloud.dubbo.service.DubboMetadataService&methods=getAllServiceKeys,getServiceRestMetadata,getExportedURLs,getAllExportedURLs&pid=11684&qos.enable=false&release=2.7.8&revision=2.2.6.RELEASE&side=provider&timestamp=1756085853809&version=1.0.0\\\" ]\",\"dubbo.metadata.revision\":\"41607C456C7C7824724BE94229E770C5\",\"dubbo.protocols.dubbo.port\":\"20881\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"enabled\":true,\"weight\":1.0,\"clusterName\":\"DEFAULT\",\"serviceName\":\"jx-mes@@jx-mes-business-application\",\"ephemeral\":true}],\"dom\":\"jx-mes@@jx-mes-business-application\",\"name\":\"jx-mes@@jx-mes-business-application\",\"cacheMillis\":10000,\"lastRefTime\":1756085855673,\"checksum\":\"a051f5e4f8fd33d2583908ca91d88999\",\"useSpecifiedURL\":false,\"clusters\":\"DEFAULT\",\"env\":\"\",\"metadata\":{}}","lastRefTime":227383529604200} from /************
2025-08-25 09:37:35,678 INFO basic [com.alibaba.nacos.naming.push.receiver] com.alibaba.nacos.client.naming [HostReactor.java : 271] current ips:(1) service: jx-mes@@jx-mes-business-application -> [{"instanceId":"************#8886#DEFAULT#jx-mes@@jx-mes-business-application","ip":"************","port":8886,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"jx-mes@@jx-mes-business-application","metadata":{"dubbo.metadata-service.urls":"[ \"dubbo://************:20881/com.alibaba.cloud.dubbo.service.DubboMetadataService?anyhost=true&application=jx-mes-business-application&bind.ip=************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&group=jx-mes-business-application&interface=com.alibaba.cloud.dubbo.service.DubboMetadataService&methods=getAllServiceKeys,getServiceRestMetadata,getExportedURLs,getAllExportedURLs&pid=11684&qos.enable=false&release=2.7.8&revision=2.2.6.RELEASE&side=provider&timestamp=1756085853809&version=1.0.0\" ]","dubbo.metadata.revision":"41607C456C7C7824724BE94229E770C5","dubbo.protocols.dubbo.port":"20881","preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000}]
2025-08-25 09:37:35,678 INFO basic [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] c.a.c.d.registry.DubboCloudRegistry [DubboCloudRegistry.java : 259] APP jx-mes-business-application instance changed, size changed to 1
2025-08-25 09:37:35,678 INFO basic [com.alibaba.nacos.naming.push.receiver] com.alibaba.nacos.client.naming [BeatReactor.java : 81] [BEAT] adding beat: BeatInfo{port=8886, ip='************', weight=1.0, serviceName='jx-mes@@jx-mes-business-application', cluster='DEFAULT', metadata={dubbo.metadata-service.urls=[ "dubbo://************:20881/com.alibaba.cloud.dubbo.service.DubboMetadataService?anyhost=true&application=jx-mes-business-application&bind.ip=************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&group=jx-mes-business-application&interface=com.alibaba.cloud.dubbo.service.DubboMetadataService&methods=getAllServiceKeys,getServiceRestMetadata,getExportedURLs,getAllExportedURLs&pid=11684&qos.enable=false&release=2.7.8&revision=2.2.6.RELEASE&side=provider&timestamp=1756085853809&version=1.0.0" ], dubbo.metadata.revision=41607C456C7C7824724BE94229E770C5, dubbo.protocols.dubbo.port=20881, preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.
2025-08-25 09:37:35,681 INFO basic [com.alibaba.nacos.naming.push.receiver] com.alibaba.nacos.client.naming [HostReactor.java : 245] modified ips(1) service: jx-mes@@jx-mes-business-application@@DEFAULT -> [{"instanceId":"************#8886#DEFAULT#jx-mes@@jx-mes-business-application","ip":"************","port":8886,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"jx-mes@@jx-mes-business-application","metadata":{"dubbo.metadata-service.urls":"[ \"dubbo://************:20881/com.alibaba.cloud.dubbo.service.DubboMetadataService?anyhost=true&application=jx-mes-business-application&bind.ip=************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&group=jx-mes-business-application&interface=com.alibaba.cloud.dubbo.service.DubboMetadataService&methods=getAllServiceKeys,getServiceRestMetadata,getExportedURLs,getAllExportedURLs&pid=11684&qos.enable=false&release=2.7.8&revision=2.2.6.RELEASE&side=provider&timestamp=1756085853809&version=1.0.0\" ]","dubbo.metadata.revision":"41607C456C7C7824724BE94229E770C5","dubbo.protocols.dubbo.port":"20881","preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-08-25 09:37:35,684 INFO basic [nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent] c.a.c.d.s.DubboMetadataServiceProxy [DubboMetadataServiceProxy.java : 187] The metadata of Dubbo service[name : jx-mes-business-application] is about to be initialized
2025-08-25 09:37:35,684 INFO basic [com.alibaba.nacos.naming.push.receiver] com.alibaba.nacos.client.naming [HostReactor.java : 271] current ips:(1) service: jx-mes@@jx-mes-business-application@@DEFAULT -> [{"instanceId":"************#8886#DEFAULT#jx-mes@@jx-mes-business-application","ip":"************","port":8886,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"jx-mes@@jx-mes-business-application","metadata":{"dubbo.metadata-service.urls":"[ \"dubbo://************:20881/com.alibaba.cloud.dubbo.service.DubboMetadataService?anyhost=true&application=jx-mes-business-application&bind.ip=************&bind.port=20881&deprecated=false&dubbo=2.0.2&dynamic=true&generic=false&group=jx-mes-business-application&interface=com.alibaba.cloud.dubbo.service.DubboMetadataService&methods=getAllServiceKeys,getServiceRestMetadata,getExportedURLs,getAllExportedURLs&pid=11684&qos.enable=false&release=2.7.8&revision=2.2.6.RELEASE&side=provider&timestamp=1756085853809&version=1.0.0\" ]","dubbo.metadata.revision":"41607C456C7C7824724BE94229E770C5","dubbo.protocols.dubbo.port":"20881","preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
2025-08-25 09:37:37,145 INFO basic [http-nio-8882-exec-9] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"production_value_calculation_status\"]","platformType":"","requestIp":"************","tableName":"","times":7,"userName":"admin"}
2025-08-25 09:37:37,185 INFO basic [http-nio-8882-exec-6] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"production_value_calculation_type\"]","platformType":"","requestIp":"************","tableName":"","times":5,"userName":"admin"}
2025-08-25 09:37:37,198 INFO basic [http-nio-8882-exec-3] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"表单页筛选器配置详情","logType":"INFO","method":"com.huatek.frame.modules.system.rest.FormFilterConfigController.detail()","params":"[\"ProductionValueCalculation\",\"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ7XCJncm91cElkXCI6XCIxOTQzNjE3MzEwOTk2Njk3MDg4XCIsXCJpZFwiOlwiZjUyYjFmOTMyOWNiNGNkNjhkMmZjNjYwZDljMmFhYzVcIixcInVzZXJOYW1lXCI6XCJhZG1pblwiLFwidXNlckNvZGVcIjpcImFkbWluXCJ9IiwianRpIjoiYTM4MGFkMjYtY2FjNi00Y2RlLTk0MGEtOWQwNWFiYjgwYjllIn0.HA4ks_-uI7BMNI9giUr-txFqoEgmD85BnWTRv7ElkjYKixZ33mkI9W2uh3RTsoUzU2nZXjsJfVfe0I38CzXI-g\"]","platformType":"","requestIp":"************","tableName":"","times":9,"userName":"admin"}
2025-08-25 09:37:37,209 INFO basic [http-nio-8882-exec-5] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"production_value_calculation_testType\"]","platformType":"","requestIp":"************","tableName":"","times":9,"userName":"admin"}
2025-08-25 09:37:37,331 INFO basic [http-nio-8886-exec-6] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"产值计算列表","logType":"INFO","method":"com.huatek.frame.modules.business.rest.ProductionValueCalculationController.query()","params":"[{\"batchNumber\":\"\",\"billStatementNumber\":\"\",\"chargingStandardName\":\"\",\"codexTorchCreatorId\":\"\",\"codexTorchDeleted\":\"\",\"codexTorchGroupId\":\"\",\"codexTorchUpdater\":\"\",\"contractNumber\":\"\",\"customerPriceClassification\":\"\",\"entrustedUnit\":\"\",\"id\":\"\",\"internalPriceClassification\":\"\",\"limit\":10,\"manufacturer\":\"\",\"nonQualityProcess\":\"\",\"orderInspectionNumber\":\"\",\"orderNumber\":\"\",\"page\":1,\"params\":{},\"productCategory\":\"\",\"productInformation1\":\"\",\"productInformationName\":\"\",\"productModel\":\"\",\"productName\":\"\",\"settlementUnit\":\"\",\"status\":\"\",\"testType\":\"\",\"ticketType\":\"\",\"type\":\"\",\"workOrderInspectionNumber1\":\"\",\"workOrderNumber\":\"\"}]","platformType":"","requestIp":"************","tableName":"","times":77,"userName":"admin"}
2025-08-25 09:37:39,103 INFO basic [http-nio-8882-exec-7] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"production_value_calculation_status\"]","platformType":"","requestIp":"************","tableName":"","times":3,"userName":"admin"}
2025-08-25 09:37:39,144 INFO basic [http-nio-8882-exec-2] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"production_value_calculation_type\"]","platformType":"","requestIp":"************","tableName":"","times":3,"userName":"admin"}
2025-08-25 09:37:39,177 INFO basic [http-nio-8882-exec-1] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"production_value_calculation_testType\"]","platformType":"","requestIp":"************","tableName":"","times":2,"userName":"admin"}
2025-08-25 10:43:59,263 INFO basic [http-nio-8882-exec-6] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"用户登出系统","logType":"INFO","method":"com.huatek.frame.modules.system.rest.OauthController.logout()","params":"[\"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ7XCJncm91cElkXCI6XCIxOTQzNjE3MzEwOTk2Njk3MDg4XCIsXCJpZFwiOlwiZjUyYjFmOTMyOWNiNGNkNjhkMmZjNjYwZDljMmFhYzVcIixcInVzZXJOYW1lXCI6XCJhZG1pblwiLFwidXNlckNvZGVcIjpcImFkbWluXCJ9IiwianRpIjoiYTM4MGFkMjYtY2FjNi00Y2RlLTk0MGEtOWQwNWFiYjgwYjllIn0.HA4ks_-uI7BMNI9giUr-txFqoEgmD85BnWTRv7ElkjYKixZ33mkI9W2uh3RTsoUzU2nZXjsJfVfe0I38CzXI-g\"]","platformType":"","requestIp":"************","tableName":"","times":10,"userName":"admin"}
2025-08-25 10:44:03,541 INFO basic [http-nio-8882-exec-3] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"用户登录","logType":"INFO","method":"com.huatek.frame.modules.system.rest.OauthController.userLogin()","params":"[{\"code\":\"3\",\"params\":{},\"password\":\"MGi6yALyhWE+caoGl6By9w==\",\"userName\":\"admin\",\"uuid\":\"code-************************************\"}]","platformType":"","requestIp":"************","tableName":"","times":16,"userName":""}
2025-08-25 10:44:03,698 INFO basic [http-nio-8882-exec-7] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"用户资料","logType":"INFO","method":"com.huatek.frame.modules.system.rest.UserController.profile()","params":"[\"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ7XCJncm91cElkXCI6XCIxOTQzNjE3MzEwOTk2Njk3MDg4XCIsXCJpZFwiOlwiZjUyYjFmOTMyOWNiNGNkNjhkMmZjNjYwZDljMmFhYzVcIixcInVzZXJOYW1lXCI6XCJhZG1pblwiLFwidXNlckNvZGVcIjpcImFkbWluXCJ9IiwianRpIjoiZGIzZjY5YTUtZjFlZi00NWRiLWE3N2QtYTAyNGZlNDZjYWZkIn0.An5WygQyDjNTiQUDjQ9N9GHn6gip6EsfO3RfZPkUceDbpMHSBQdCvBa7l5bihjEtRCKefxcimQtmev5lhBL3DA\"]","platformType":"","requestIp":"************","tableName":"","times":4,"userName":"admin"}
2025-08-25 10:44:03,779 INFO basic [http-nio-8882-exec-2] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"获取用户许可资源","logType":"INFO","method":"com.huatek.frame.modules.system.rest.OauthController.menus()","params":"[\"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ7XCJncm91cElkXCI6XCIxOTQzNjE3MzEwOTk2Njk3MDg4XCIsXCJpZFwiOlwiZjUyYjFmOTMyOWNiNGNkNjhkMmZjNjYwZDljMmFhYzVcIixcInVzZXJOYW1lXCI6XCJhZG1pblwiLFwidXNlckNvZGVcIjpcImFkbWluXCJ9IiwianRpIjoiZGIzZjY5YTUtZjFlZi00NWRiLWE3N2QtYTAyNGZlNDZjYWZkIn0.An5WygQyDjNTiQUDjQ9N9GHn6gip6EsfO3RfZPkUceDbpMHSBQdCvBa7l5bihjEtRCKefxcimQtmev5lhBL3DA\"]","platformType":"","requestIp":"************","tableName":"","times":4,"userName":"admin"}
2025-08-25 10:44:15,472 INFO basic [http-nio-8882-exec-9] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"production_value_calculation_status\"]","platformType":"","requestIp":"************","tableName":"","times":3,"userName":"admin"}
2025-08-25 10:44:15,474 INFO basic [http-nio-8882-exec-8] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"表单页筛选器配置详情","logType":"INFO","method":"com.huatek.frame.modules.system.rest.FormFilterConfigController.detail()","params":"[\"ProductionValueCalculation\",\"eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ7XCJncm91cElkXCI6XCIxOTQzNjE3MzEwOTk2Njk3MDg4XCIsXCJpZFwiOlwiZjUyYjFmOTMyOWNiNGNkNjhkMmZjNjYwZDljMmFhYzVcIixcInVzZXJOYW1lXCI6XCJhZG1pblwiLFwidXNlckNvZGVcIjpcImFkbWluXCJ9IiwianRpIjoiZGIzZjY5YTUtZjFlZi00NWRiLWE3N2QtYTAyNGZlNDZjYWZkIn0.An5WygQyDjNTiQUDjQ9N9GHn6gip6EsfO3RfZPkUceDbpMHSBQdCvBa7l5bihjEtRCKefxcimQtmev5lhBL3DA\"]","platformType":"","requestIp":"************","tableName":"","times":5,"userName":"admin"}
2025-08-25 10:44:15,503 INFO basic [http-nio-8882-exec-10] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"production_value_calculation_testType\"]","platformType":"","requestIp":"************","tableName":"","times":18,"userName":"admin"}
2025-08-25 10:44:15,510 INFO basic [http-nio-8882-exec-4] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"production_value_calculation_type\"]","platformType":"","requestIp":"************","tableName":"","times":25,"userName":"admin"}
2025-08-25 10:44:15,525 INFO basic [http-nio-8886-exec-8] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"产值计算列表","logType":"INFO","method":"com.huatek.frame.modules.business.rest.ProductionValueCalculationController.query()","params":"[{\"batchNumber\":\"\",\"billStatementNumber\":\"\",\"chargingStandardName\":\"\",\"codexTorchCreatorId\":\"\",\"codexTorchDeleted\":\"\",\"codexTorchGroupId\":\"\",\"codexTorchUpdater\":\"\",\"contractNumber\":\"\",\"customerPriceClassification\":\"\",\"entrustedUnit\":\"\",\"id\":\"\",\"internalPriceClassification\":\"\",\"limit\":10,\"manufacturer\":\"\",\"nonQualityProcess\":\"\",\"orderInspectionNumber\":\"\",\"orderNumber\":\"\",\"page\":1,\"params\":{},\"productCategory\":\"\",\"productInformation1\":\"\",\"productInformationName\":\"\",\"productModel\":\"\",\"productName\":\"\",\"settlementUnit\":\"\",\"status\":\"\",\"testType\":\"\",\"ticketType\":\"\",\"type\":\"\",\"workOrderInspectionNumber1\":\"\",\"workOrderNumber\":\"\"}]","platformType":"","requestIp":"************","tableName":"","times":15,"userName":"admin"}
2025-08-25 10:44:15,627 INFO basic [http-nio-8882-exec-6] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"production_value_calculation_status\"]","platformType":"","requestIp":"************","tableName":"","times":10,"userName":"admin"}
2025-08-25 10:44:15,739 INFO basic [http-nio-8882-exec-5] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"production_value_calculation_type\"]","platformType":"","requestIp":"************","tableName":"","times":7,"userName":"admin"}
2025-08-25 10:44:15,844 INFO basic [http-nio-8882-exec-3] c.huatek.frame.log.aspect.LogAspect [LogAspect.java : 84] {"accessType":"0","browser":"Chrome 13","description":"根据字典Code获取字典明细","logType":"INFO","method":"com.huatek.frame.modules.system.rest.DicDetailController.getDicDetailByDicCode()","params":"[\"production_value_calculation_testType\"]","platformType":"","requestIp":"************","tableName":"","times":4,"userName":"admin"}
