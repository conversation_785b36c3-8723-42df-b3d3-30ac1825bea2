<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.WorkstationMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.workstation_number as workstationNumber,
		t.workstation_name as workstationName,
		t.scanner_gun_number as scannerGunNumber,
		t.scanner_gun_sn_code as scannerGunSnCode,
<!--		t.department as department,-->
		t.status as status,
<!--        t.group_code as groupCode,-->
<!--        t.group_id as groupId,-->
		t.`comment` as `comment`,
		t.CODEX_TORCH_CREATOR_ID as codexTorchCreatorId,
		t.CODEX_TORCH_UPDATER as codexTorchUpdater,
		t.CODEX_TORCH_GROUP_ID as codexTorchGroupId,
		t.CODEX_TORCH_CREATE_DATETIME as codexTorchCreateDatetime,
		t.CODEX_TORCH_UPDATE_DATETIME as codexTorchUpdateDatetime,
		t.CODEX_TORCH_DELETED as codexTorchDeleted
	</sql>
	<select id="selectWorkstationPage" parameterType="com.huatek.frame.modules.business.service.dto.WorkstationDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.WorkstationVO">
		select
		<include refid="Base_Column_List" />, g.group_name as department
			from workstation t
        LEFT JOIN sys_group g ON t.group_id = g.id
            <where>
                and 1=1
                <if test="workstationNumber != null and workstationNumber != ''">
                    and t.workstation_number  like concat('%', #{workstationNumber} ,'%')
                </if>
                <if test="workstationName != null and workstationName != ''">
                    and t.workstation_name  like concat('%', #{workstationName} ,'%')
                </if>
                <if test="scannerGunNumber != null and scannerGunNumber != ''">
                    and t.scanner_gun_number  like concat('%', #{scannerGunNumber} ,'%')
                </if>
                <if test="scannerGunSnCode != null and scannerGunSnCode != ''">
                    and t.scanner_gun_sn_code  like concat('%', #{scannerGunSnCode} ,'%')
                </if>
<!--                <if test="department != null and department != ''">-->
<!--                    and t.department  = #{department}-->
<!--                </if>-->
                <if test="status != null and status != ''">
                    and t.status  = #{status}
                </if>

                <if test="comment != null and comment != ''">
                    and t.comment  = #{comment}
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.CODEX_TORCH_CREATOR_ID  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.CODEX_TORCH_UPDATER  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.CODEX_TORCH_GROUP_ID  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.CODEX_TORCH_CREATE_DATETIME  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.CODEX_TORCH_UPDATE_DATETIME  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.CODEX_TORCH_DELETED  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>
     <select id="selectOptionsByDepartment" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.group_name label,
        	t.id value
        from sys_group t
        WHERE t.id != ''
     </select>
     <select id="selectOptionsByDeviceType" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.device_type_name label,
        	t.device_type_name value
        from device_type t
        WHERE t.device_type_name != ''
     </select>

    <select id="selectWorkstationList" parameterType="com.huatek.frame.modules.business.service.dto.WorkstationDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.WorkstationVO">
		select
		<include refid="Base_Column_List" />, g.group_name as department
        from workstation t
        LEFT JOIN sys_group g ON t.group_id = g.id
            <where>
                and 1=1
                <if test="workstationNumber != null and workstationNumber != ''">
                    and t.workstation_number  like concat('%', #{workstationNumber} ,'%')
                </if>
                <if test="workstationName != null and workstationName != ''">
                    and t.workstation_name  like concat('%', #{workstationName} ,'%')
                </if>
                <if test="scannerGunNumber != null and scannerGunNumber != ''">
                    and t.scanner_gun_number  like concat('%', #{scannerGunNumber} ,'%')
                </if>
                <if test="scannerGunSnCode != null and scannerGunSnCode != ''">
                    and t.scanner_gun_sn_code  like concat('%', #{scannerGunSnCode} ,'%')
                </if>
                <if test="department != null and department != ''">
                    and t.department  = #{department}
                </if>
                <if test="status != null and status != ''">
                    and t.status  = #{status}
                </if>

                <if test="comment != null and comment != ''">
                    and t.comment  = #{comment}
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.CODEX_TORCH_CREATOR_ID  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.CODEX_TORCH_UPDATER  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.CODEX_TORCH_GROUP_ID  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.CODEX_TORCH_CREATE_DATETIME  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.CODEX_TORCH_UPDATE_DATETIME  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.CODEX_TORCH_DELETED  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectWorkstationListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.WorkstationVO">
		select
		<include refid="Base_Column_List" />
			from workstation t
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>
</mapper>