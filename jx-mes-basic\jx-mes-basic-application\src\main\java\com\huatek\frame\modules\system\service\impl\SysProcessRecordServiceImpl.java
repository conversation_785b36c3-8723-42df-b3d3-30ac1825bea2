package com.huatek.frame.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.bpm.constant.ProcessConstant;
import com.huatek.frame.modules.system.domain.SysProcessRecord;
import com.huatek.frame.modules.system.domain.vo.SysProcessRecordVO;
import com.huatek.frame.modules.system.domain.vo.SysSelectOptionsVO;
import com.huatek.frame.modules.system.mapper.SysProcessRecordMapper;
import com.huatek.frame.modules.system.service.SysProcessRecordService;
import com.huatek.frame.modules.system.service.dto.SysProcessRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 流程记录表 ServiceImpl
 * <AUTHOR>
 * @date 2024-07-10
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "sysProcessRecord")
//@RefreshScope
@Slf4j
public class SysProcessRecordServiceImpl implements SysProcessRecordService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

	@Autowired
	private SysProcessRecordMapper sysProcessRecordMapper;

	private Map<String, Function<String, Page<SysSelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();

	public SysProcessRecordServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
	public TorchResponse<List<SysProcessRecordVO>> findSysProcessRecordPage(SysProcessRecordDTO dto) {

		String currentUser = SecurityContextHolder.getCurrentUserName();

		// 流程人员流程查看数据权限控制
		if(currentUser.equals(ProcessConstant.SUPER_USER)){
			//管理员可查看所有记录
		}else{
			//数据权限？

			//用户可查看是申请人或审批人的记录
			dto.setApplicant(currentUser);
			dto.setApprovers(currentUser);
		}


		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<SysProcessRecordVO> sysProcessRecords = sysProcessRecordMapper.selectSysProcessRecordPage(dto);
		TorchResponse<List<SysProcessRecordVO>> response = new TorchResponse<List<SysProcessRecordVO>>();
		response.getData().setData(sysProcessRecords);
		response.setStatus(200);
		response.getData().setCount(sysProcessRecords.getTotal());
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(SysProcessRecordDTO sysProcessRecordDto) {
		String id = sysProcessRecordDto.getId();
		SysProcessRecord entity = new SysProcessRecord();
        BeanUtils.copyProperties(sysProcessRecordDto, entity);
		if (HuatekTools.isEmpty(id)) {
			sysProcessRecordMapper.insert(entity);
		} else {
			sysProcessRecordMapper.updateById(entity);
		}
		TorchResponse response = new TorchResponse();
        SysProcessRecordVO vo = new SysProcessRecordVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<SysProcessRecordVO> findSysProcessRecord(String id) {
		SysProcessRecordVO vo = new SysProcessRecordVO();
		if (!HuatekTools.isEmpty(id)) {
			SysProcessRecord entity = sysProcessRecordMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<SysProcessRecordVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public	TorchResponse<SysProcessRecordVO> findSysProcessRecord(String formId,String processInstanceId){
		SysProcessRecordVO vo = new SysProcessRecordVO();
		if (!HuatekTools.isEmpty(formId) || !HuatekTools.isEmpty(processInstanceId)) {
			QueryWrapper<SysProcessRecord> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("form_id", formId).or().eq("process_instance_id", processInstanceId);
			SysProcessRecord entity = sysProcessRecordMapper.selectOne(queryWrapper);
			if(HuatekTools.isEmpty(entity)) {
				throw new ServiceException("未找到表单对应的工作流，表单号：" + formId);
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<SysProcessRecordVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
		sysProcessRecordMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
        }

	    //默认分页
		PageHelper.startPage(1, 20);
		Page<SysSelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SysSelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SysSelectOptionsVO>> response = new TorchResponse<List<SysSelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}

	@Override
	public List<SysProcessRecord> selectListByFromId(List<String> ids) {
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.in("form_id", ids);
		List<SysProcessRecord> list =  sysProcessRecordMapper.selectList(wrapper);
		return list;
	}
}