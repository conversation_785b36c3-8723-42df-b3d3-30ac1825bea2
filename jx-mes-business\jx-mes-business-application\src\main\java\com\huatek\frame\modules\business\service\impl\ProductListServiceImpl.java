package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;

import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.domain.*;
import com.huatek.frame.modules.business.domain.vo.*;
import com.huatek.frame.modules.business.mapper.*;
import com.huatek.frame.modules.business.service.*;
import com.huatek.frame.modules.business.service.dto.*;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.constant.RedisKeyConstant;
import com.huatek.frame.modules.system.domain.Dic;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysUserVO;
import com.huatek.frame.modules.system.service.SysUserService;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.poi.ss.formula.functions.T;
import org.redisson.api.RBloomFilter;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import org.springframework.util.CollectionUtils;


/**
 * 产品列表 ServiceImpl
 *
 * <AUTHOR>
 * @date 2025-07-30
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "productList1")
//@RefreshScope
@Slf4j
public class ProductListServiceImpl extends ServiceImpl<ProductListMapper, ProductList> implements ProductListService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

    @Autowired
    private ProductListMapper productListMapper;

    @Autowired
    private StandardSpecificationMapper standardSpecificationMapper;

    @Autowired
    private AwaitingProductionOrderService awaitingProductionOrderService;

    @Autowired
    private EvaluationOrderService evaluationOrderService;

    @Autowired
    private EvaluationOrderMapper evaluationOrderMapper;

    @Autowired
    private RBloomFilter<String> productInfoCreateCachePenetrationBloomFilter;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private MessageManagementService messageManagementService;

    @Autowired
    private MessageManagementMapper messageManagementMapper;

    @Autowired
    private CapabilityReviewMapper capabilityReviewMapper;

    @Autowired
    private EvaluationOrderReviewService evaluationOrderReviewService;

    @Autowired
    private CapabilityAssetMapper capabilityAssetMapper;

    @Autowired
    private CustomerInformationManagementService customerInformationManagementService;

    @Autowired
    private ProductManagementService productManagementService;

    @Autowired
    private ProductManagementMapper productManagementMapper;

    @Autowired
    private CapabilityReviewService capabilityReviewService;

    @Autowired
    private EvaluationOrderNotifyService evaluationOrderNotifyService;

    @Autowired
    private AwaitingProductionOrderMapper awaitingProductionOrderMapper;

    @DubboReference
    private SysUserService sysUserService;


    @Autowired
    private SawOrderService sawOrderService;

    @Autowired
    private SawOrderMapper sawOrderMapper;


    @Autowired
    protected Validator validator;

    private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();

    private final ExecutorService notificationExecutor = Executors.newFixedThreadPool(5);


    public ProductListServiceImpl() {

    }

    @Override
    //@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
    public TorchResponse<List<ProductListVO>> findProductListPage(ProductListDTO dto) {
        if (!HuatekTools.isEmpty(dto.getSpecialAnalysisTestProject())) {
            String productList1 = dto.getSpecialAnalysisTestProject().replaceAll(",", "|");
            productList1 = MATCH_MULTIPLE_VALUE_REGEXP.replace("#", productList1);
            dto.setSpecialAnalysisTestProject(productList1);
        }
        if (!HuatekTools.isEmpty(dto.getGroupType())) {
            String productList1 = dto.getGroupType().replaceAll(",", "|");
            productList1 = MATCH_MULTIPLE_VALUE_REGEXP.replace("#", productList1);
            dto.setGroupType(productList1);
        }
        if (!HuatekTools.isEmpty(dto.getInspectionTestProject())) {
            String productList1 = dto.getInspectionTestProject().replaceAll(",", "|");
            productList1 = MATCH_MULTIPLE_VALUE_REGEXP.replace("#", productList1);
            dto.setInspectionTestProject(productList1);
        }
        if (!HuatekTools.isEmpty(dto.getQualityConsistencyTestItems())) {
            String productList1 = dto.getQualityConsistencyTestItems().replaceAll(",", "|");
            productList1 = MATCH_MULTIPLE_VALUE_REGEXP.replace("#", productList1);
            dto.setQualityConsistencyTestItems(productList1);
        }
        if (!HuatekTools.isEmpty(dto.getDpaTestProject())) {
            String productList1 = dto.getDpaTestProject().replaceAll(",", "|");
            productList1 = MATCH_MULTIPLE_VALUE_REGEXP.replace("#", productList1);
            dto.setDpaTestProject(productList1);
        }
        PageHelper.startPage(dto.getPage(), dto.getLimit());
        Page<ProductListVO> productList1s = productListMapper.selectProductListPage(dto);
        TorchResponse<List<ProductListVO>> response = new TorchResponse<List<ProductListVO>>();
        response.getData().setData(productList1s);
        response.setStatus(200);
        response.getData().setCount(productList1s.getTotal());
        return response;
    }


    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse saveOrUpdate(ProductListDTO productListDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(productListDto.getCodexTorchDeleted())) {
            productListDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = productListDto.getId();
        ProductList entity = new ProductList();
        BeanUtils.copyProperties(productListDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
        ProductList productList;
        //查看产品管理中是否存在产品名称、产品型号、生产厂家相同的记录，如果没有，产品管理中新增一条记录
        LambdaQueryWrapper<ProductManagement> queryWrapper = Wrappers.lambdaQuery(ProductManagement.class)
                .eq(ProductManagement::getProductName, productListDto.getProductName())
                .eq(ProductManagement::getProductModel, productListDto.getProductModel())
                .eq(ProductManagement::getManufacturer, productListDto.getManufacturer());
        ProductManagement productManagement = productManagementMapper.selectOne(queryWrapper);
        if (productManagement == null) {
            ProductManagementDTO productManagementDTO = ProductManagementDTO.builder()
                    .productName(productListDto.getProductName())
                    .productModel(productListDto.getProductModel())
                    .manufacturer(productListDto.getManufacturer())
                    .productCategory(productListDto.getProductCategory())
                    .codexTorchCreatorId(SecurityContextHolder.getCurrentUserId())
                    .codexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId())
                    .codexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()))
                    .build();
            productManagementService.saveOrUpdate(productManagementDTO);
        }
        try {
            if (HuatekTools.isEmpty(id)) {
                //检验订单类型和产品试验类型是否一致
                if (!validateOrderType(productListDto)){
                    throw new ServiceException("产品的试验类型需要和订单类型一致");
                }
                productListMapper.insert(entity);
            } else {
                //变更，并且是已下发状态下的产品，需要消息通知
                if (!StrUtil.equals(productListDto.getStatus(), DicConstant.SalesOrder.PRODUCT_LIST_STATUS_PENDING)){
                    ProductList oldProductList = productListMapper.selectById(productListDto.getId());
                    ProductList newProductList = BeanUtil.toBean(productListDto, ProductList.class);
                    if (!StrUtil.equals(JSON.toJSONString(oldProductList), JSON.toJSONString(newProductList))) {
                        EvaluationOrderNotifyDTO evaluationOrderNotifyDTO = generateEvaluationOrderNotifyDTO(productListDto.getId(),
                                productListDto.getEvaluationOrderId(),
                                evaluationOrderService.findEvaluationOrder(productListDto.getEvaluationOrderId()).getData().getData().getOrderNumber(),
                                productListDto.getSerialNumber());
                        if (StrUtil.isNotEmpty(evaluationOrderNotifyDTO.getRecipients())) {
                            evaluationOrderNotifyService.saveOrUpdate(BeanUtil.toBean(evaluationOrderNotifyDTO, EvaluationOrderNotify.class));
                        }
                    }
                }

                productListMapper.updateById(entity);
            }
        } catch (DuplicateKeyException ex) {
            //看布隆过滤器是否存在，不存在直接新增
            if (productInfoCreateCachePenetrationBloomFilter.contains(productListDto.getEvaluationOrderId() + "_" + productListDto.getSerialNumber())) {
                productInfoCreateCachePenetrationBloomFilter.add(productListDto.getEvaluationOrderId() + "_" + productListDto.getSerialNumber());
            }
            throw new ServiceException("产品序号不能重复!");
        }
        ProductListExportVO productListExportVO = new ProductListExportVO();
        BeanUtil.copyProperties(entity, productListExportVO);
        //缓存预热
        stringRedisTemplate.opsForValue().set(
                String.format(RedisKeyConstant.PRODUCT_INFO_CREATE_KEY, entity.getEvaluationOrderId(), entity.getSerialNumber()),
                JSON.toJSONString(productListExportVO));

        productInfoCreateCachePenetrationBloomFilter.add(productListDto.getEvaluationOrderId() + "_" + productListDto.getSerialNumber());
        TorchResponse response = new TorchResponse();
        ProductListVO vo = new ProductListVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    private boolean validateOrderType(ProductListDTO productListDTO){
        String testType = productListDTO.getTestType();
        EvaluationOrder evaluationOrder = evaluationOrderMapper.selectById(productListDTO.getEvaluationOrderId());
        if (evaluationOrder == null){
            throw new ServiceException("请先保存测评订单信息");
        }
        String orderType = evaluationOrder.getOrderType();
        String[] orderTypes = orderType.split(",");
        //订单类型为测筛时，仅包含一筛、二筛、环境、复验试验。
        // 订单类型为可靠性时，试验类型包含DPA、专项分析、失效分析、鉴定试验、质量一致性、其它试验。
        // 订单类型为监制时，试验类型包含监制，
        // 订单类型为验收时，试验类型包含验收
        Map<String, Set<String>> orderType2TestType = new HashMap<String, Set<String>>() {{
            put(DicConstant.SalesOrder.EVALUATION_ORDER_TYPE_SCREENING,
                    Stream.of(DicConstant.ProductionOrder.TEST_TYPE_ONE,
                                    DicConstant.ProductionOrder.TEST_TYPE_TWO,
                                    DicConstant.ProductionOrder.TEST_TYPE_ENVIRONMENT,
                                    DicConstant.ProductionOrder.TEST_TYPE_REINSPECTION)
                            .collect(Collectors.toSet()));

            put(DicConstant.SalesOrder.EVALUATION_ORDER_TYPE_RELIABILITY,
                    Stream.of(DicConstant.ProductionOrder.TEST_TYPE_DPA,
                                    DicConstant.ProductionOrder.TEST_TYPE_SPECIAL_ANALYSIS,
                                    DicConstant.ProductionOrder.TEST_TYPE_FAILURE_ANALYSIS,
                                    DicConstant.ProductionOrder.TEST_TYPE_QUALIFICATION_TEST,
                                    DicConstant.ProductionOrder.TEST_TYPE_QUALITY_CONSISTENCY,
                                    DicConstant.ProductionOrder.TEST_TYPE_OTHER_TEST)
                            .collect(Collectors.toSet()));

            put(DicConstant.SalesOrder.EVALUATION_ORDER_TYPE_SUPEVISION,
                    Collections.unmodifiableSet(new HashSet<>(Stream.of(
                            DicConstant.ProductionOrder.TEST_TYPE_EXECUTIVE_PRODUCER
                    ).collect(Collectors.toSet()))));

            put(DicConstant.SalesOrder.EVALUATION_ORDER_TYPE_ACCEPTANCE,
                    Collections.unmodifiableSet(new HashSet<>(Stream.of(
                            DicConstant.ProductionOrder.TEST_TYPE_CHECK_ACCEPT
                    ).collect(Collectors.toSet()))));
        }};
        for (String type : orderTypes){
            if (!orderType2TestType.get(orderType).contains(testType)){
                return false;
            }
        }
        return true;

    }

    @Override
    //@Cacheable(key = "#p0")
    public TorchResponse<ProductListVO> findProductList(String id) {
        ProductListVO vo = new ProductListVO();
        if (!HuatekTools.isEmpty(id)) {
            ProductListVO entity = productListMapper.findProductListById(id);
            if (HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
            }
            BeanUtils.copyProperties(entity, vo);
        }
        TorchResponse<ProductListVO> response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(vo);
        return response;
    }

    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse delete(String[] ids) {
        List<ProductList> productListList = productListMapper.selectBatchIds(Arrays.asList(ids));
        for (ProductList productList : productListList) {
            if (!StrUtil.equals(productList.getStatus(), DicConstant.SalesOrder.PRODUCT_LIST_STATUS_PENDING)) {
                throw new ServiceException("只能删除未下发的产品");
            }
            stringRedisTemplate.delete(String.format(RedisKeyConstant.PRODUCT_INFO_CREATE_KEY, productList.getEvaluationOrderId(), productList.getSerialNumber()));
            productList.setCodexTorchDeleted(Constant.DEFAULT_YES);
            productListMapper.updateById(productList);
        }
        //productListMapper.deleteBatchIds(Arrays.asList(ids));
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    public TorchResponse getOptionsList(String id) {
        if (selectOptionsFuncMap.size() == 0) {
            //初始化外键函数
            selectOptionsFuncMap.put("standardSpecificationNumber", productListMapper::selectOptionsByStandardSpecificationNumber);
        }

        //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
        PageHelper.startPage(1, 1000);
        Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

        TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
        response.getData().setData(selectOptionsVOs);
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setCount(selectOptionsVOs.getTotal());
        return response;
    }


    @Override
    @ExcelExportConversion(tableName = "product_list", convertorFields = "test_data_dictionary_testType#testType,status,taskLevel")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<ProductListVO> selectProductDetails(ProductListDTO dto) {
        return productListMapper.selectProductList1List(dto);
    }

    /**
     * 导入产品列表数据
     *
     * @param productListList   产品列表数据列表
     * @param evaluationOrderId 该产品列表数据列表所属的测评订单ID
     * @return 结果
     */
    @Override
//    @Async("opsLogExecutor")
    @ExcelImportConversion(tableName = "product_list", convertorFields = "taskLevel,testType,specialAnalysisTestProject,groupType,inspectionTestProject,qualityConsistencyTestItems,dpaTestProject,status")
    public TorchResponse importProductList(List<ProductListVO> productListList, String evaluationOrderId) {

        long startTime = System.currentTimeMillis();
        if (StringUtils.isNull(productListList) || productListList.size() == 0) {
            throw new ServiceException("导入产品列表数据不能为空！");
        }
        EvaluationOrder currentEvaluationOrder = evaluationOrderService.getById(evaluationOrderId);
        List<ProductListVO> oldProductLists = new ArrayList<>();
        if (StrUtil.isNotEmpty(evaluationOrderId)) {
            oldProductLists = productListMapper.selectProductListsByEvaluationOrderId(evaluationOrderId);
        }
        //记录导入的已有产品列表id
        Set<String> importExistIds = new HashSet<>();
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        //记录新数据，批量插入
        List<ProductList> insertList = new ArrayList<>();
        //记录已存在的数据， 更新
        List<ProductList> updateList = new ArrayList<>();
        //存储测评订单下的所有产品变化信息
        Map<String, List<String>> messages = new HashMap<>();
        //记录测评订单下的信息变更通知人
        Map<String, Set<String>> notifyTo = new HashMap<>();
        //记录要发生产品信息变更的消息
        List<EvaluationOrderNotifyDTO> evaluationOrderNotifyDTOS = new ArrayList<>();
        //将导入的productListVO进行分类
        for (ProductListVO vo : productListList) {
            ProductList productList = new ProductList();
            if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                failureNum++;
                continue;
            }
            BeanUtils.copyProperties(vo, productList);
            Boolean bloomContains = productInfoCreateCachePenetrationBloomFilter.contains(evaluationOrderId + "_" + vo.getSerialNumber());
            productList.setEvaluationOrderId(evaluationOrderId);
            if (!bloomContains) {
                //新增
                productList.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
                productList.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
                productList.setCodexTorchCreateDatetime(new Timestamp(System.currentTimeMillis()));

                insertList.add(productList);
            } else {
                productList.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
                LambdaQueryWrapper<ProductList> queryWrapper = Wrappers.lambdaQuery(ProductList.class)
                        .eq(ProductList::getEvaluationOrderId, evaluationOrderId)
                        .eq(ProductList::getSerialNumber, vo.getSerialNumber());
                ProductList entity = productListMapper.selectOne(queryWrapper);
                String productListId = entity.getId();

                String key = String.format(RedisKeyConstant.PRODUCT_INFO_CREATE_KEY, evaluationOrderId, vo.getSerialNumber());
                if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(key))) {
                    //判断是否需要更新
                    ProductListVO productListVO = JSON.parseObject(stringRedisTemplate.opsForValue().get(key), ProductListVO.class);
                    ProductListExportVO oldProductList = new ProductListExportVO();
                    ProductListExportVO newProductList = new ProductListExportVO();
                    BeanUtil.copyProperties(productListVO, oldProductList);
                    BeanUtil.copyProperties(vo, newProductList);
                    //转成字符串去比较
                    if (!StrUtil.equals(JSON.toJSONString(oldProductList), JSON.toJSONString(newProductList))) {
                        //从生产工单获取通知人
//                        String[] recipients = findRecipientsFromProductionOrder(productListId);
//                        String sb = "订单编号:" + currentEvaluationOrder.getOrderNumber() +  " 序号:" + productListVO.getSerialNumber() + " 的产品信息发生了变更，请及时查看";
//                        EvaluationOrderNotifyDTO evaluationOrderNotifyDTO = EvaluationOrderNotifyDTO.builder()
//                                .message(sb)
//                                .productListId(productListId)
//                                .evaluationOrderId(evaluationOrderId)
//                                .createTime(new Timestamp(System.currentTimeMillis()))
//                                .creator(SecurityContextHolder.getCurrentUserId())
//                                .recipients(recipients) //todo
//                                .build();
                        EvaluationOrderNotifyDTO evaluationOrderNotifyDTO = generateEvaluationOrderNotifyDTO(productListId,
                                evaluationOrderId,
                                currentEvaluationOrder.getOrderNumber(),
                                productListVO.getSerialNumber());
                        evaluationOrderNotifyDTOS.add(evaluationOrderNotifyDTO);
                        productList.setId(productListId);
                        updateList.add(productList);
                    }
                    importExistIds.add(productList.getId());
                } else {
                    //布隆过滤器误判，不存在的productList,新增
                    productList.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
                    productList.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
                    productList.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
                    productList.setEvaluationOrderId(evaluationOrderId);
                    insertList.add(productList);
                }

            }
        }
        //全量导,处理导入的excel中不包含的数据项，删去
        List<String> deleteIds = new ArrayList<>();
        oldProductLists.forEach(item -> {
            if (!importExistIds.contains(item.getId())) {
                deleteIds.add(item.getId());
            }
        });
        if (CollUtil.isNotEmpty(deleteIds)) {
            productListMapper.deleteBatchIds(deleteIds);
        }

        //批量插入insertList的数据
        if (CollUtil.isNotEmpty(insertList)) {
            try {
                saveBatch(insertList, 500);
            } catch (Exception e) {
                failureNum += insertList.size();
                String msg = "<br/>" + failureNum + " 批量导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
            successNum += insertList.size();
            List<String> cacheKeys = insertList.stream()
                    .map(productList -> String.format(RedisKeyConstant.PRODUCT_INFO_CREATE_KEY, productList.getEvaluationOrderId(), productList.getSerialNumber()))
                    .collect(Collectors.toList());
            List<String> cacheValues = insertList.stream()
                    .map(productList -> {
                        ProductListExportVO productListExportVO = new ProductListExportVO();
                        BeanUtil.copyProperties(productList, productListExportVO);

                        String json = JSON.toJSONString(productListExportVO);
                        return JSON.toJSONString(productListExportVO);
                    }).collect(Collectors.toList());
            //布隆过滤器keys
            List<String> bloomKeys = insertList.stream()
                    .map(productList -> productList.getEvaluationOrderId() + "_" + productList.getSerialNumber())
                    .collect(Collectors.toList());
            //批量进行缓存预热
            stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                        for (int i = 0; i < cacheKeys.size(); i++) {
                            byte[] key = cacheKeys.get(i).getBytes();
                            byte[] value = cacheValues.get(i).getBytes();
                            connection.set(key, value);
                        }
                        return null;
                    }
            );
            //布隆过滤器缓存
            for (String bloomKey : bloomKeys) {
                productInfoCreateCachePenetrationBloomFilter.add(bloomKey);
            }
        }

        //对需要更新的进行更新
//        updateList.forEach(item -> {
//            ProductListVO productListVO = JSON.parseObject(stringRedisTemplate.opsForValue()
//                    .get(String.format(RedisKeyConstant.PRODUCT_INFO_CREATE_KEY, evaluationOrderId, item.getSerialNumber())), ProductListVO.class);
//        });
        if (CollUtil.isNotEmpty(updateList)) {
//            try {
//                productListMapper.batchUpdateProductList(updateList);
//            }catch (Exception ex){
//                throw new ServiceException("批量更新产品失败");
//            }
//            productListMapper.batchUpdateProductList(updateList);
            this.updateBatchById(updateList);

        }
        //更新完成后，需要更新redis的值
        if (CollUtil.isNotEmpty(updateList)) {
            List<String> updateCacheKeys = updateList.stream()
                    .map(productList -> String.format(RedisKeyConstant.PRODUCT_INFO_CREATE_KEY, productList.getEvaluationOrderId(), productList.getSerialNumber()))
                    .collect(Collectors.toList());
            List<String> updateCacheValues = updateList.stream()
                    .map(productList -> {
                        ProductListExportVO productListExportVO = new ProductListExportVO();
                        BeanUtil.copyProperties(productList, productListExportVO);
                        return JSON.toJSONString(productListExportVO);
                    }).collect(Collectors.toList());
            //批量更新redis
            stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                        for (int i = 0; i < updateCacheKeys.size(); i++) {
                            byte[] key = updateCacheKeys.get(i).getBytes();
                            byte[] value = updateCacheValues.get(i).getBytes();
                            connection.set(key, value);
                        }
                        return null;
                    }
            );

        }


        //todo 通知制单人和审批人

        //进行消息管理
//        for (Map.Entry<String , List<String>> entry : messages.entrySet()){
//            String evaluationOrderNumber = entry.getKey();
//            StringBuilder message = new StringBuilder();
//            message.append("编号为").append(evaluationOrderNumber).append("的测评订单下的产品信息发生了变更, 详情如下:\n");
//            for (String productListMessage : entry.getValue()){
//                message.append(productListMessage);
//                message.append("\n");
//            }
//            MessageManagementDTO messageDTO = MessageManagementDTO.builder()
//                    .messageContent(message.toString())
//                    .senderId(SecurityContextHolder.getCurrentUserId())
//                    .sendTime(new Timestamp(System.currentTimeMillis()))
//                    .userId("收信人")
//                    .build();
//            //todo 拿到通知人，创建消息通知
//            messageManagementService.saveOrUpdate(messageDTO);
//        }

        sendNotification(evaluationOrderNotifyDTOS);
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }
        long endTime = System.currentTimeMillis();
        System.out.println("=========导入200条数据耗时:" + (endTime - startTime));
        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private void sendNotification(List<EvaluationOrderNotifyDTO> messages) {

        List<EvaluationOrderNotify> EvaluationOrderNotifys = messages.stream()
                .map(item -> BeanUtil.toBean(item, EvaluationOrderNotify.class))
                .collect(Collectors.toList());
        // 提交异步任务,保存通知消息
        notificationExecutor.submit(() -> {
            try {
                evaluationOrderNotifyService.saveBatch(EvaluationOrderNotifys, 200);
            } catch (Exception e) {
                log.error("通知消息存储失败", e);
                throw new ServiceException("通知消息存储失败");
            }
        });

    }

    private Boolean linkedDataValidityVerification(ProductListVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (HuatekTools.isEmpty(vo.getSerialNumber())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>序号不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getProductName())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>产品名称不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getProductionBatch())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>生产批次不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getManufacturer())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>生产厂家不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getDeadline())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>要求完成日期不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getQuantity())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>数量不能为空!");
        }

        if (HuatekTools.isEmpty(vo.getTaskLevel())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>任务等级不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getTestType())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>试验类型不能为空!");
        }
        if (failureRecord > 0) {
            failureNum++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectProductListListByIds(List<String> ids) {
        List<ProductListVO> productList1List = productListMapper.selectProductListListByIds(ids);

        TorchResponse<List<ProductListVO>> response = new TorchResponse<List<ProductListVO>>();
        response.getData().setData(productList1List);
        response.setStatus(200);
        response.getData().setCount((long) productList1List.size());
        return response;
    }

    @Override
    public TorchResponse batchUpdateProductList(ProductBatchUpdateReqDTO requestParam) {
        List<ProductListVO> productListVOList = productListMapper.selectProductListListByIds(requestParam.getIds());
        if (CollUtil.isEmpty(productListVOList)) {
            throw new ServiceException("请选择要修改的产品信息");
        }
        String fieldName = requestParam.getFieldName(), value = requestParam.getValue();

        //批量选择试验规范
        if (StrUtil.equals(fieldName, "standardSpecificationId")) {
            productListVOList.forEach(item -> {
                item.setStandardSpecificationId(value);
            });
        }
        //产品下发
        List<String> productionOrderIds = new ArrayList<>(); //记录需要产生待制工单的产品id
        if (StrUtil.equals(fieldName, "status") && StrUtil.equals(value, DicConstant.SalesOrder.PRODUCT_LIST_STATUS_COMPLETED)) {
            productListVOList.forEach(item -> {
                if (!StrUtil.equals(item.getStatus(), DicConstant.SalesOrder.PRODUCT_LIST_STATUS_PENDING)) {
                    throw new ServiceException("只能下发还未下发的产品");
                }
                item.setStatus(DicConstant.SalesOrder.PRODUCT_LIST_STATUS_COMPLETED);
                //更新产品列表状态为已下发
                productListMapper.updateById(BeanUtil.toBean(item, ProductList.class));
                //查看该产品列表所属测评订单是否存在还未下发的工单
                LambdaQueryWrapper<ProductList> queryWrapper = Wrappers.lambdaQuery(ProductList.class)
                        .eq(ProductList::getEvaluationOrderId, item.getEvaluationOrderId())
                        .eq(ProductList::getStatus, DicConstant.SalesOrder.PRODUCT_LIST_STATUS_PENDING);
                List<ProductList> pendingProducts = productListMapper.selectList(queryWrapper);
                //修改当前测评订单状态为生产中
                LambdaUpdateWrapper<EvaluationOrder> updateEvaluationOrderWrapper = Wrappers.lambdaUpdate(EvaluationOrder.class)
                        .eq(EvaluationOrder::getId, item.getEvaluationOrderId())
                        .set(EvaluationOrder::getProductionStage, DicConstant.SalesOrder.EVALUATION_ORDER_STATUS_IN_PRODUCTION);
                evaluationOrderService.update(updateEvaluationOrderWrapper);

//                //查看当前产品的标准规范编码
//                LambdaQueryWrapper<StandardSpecification> standardSpecificationQueryWrapper = Wrappers.lambdaQuery(StandardSpecification.class)
//                        .eq(StandardSpecification::getId, item.getStandardSpecificationId());
//                StandardSpecification standardSpecification = standardSpecificationMapper.selectOne(standardSpecificationQueryWrapper);
//
//                //获取所属订单信息，通过所属订单信息查出委托单位
//                EvaluationOrderVO evaluationOrderVO = evaluationOrderService.findEvaluationOrder(item.getEvaluationOrderId()).getData().getData();
//                CustomerInformationManagementVO customerInformationManagementVO = customerInformationManagementService.findCustomerInformationManagement(evaluationOrderVO.getCustomerId()).getData().getData();

                //如果是属于"S"类型的工单，根据产品型号和参考规范检查这类产品之前是否检查过
                if (StrUtil.equals(DicConstant.ProductionOrder.WORK_ORDER_TYPE_SCREENING, checkOrderType(item))) {
                    LambdaQueryWrapper<ProductList> queryProductListWrapper = Wrappers.lambdaQuery(ProductList.class)
                            .eq(ProductList::getProductModel, item.getProductModel())
                            .eq(ProductList::getStandardSpecificationId, item.getStandardSpecificationId())
                            .eq(ProductList::getStatus, DicConstant.SalesOrder.PRODUCT_LIST_STATUS_COMPLETED);
                    List<ProductList> productLists = baseMapper.selectList(queryProductListWrapper);
                    //如果等于null，说明之前没遇到过同类型产品，需要进行能力评审
                    //先去能力评审库中查找
                    if (productLists == null || productLists.size() == 0) {
                        LambdaQueryWrapper<CapabilityReview> capabilityReviewQueryWrapper = Wrappers.lambdaQuery(CapabilityReview.class)
                                .eq(CapabilityReview::getProductModel, item.getProductModel())
                                .eq(CapabilityReview::getStandardSpecificationNumber0, item.getStandardSpecificationNumber())
                                .in(CapabilityReview::getType, DicConstant.TechnicalManagement.CAPABILITY_REVIEW_ZIXUN, DicConstant.TechnicalManagement.CAPABILITY_REVIEW_DINGDAN);
                        List<CapabilityReview> capabilityReviews = capabilityReviewMapper.selectList(capabilityReviewQueryWrapper);
                        //需要查出两条，一条是测试类型，一条是老化类型，并且评审结果为通过，反馈处理结果为已具备能力/无需核验的
                        boolean matched = capabilityReviews.size() == 2;
                        if (capabilityReviews.size() == 2) {
                            for (CapabilityReview capabilityReview : capabilityReviews) {
                                if (!StrUtil.equals(capabilityReview.getReviewResult(), DicConstant.TechnicalManagement.CAPABILITY_REVIEW_RESULT_YES)
                                        || !StrUtil.equals(capabilityReview.getFeedbackProcessing(), DicConstant.TechnicalManagement.FEEDBACKPROCESSING_WUXUHEYAN)
                                        || !StrUtil.equals(capabilityReview.getFeedbackProcessing(), DicConstant.TechnicalManagement.FEEDBACKPROCESSING_YIJUBEINENGLI)) {
                                    matched = Boolean.FALSE;
                                    break;
                                }
                            }
                            if (matched) {
                                //匹配上，产品订单评审结果自动更新为已通过
                                EvaluationOrderReviewDTO evaluationOrderReview = EvaluationOrderReviewDTO.builder()
                                        .evaluationOrderId(item.getEvaluationOrderId())
                                        .productListId(item.getProductId())
                                        .reviewResult(DicConstant.TechnicalManagement.CAPABILITY_REVIEW_RESULT_YES)
                                        .reviewer("系统内置人员") //todo
                                        .codexTorchCreateDatetime(new Timestamp(System.currentTimeMillis()))
                                        .codexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()))
                                        .build();
                                evaluationOrderReviewService.createEvalueationOrderReview(evaluationOrderReview);
                            }
                        }
                        if (!matched) {
                            //没有匹配上，进一步去能力资产库中查找
                            LambdaQueryWrapper<CapabilityAsset> capabilityAssetQueryWrapper = Wrappers.lambdaQuery(CapabilityAsset.class)
                                    .eq(CapabilityAsset::getProductModel, item.getProductModel())
                                    .in(CapabilityAsset::getCapabilityType, DicConstant.TechnicalManagement.CAPABILITY_ASSET_CAPABILITY_TYPE_LAOHUA, DicConstant.TechnicalManagement.CAPABILITY_ASSET_CAPABILITY_TYPE_CESHI);
                            List<CapabilityAsset> capabilityAssets = capabilityAssetMapper.selectList(capabilityAssetQueryWrapper);
                            //如果存在两条记录，说明能力资产里存在老化和测试的能力,产品订单评审结果自动更新为已通过
                            if (capabilityAssets.size() == 2) {
                                EvaluationOrderReviewDTO evaluationOrderReview = EvaluationOrderReviewDTO.builder()
                                        .evaluationOrderId(item.getEvaluationOrderId())
                                        .productListId(item.getProductId())
                                        .reviewResult(DicConstant.TechnicalManagement.CAPABILITY_REVIEW_RESULT_YES)
                                        .reviewer("系统内置人员")
                                        .codexTorchCreateDatetime(new Timestamp(System.currentTimeMillis()))
                                        .codexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()))
                                        .build();
                                evaluationOrderReviewService.createEvalueationOrderReview(evaluationOrderReview);
                            } else {
                                //没有匹配到，能力评审需要新增两条记录
//
                                List<CapabilityReviewDTO> capabilityReviewDTOs = Stream.of(DicConstant.TechnicalManagement.CAPABILITY_REVIEW_INSPECTIONTYPE_CESHI, DicConstant.TechnicalManagement.CAPABILITY_REVIEW_INSPECTIONTYPE_LAOHUA)  // todo 使用字典值
                                        .map(inspectionType -> CapabilityReviewDTO.builder()
                                                .productListId(item.getProductId())
                                                .inspectionType(inspectionType)
                                                .type(DicConstant.TechnicalManagement.CAPABILITY_REVIEW_DINGDAN)
                                                .entrustedUnit(item.getEntrustedUnit())
                                                .productModel(item.getProductModel())
                                                .productName(item.getProductName())
                                                .manufacturer(item.getManufacturer())
                                                .productCategory(item.getProductCategory())
                                                .standardSpecificationNumber0(item.getStandardSpecificationNumber())
                                                .orderNumber(item.getOrderNumber())
                                                .submitter(SecurityContextHolder.getCurrentUserName())
                                                .submissionTime(new Timestamp(System.currentTimeMillis()))
                                                .codexTorchCreatorId(SecurityContextHolder.getCurrentUserId())
                                                .codexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId())
                                                .build()
                                        ).collect(Collectors.toList());
                                capabilityReviewDTOs.forEach(capabilityReviewDTO -> capabilityReviewService.saveOrUpdate(capabilityReviewDTO));
                            }
                        }

                    }
                    productionOrderIds.add(item.getId());

                } else if (StrUtil.equals(DicConstant.ProductionOrder.WORK_ORDER_TYPE_RELIABILITY, checkOrderType(item))) {

                    //生产工单为K的可靠性工单类型，直接生成一条技术能力评审记录
                    CapabilityReviewDTO capabilityReview = CapabilityReviewDTO.builder()
                            .productListId(item.getProductId())
                            .inspectionType(convertTestTypeToCapability(item.getTestType()))
                            .type(DicConstant.TechnicalManagement.CAPABILITY_REVIEW_DINGDAN)
                            .entrustedUnit(item.getEntrustedUnit())
                            .productModel(item.getProductModel())
                            .productName(item.getProductName())
                            .manufacturer(item.getManufacturer())
                            .productCategory(item.getProductCategory())
                            .standardSpecificationNumber0(item.getStandardSpecificationNumber())
                            .orderNumber(item.getOrderNumber())
                            .submitter(SecurityContextHolder.getCurrentUserName())
                            .submissionTime(new Timestamp(System.currentTimeMillis()))
                            .codexTorchCreatorId(SecurityContextHolder.getCurrentUserId())
                            .codexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId())
                            .build();
                    capabilityReviewService.saveOrUpdate(capabilityReview);
                    productionOrderIds.add(item.getId());
                } else {
                    //下发监制验收类型工单
                    LambdaQueryWrapper<SawOrder> sawOrderQueryWrapper = Wrappers.lambdaQuery(SawOrder.class)
                            .eq(SawOrder::getOrderId, item.getEvaluationOrderId());
                    SawOrder sawOrder = sawOrderMapper.selectOne(sawOrderQueryWrapper);
                    String sawOrderId = sawOrder == null ? null : sawOrder.getId();
                    if (sawOrder == null) {
                        //新增一条工单记录
                        SawOrderAddOrUpdateDTO sawOrderAddOrUpdateDTO = SawOrderAddOrUpdateDTO.builder()
                                .productListId(item.getId())
                                .orderId(item.getEvaluationOrderId())
                                .status(DicConstant.ProductionOrder.SUPERVISOR_ACCEPTANCE_STATUS_UNFINISHED)
                                .build();
                        sawOrderId = sawOrderService.saveOrUpdate(sawOrderAddOrUpdateDTO).getData().getData().getId();
                    }
                    //修改该产品数据的saw_order_id字段,关联监制验收工单
                    item.setSawOrderId(sawOrderId);
                    item.setSawStatus(DicConstant.SalesOrder.SAW_ORDER_PRODUCT_LIST_STATUS_UNFINISHED);
                    productListMapper.updateById(BeanUtil.toBean(item, ProductList.class));
                }
                //todo 当测评订单全部的产品都出库/入库后，测评订单状态修改为出库/入库
            });
            try {
                awaitingProductionOrderService.issuedProductByIds(productionOrderIds);
            } catch (Exception e) {
                throw new ServiceException("生成待制工单失败");
            }
            //todo 文件评审
        }


        //产品退回
        if (StrUtil.equals(fieldName, "status") && StrUtil.equals(value, DicConstant.SalesOrder.PRODUCT_LIST_STATUS_REJECTED)) {
            productListVOList.forEach(item -> {
                //如果是已下发的产品，直接修改状态为已退回
                if (StrUtil.equals(item.getStatus(), DicConstant.SalesOrder.PRODUCT_LIST_STATUS_COMPLETED)) {
                    item.setStatus(DicConstant.SalesOrder.PRODUCT_LIST_STATUS_REJECTED);
                    MessageManagementDTO messageManagement = MessageManagementDTO.builder()
                            .messageContent("序号为" +
                                    item.getSerialNumber() +
                                    " 产品名称为" +
                                    item.getProductName() + "的产品被退回，请手动取消对应工单和生产任务")
                            .senderId(SecurityContextHolder.getCurrentUserId())
                            .sendTime(new Timestamp(System.currentTimeMillis()))
                            .status(DicConstant.MessageManagement.MESSAGE_MANAGEMENT_STATUS_HAS_READ)
                            //todo 接收人id
                            .build();
                    messageManagementService.saveOrUpdate(messageManagement);
                }
                //如果是未下发产品，直接删除
                else if (StrUtil.equals(item.getStatus(), DicConstant.SalesOrder.PRODUCT_LIST_STATUS_PENDING)) {
                    item.setCodexTorchDeleted(DicConstant.CommonDic.DELETED);
                    productListMapper.updateById(BeanUtil.toBean(item, ProductList.class));
                    stringRedisTemplate.delete(String.format(RedisKeyConstant.PRODUCT_INFO_CREATE_KEY, item.getEvaluationOrderId(), item.getSerialNumber()));
                }


            });
        }
        try {
            List<ProductList> entities = productListVOList.stream()
                    .map(vo -> {
                        ProductList entity = new ProductList();
                        BeanUtils.copyProperties(vo, entity); // 使用Spring的工具类复制属性
                        return entity;
                    })
                    .collect(Collectors.toList());
            productListMapper.batchUpdateProductList(entities);
        } catch (Exception ex) {
            throw new ServiceException("批量更新产品列表信息失败");
        }

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;


    }

    public static String convertTestTypeToCapability(String testType) {
        switch (testType) {
            case DicConstant.ProductionOrder.TEST_TYPE_DPA: // "10"
                return DicConstant.TechnicalManagement.CAPABILITY_REVIEW_INSPECTIONTYPE_DPA; // "2"
            case DicConstant.ProductionOrder.TEST_TYPE_SPECIAL_ANALYSIS: // "9"
                return DicConstant.TechnicalManagement.CAPABILITY_REVIEW_INSPECTIONTYPE_ZXFX; // "3"
            case DicConstant.ProductionOrder.TEST_TYPE_FAILURE_ANALYSIS: // "8"
                return DicConstant.TechnicalManagement.CAPABILITY_REVIEW_INSPECTIONTYPE_SXFX; // "4"
            case DicConstant.ProductionOrder.TEST_TYPE_QUALIFICATION_TEST: // "7"
                return DicConstant.TechnicalManagement.CAPABILITY_REVIEW_INSPECTIONTYPE_JDSY; // "6"
            case DicConstant.ProductionOrder.TEST_TYPE_QUALITY_CONSISTENCY: // "5"
                return DicConstant.TechnicalManagement.CAPABILITY_REVIEW_INSPECTIONTYPE_ZLYZX; // "8"
            case DicConstant.ProductionOrder.TEST_TYPE_OTHER_TEST: // "4"
                return DicConstant.TechnicalManagement.CAPABILITY_REVIEW_INSPECTIONTYPE_QTSY; // "9"
            default:
                return null; // 未匹配到
        }
    }

    @Override
    public TorchResponse notifyProductListChangeMessage(List<EvaluationOrderNotifyDTO> requestParam) {

//        List<EvaluationOrderNotifyVO> evaluationOrderNotifyVOS = evaluationOrderNotifyService.findAll(requestParam).getData().getData();
        List<EvaluationOrderNotifyVO> evaluationOrderNotifyVOS = requestParam.stream().map(item -> {
            EvaluationOrderNotifyVO evaluationOrderNotifyVO = BeanUtil.toBean(item, EvaluationOrderNotifyVO.class);
            return evaluationOrderNotifyVO;
        }).collect(Collectors.toList());
        List<MessageManagement> messageManagements = new ArrayList<>();
        List<EvaluationOrderNotify> notifyList = new ArrayList<>();
        evaluationOrderNotifyVOS.forEach(item -> {
            String[] userIds = item.getRecipientsList();
            if (ArrayUtil.isEmpty(userIds)) {
                //不需要下发消息
                return;
            }
            for (String userId : userIds) {
                MessageManagement messageManagement = MessageManagement.builder()
                        .messageContent(item.getMessage())
                        .senderId(item.getCreator())
                        .sendTime(new Timestamp(System.currentTimeMillis()))
                        .userId(userId)
                        .status(DicConstant.MessageManagement.MESSAGE_MANAGEMENT_STATUS_UN_READ)
                        .codexTorchCreatorId(SecurityContextHolder.getCurrentUserId())
                        .codexTorchCreateDatetime(new Timestamp(System.currentTimeMillis()))
                        .codexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId())
                        .codexTorchDeleted("0")
                        .build();
                messageManagements.add(messageManagement);
            }
            item.setDeleted(DicConstant.CommonDic.DELETED);
            notifyList.add(BeanUtil.toBean(item, EvaluationOrderNotify.class));
        });
        //批量插入消息
        messageManagementService.saveBatch(messageManagements, 200);
        //批量更新
        evaluationOrderNotifyService.updateBatchById(notifyList);
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @Override
    public TorchResponse<List<ProductListVO>> selectProductListsByEvaluationOrderId(String evaluationOrderId) {
        List<ProductListVO> list = productListMapper.selectProductListsByEvaluationOrderId(evaluationOrderId);

        TorchResponse response = new TorchResponse();
        response.getData().setData(list);
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    private void senMessage(String message, String receiveUser) {
        MessageManagement messageManagement = MessageManagement.builder()
                .status(DicConstant.MessageManagement.MESSAGE_MANAGEMENT_STATUS_UN_READ)
                .sendTime(new Timestamp(System.currentTimeMillis()))
                .senderId(SecurityContextHolder.getCurrentUserId())
                .userId(receiveUser)
                .messageContent(message)
                .build();
        messageManagementMapper.insert(messageManagement);
    }

    private <T> String generateChangeMessage(T oldObj, T newObj) {
        StringBuilder sb = new StringBuilder();
        if (oldObj == null || newObj == null) {
            return "其中一个对象为null";
        }
        try {
            Class<?> clazz = oldObj.getClass();
            Field[] fields = clazz.getDeclaredFields();

            for (Field field : fields) {
                field.setAccessible(true);
                Object oldValue = field.get(oldObj);
                Object newValue = field.get(newObj);

                if (!Objects.equals(oldValue, newValue)) {
                    sb.append(field.getName())
                            .append("从'").append(oldValue)
                            .append("'变更为'").append(newValue)
                            .append("', ");
                }
            }
        } catch (IllegalAccessException e) {
            return "比较过程中发生错误: " + e.getMessage();
        }

        return sb.toString();
    }

    private Boolean objChanged(T oldObj, T newObj) {
        return !StrUtil.isEmpty(generateChangeMessage(oldObj, newObj));
    }

    private String checkOrderType(ProductListVO productListVO) {
        //K类型订单
        Set<String> kTypeOrder = new HashSet<>(Arrays.asList(
                DicConstant.ProductionOrder.TEST_TYPE_DPA,
                DicConstant.ProductionOrder.TEST_TYPE_SPECIAL_ANALYSIS,
                DicConstant.ProductionOrder.TEST_TYPE_FAILURE_ANALYSIS,
                DicConstant.ProductionOrder.TEST_TYPE_QUALIFICATION_TEST,
                DicConstant.ProductionOrder.TEST_TYPE_QUALITY_CONSISTENCY,
                DicConstant.ProductionOrder.TEST_TYPE_OTHER_TEST
        ));
        //监制验收类型订单
        Set<String> SawTypeOrder = new HashSet<>(Arrays.asList(
                DicConstant.ProductionOrder.TEST_TYPE_EXECUTIVE_PRODUCER,
                DicConstant.ProductionOrder.TEST_TYPE_CHECK_ACCEPT,
                DicConstant.ProductionOrder.TEST_TYPE_CERTIFICATION
        ));
        if (kTypeOrder.contains(productListVO.getTestType())) {
            return "S";
        } else if (SawTypeOrder.contains(productListVO.getTestType())) {
            return "SAW";
        }
        return "K";

    }

    /**
     * 从待制工单查找通知人，包括负责人、制单人和审批人
     *
     * @param productionListId
     * @return
     */
    private String[] findRecipientsFromProductionOrder(String productionListId) {
        HashSet<String> userIds = new HashSet<>();
        //通过产品id获取待制工单，可能有多个
        LambdaQueryWrapper<ProductionOrder> queryWrapper = Wrappers.lambdaQuery(ProductionOrder.class)
                .eq(ProductionOrder::getProduct, productionListId);
        List<ProductionOrder> productionOrders = awaitingProductionOrderMapper.selectList(queryWrapper);

        //将查询到的生产工单的通知人合并
        productionOrders.forEach(item -> {
            String responsiblePerson = item.getResponsiblePerson();
            String preparedBy = item.getPreparedBy();
            String[] approvers = null;
            if (item.getCodexTorchApprovers() != null) {
                approvers = item.getCodexTorchApprovers().split(",");
            }

            //将approvers存的名字转为id

            List<SysUserVO> users = (approvers != null ? sysUserService.getUsersByNames(approvers) : new ArrayList<>());
            if (StrUtil.isNotEmpty(responsiblePerson)) {
                userIds.add(responsiblePerson);
            }
            if (StrUtil.isNotEmpty(preparedBy)) {
                userIds.add(preparedBy);
            }
            if (CollUtil.isNotEmpty(users)) {
                userIds.addAll(users.stream().map(SysUserVO::getId).collect(Collectors.toList()));
            }

        });
        //返回结果
        return userIds.toArray(new String[0]);

    }

    private EvaluationOrderNotifyDTO generateEvaluationOrderNotifyDTO(String productListId, String evaluationOrderId, String orderNumber, Integer serialNumber) {
        String[] recipients = findRecipientsFromProductionOrder(productListId);
        //转成逗号拼接的字符串
        String recipientsConvert2String = String.join(",", recipients);
//        String sb = "订单编号:" + orderNumber + " 序号:" + serialNumber + " 的产品信息发生了变更，请及时查看";
        String sb = String.format("订单编号: %s, 序号:%s 的产品信息发生了变更，请及时查看", orderNumber, serialNumber);
        EvaluationOrderNotifyDTO evaluationOrderNotifyDTO = EvaluationOrderNotifyDTO.builder()
                .message(sb)
                .productListId(productListId)
                .evaluationOrderId(evaluationOrderId)
                .createTime(new Timestamp(System.currentTimeMillis()))
                .creator(SecurityContextHolder.getCurrentUserId())
                .recipients(recipientsConvert2String)
                .build();
        return evaluationOrderNotifyDTO;
    }


}
