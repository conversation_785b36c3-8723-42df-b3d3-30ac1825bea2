2025-08-25 09:33:17,853 INFO gateway [main] com.huatek.frame.GatewayApplication [SpringApplication.java : 655] The following profiles are active: dev
2025-08-25 09:33:19,378 INFO gateway [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 249] Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-25 09:33:19,385 INFO gateway [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 127] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-25 09:33:19,460 INFO gateway [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 187] Finished Spring Data repository scanning in 22ms. Found 0 Redis repository interfaces.
2025-08-25 09:33:19,879 INFO gateway [main] o.s.cloud.context.scope.GenericScope [GenericScope.java : 295] BeanFactory id=64f4209a-d1ff-3528-9ea5-7ff5c377b0aa
2025-08-25 09:33:20,097 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'redisConfig' of type [com.huatek.frame.gate.conf.RedisConfig$$EnhancerBySpringCGLIB$$bd6aecb0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:33:20,529 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:33:20,601 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:33:20,611 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:33:20,614 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactiveLoadBalancerConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactiveLoadBalancerConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:33:20,619 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'deferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-25 09:33:21,996 INFO gateway [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-25 09:33:22,012 INFO gateway [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-25 09:33:25,729 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [After]
2025-08-25 09:33:25,730 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Before]
2025-08-25 09:33:25,730 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Between]
2025-08-25 09:33:25,731 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Cookie]
2025-08-25 09:33:25,731 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Header]
2025-08-25 09:33:25,731 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Host]
2025-08-25 09:33:25,731 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Method]
2025-08-25 09:33:25,732 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Path]
2025-08-25 09:33:25,732 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Query]
2025-08-25 09:33:25,732 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [ReadBody]
2025-08-25 09:33:25,733 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [RemoteAddr]
2025-08-25 09:33:25,733 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Weight]
2025-08-25 09:33:25,733 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-08-25 09:33:26,214 INFO gateway [main] c.a.c.s.SentinelWebFluxAutoConfiguration [SentinelWebFluxAutoConfiguration.java : 91] [Sentinel Starter] register Sentinel SentinelWebFluxFilter
2025-08-25 09:33:26,303 INFO gateway [main] o.s.s.c.ThreadPoolTaskScheduler [ExecutorConfigurationSupport.java : 181] Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-08-25 09:33:29,526 INFO gateway [main] o.s.b.w.e.netty.NettyWebServer [NettyWebServer.java : 109] Netty started on port(s): 8881
2025-08-25 09:33:31,062 INFO gateway [main] c.a.c.n.r.NacosServiceRegistry [NacosServiceRegistry.java : 75] nacos registry, jx-mes jx-mes-gateway ************:8881 register finished
2025-08-25 09:33:31,126 INFO gateway [main] com.huatek.frame.GatewayApplication [StartupInfoLogger.java : 61] Started GatewayApplication in 18.459 seconds (JVM running for 19.458)
2025-08-25 09:33:31,136 INFO gateway [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 169] [fixed-127.0.0.1_8848] [subscribe] jx-mes-gateway+jx-mes
2025-08-25 09:33:31,138 INFO gateway [main] c.a.n.client.config.impl.CacheData [CacheData.java : 92] [fixed-127.0.0.1_8848] [add-listener] ok, tenant=, dataId=jx-mes-gateway, group=jx-mes, cnt=1
2025-08-25 09:33:31,139 INFO gateway [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 169] [fixed-127.0.0.1_8848] [subscribe] jx-mes-gateway-dev.yml+jx-mes
2025-08-25 09:33:31,140 INFO gateway [main] c.a.n.client.config.impl.CacheData [CacheData.java : 92] [fixed-127.0.0.1_8848] [add-listener] ok, tenant=, dataId=jx-mes-gateway-dev.yml, group=jx-mes, cnt=1
2025-08-25 09:33:31,140 INFO gateway [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 169] [fixed-127.0.0.1_8848] [subscribe] jx-mes-gateway.yml+jx-mes
2025-08-25 09:33:31,140 INFO gateway [main] c.a.n.client.config.impl.CacheData [CacheData.java : 92] [fixed-127.0.0.1_8848] [add-listener] ok, tenant=, dataId=jx-mes-gateway.yml, group=jx-mes, cnt=1
2025-08-25 09:36:03,819 INFO gateway [reactor-http-nio-4] c.n.config.ChainedDynamicProperty [ChainedDynamicProperty.java : 115] Flipping property: jx-mes-basic-application.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-08-25 09:36:03,904 INFO gateway [reactor-http-nio-4] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 197] Client: jx-mes-basic-application instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=jx-mes-basic-application,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-08-25 09:36:03,937 INFO gateway [reactor-http-nio-4] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 222] Using serverListUpdater PollingServerListUpdater
2025-08-25 09:36:04,025 INFO gateway [reactor-http-nio-4] c.n.config.ChainedDynamicProperty [ChainedDynamicProperty.java : 115] Flipping property: jx-mes-basic-application.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-08-25 09:36:04,036 INFO gateway [reactor-http-nio-4] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 150] DynamicServerListLoadBalancer for client jx-mes-basic-application initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=jx-mes-basic-application,current list of Servers=[************:8882],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:************:8882;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@7b4da784
2025-08-25 09:36:04,165 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-08-25 09:36:04,165 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-08-25 09:36:04,165 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-08-25 09:36:04,969 INFO gateway [PollingServerListUpdater-0] c.n.config.ChainedDynamicProperty [ChainedDynamicProperty.java : 115] Flipping property: jx-mes-basic-application.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-08-25 09:36:06,194 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-08-25 09:36:26,497 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-08-25 09:36:26,704 INFO gateway [reactor-http-nio-1] c.n.config.ChainedDynamicProperty [ChainedDynamicProperty.java : 115] Flipping property: jx-mes-business-application.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-08-25 09:36:26,721 INFO gateway [reactor-http-nio-1] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 197] Client: jx-mes-business-application instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=jx-mes-business-application,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-08-25 09:36:26,731 INFO gateway [reactor-http-nio-1] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 222] Using serverListUpdater PollingServerListUpdater
2025-08-25 09:36:26,756 INFO gateway [reactor-http-nio-1] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 150] DynamicServerListLoadBalancer for client jx-mes-business-application initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=jx-mes-business-application,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@31568ac6
2025-08-25 09:36:26,938 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-08-25 09:36:26,998 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-08-25 09:36:27,066 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-08-25 09:36:27,131 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-08-25 09:36:27,208 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-08-25 09:36:27,278 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-08-25 09:36:41,861 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-08-25 09:36:42,178 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-08-25 09:36:57,739 INFO gateway [PollingServerListUpdater-0] c.n.config.ChainedDynamicProperty [ChainedDynamicProperty.java : 115] Flipping property: jx-mes-business-application.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-08-25 09:37:02,020 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-08-25 09:37:28,848 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_verification_confirmationOfCapability
2025-08-25 09:37:28,849 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_verification_status
2025-08-25 09:37:28,849 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityVerification/optionsList/entrustedUnit
2025-08-25 09:37:28,849 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_verification_verificationResult
2025-08-25 09:37:28,850 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityVerification
2025-08-25 09:37:28,897 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityVerification/capabilityVerificationList
2025-08-25 09:37:29,155 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_verification_confirmationOfCapability
2025-08-25 09:37:29,288 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityVerification/optionsList/entrustedUnit
2025-08-25 09:37:29,434 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_verification_verificationResult
2025-08-25 09:37:29,606 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_verification_status
2025-08-25 09:37:29,684 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-08-25 09:37:37,116 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-08-25 09:37:37,132 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionValueCalculation/productionValueCalculationList
2025-08-25 09:37:37,156 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-08-25 09:37:37,157 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_testType
2025-08-25 09:37:37,170 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionValueCalculation
2025-08-25 09:37:39,087 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-08-25 09:37:39,131 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-08-25 09:37:39,166 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_testType
2025-08-25 10:43:59,226 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/logout
2025-08-25 10:44:00,214 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/code
2025-08-25 10:44:03,487 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/login
2025-08-25 10:44:03,681 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-08-25 10:44:03,759 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-08-25 10:44:15,432 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionValueCalculation
2025-08-25 10:44:15,433 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-08-25 10:44:15,433 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-08-25 10:44:15,447 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionValueCalculation/productionValueCalculationList
2025-08-25 10:44:15,453 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_testType
2025-08-25 10:44:15,590 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-08-25 10:44:15,710 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-08-25 10:44:15,829 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_testType
2025-08-25 14:39:34,301 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessManagement/optionsList/testingTeam
2025-08-25 14:39:34,302 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessManagement/standardProcessManagementList
2025-08-25 14:39:34,302 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessManagement/optionsList/workstation
2025-08-25 14:39:34,305 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_status
2025-08-25 14:39:34,304 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-08-25 14:39:34,304 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_isApproval
2025-08-25 14:39:34,579 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessManagement/optionsList/totalNoncompliantCount
2025-08-25 14:39:34,580 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/StandardProcessManagement
2025-08-25 14:39:34,583 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessManagement/optionsList/mpPdaCalcRls
2025-08-25 14:39:34,697 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-08-25 14:39:34,861 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessManagement/optionsList/workstation
2025-08-25 14:39:35,005 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessManagement/optionsList/testingTeam
2025-08-25 14:39:35,115 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_isApproval
2025-08-25 14:39:35,243 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/standardProcessManagement/optionsList/department
