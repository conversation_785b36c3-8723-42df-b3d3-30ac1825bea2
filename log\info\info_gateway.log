2025-09-02 09:28:29,435 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-02 09:28:29,442 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-02 09:28:29,462 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-02 09:28:29,674 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-02 09:28:32,047 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-02 09:28:32,047 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-09-02 09:28:32,052 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionValueCalculation/productionValueCalculationList
2025-09-02 09:28:32,074 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-09-02 09:28:32,074 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_testType
2025-09-02 09:28:32,078 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionValueCalculation
2025-09-02 09:28:32,282 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-09-02 09:28:32,382 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-09-02 09:28:34,286 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_testType
2025-09-02 09:28:50,841 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-02 09:28:50,847 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-02 09:28:50,933 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-02 09:28:51,003 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-02 09:28:51,088 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-02 09:28:51,235 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-02 09:28:51,281 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 09:28:51,376 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-02 09:28:51,415 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-02 09:28:51,545 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-02 09:29:01,900 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-02 09:29:01,902 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-02 09:29:01,909 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-02 09:29:01,936 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-02 09:29:01,941 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-02 09:29:01,945 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-02 09:29:02,759 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-02 09:29:02,795 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-02 09:29:03,348 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-02 09:29:03,701 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-02 09:29:04,195 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-02 09:29:04,315 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-02 09:29:04,544 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-02 09:29:04,602 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-02 09:29:05,116 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-02 09:29:17,604 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/findDepartById/f52b1f9329cb4cd68d2fc660d9c2aac5
2025-09-02 09:36:32,704 INFO gateway [Thread-38] c.n.l.PollingServerListUpdater [PollingServerListUpdater.java : 53] Shutting down the Executor Pool for PollingServerListUpdater
2025-09-02 09:36:32,770 INFO gateway [SpringContextShutdownHook] o.s.s.c.ThreadPoolTaskScheduler [ExecutorConfigurationSupport.java : 218] Shutting down ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-09-02 09:36:32,776 INFO gateway [SpringContextShutdownHook] c.a.c.n.r.NacosServiceRegistry [NacosServiceRegistry.java : 94] De-registering from Nacos Server now...
2025-09-02 09:36:32,804 INFO gateway [SpringContextShutdownHook] c.a.c.n.r.NacosServiceRegistry [NacosServiceRegistry.java : 114] De-registration finished.
2025-09-02 09:36:41,814 INFO gateway [SpringContextShutdownHook] c.a.n.c.identify.CredentialWatcher [CredentialWatcher.java : 105] [null] CredentialWatcher is stopped
2025-09-02 09:36:41,815 INFO gateway [SpringContextShutdownHook] c.a.n.c.identify.CredentialService [CredentialService.java : 98] [null] CredentialService is freed
2025-09-02 09:42:54,596 INFO gateway [main] com.huatek.frame.GatewayApplication [SpringApplication.java : 655] The following profiles are active: dev
2025-09-02 09:42:56,279 INFO gateway [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 249] Multiple Spring Data modules found, entering strict repository configuration mode!
2025-09-02 09:42:56,285 INFO gateway [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 127] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-02 09:42:56,354 INFO gateway [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 187] Finished Spring Data repository scanning in 22ms. Found 0 Redis repository interfaces.
2025-09-02 09:42:56,754 INFO gateway [main] o.s.cloud.context.scope.GenericScope [GenericScope.java : 295] BeanFactory id=64f4209a-d1ff-3528-9ea5-7ff5c377b0aa
2025-09-02 09:42:57,152 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'redisConfig' of type [com.huatek.frame.gate.conf.RedisConfig$$EnhancerBySpringCGLIB$$826b6709] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-02 09:42:57,742 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-02 09:42:57,885 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-02 09:42:57,907 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-02 09:42:57,911 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactiveLoadBalancerConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactiveLoadBalancerConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-02 09:42:57,923 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'deferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-02 09:42:59,875 INFO gateway [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-09-02 09:42:59,890 INFO gateway [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-09-02 09:43:05,289 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [After]
2025-09-02 09:43:05,290 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Before]
2025-09-02 09:43:05,290 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Between]
2025-09-02 09:43:05,291 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Cookie]
2025-09-02 09:43:05,291 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Header]
2025-09-02 09:43:05,291 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Host]
2025-09-02 09:43:05,292 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Method]
2025-09-02 09:43:05,292 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Path]
2025-09-02 09:43:05,292 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Query]
2025-09-02 09:43:05,293 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [ReadBody]
2025-09-02 09:43:05,293 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [RemoteAddr]
2025-09-02 09:43:05,293 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Weight]
2025-09-02 09:43:05,293 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-09-02 09:43:05,801 INFO gateway [main] c.a.c.s.SentinelWebFluxAutoConfiguration [SentinelWebFluxAutoConfiguration.java : 91] [Sentinel Starter] register Sentinel SentinelWebFluxFilter
2025-09-02 09:43:05,908 INFO gateway [main] o.s.s.c.ThreadPoolTaskScheduler [ExecutorConfigurationSupport.java : 181] Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-09-02 09:43:10,366 INFO gateway [main] o.s.b.w.e.netty.NettyWebServer [NettyWebServer.java : 109] Netty started on port(s): 8881
2025-09-02 09:43:12,330 INFO gateway [main] c.a.c.n.r.NacosServiceRegistry [NacosServiceRegistry.java : 75] nacos registry, jx-mes jx-mes-gateway ************:8881 register finished
2025-09-02 09:43:12,415 INFO gateway [main] com.huatek.frame.GatewayApplication [StartupInfoLogger.java : 61] Started GatewayApplication in 26.631 seconds (JVM running for 28.466)
2025-09-02 09:43:12,429 INFO gateway [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 169] [fixed-127.0.0.1_8848] [subscribe] jx-mes-gateway+jx-mes
2025-09-02 09:43:12,433 INFO gateway [main] c.a.n.client.config.impl.CacheData [CacheData.java : 92] [fixed-127.0.0.1_8848] [add-listener] ok, tenant=, dataId=jx-mes-gateway, group=jx-mes, cnt=1
2025-09-02 09:43:12,435 INFO gateway [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 169] [fixed-127.0.0.1_8848] [subscribe] jx-mes-gateway-dev.yml+jx-mes
2025-09-02 09:43:12,436 INFO gateway [main] c.a.n.client.config.impl.CacheData [CacheData.java : 92] [fixed-127.0.0.1_8848] [add-listener] ok, tenant=, dataId=jx-mes-gateway-dev.yml, group=jx-mes, cnt=1
2025-09-02 09:43:12,436 INFO gateway [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 169] [fixed-127.0.0.1_8848] [subscribe] jx-mes-gateway.yml+jx-mes
2025-09-02 09:43:12,437 INFO gateway [main] c.a.n.client.config.impl.CacheData [CacheData.java : 92] [fixed-127.0.0.1_8848] [add-listener] ok, tenant=, dataId=jx-mes-gateway.yml, group=jx-mes, cnt=1
2025-09-02 09:47:56,414 INFO gateway [reactor-http-nio-4] c.n.config.ChainedDynamicProperty [ChainedDynamicProperty.java : 115] Flipping property: jx-mes-basic-application.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-09-02 09:47:56,452 INFO gateway [reactor-http-nio-4] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 197] Client: jx-mes-basic-application instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=jx-mes-basic-application,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-09-02 09:47:56,469 INFO gateway [reactor-http-nio-4] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 222] Using serverListUpdater PollingServerListUpdater
2025-09-02 09:47:56,563 INFO gateway [reactor-http-nio-4] c.n.config.ChainedDynamicProperty [ChainedDynamicProperty.java : 115] Flipping property: jx-mes-basic-application.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-09-02 09:47:56,567 INFO gateway [reactor-http-nio-4] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 150] DynamicServerListLoadBalancer for client jx-mes-basic-application initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=jx-mes-basic-application,current list of Servers=[************:8882],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:************:8882;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@3889eb47
2025-09-02 09:47:56,703 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-02 09:47:56,703 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-02 09:47:56,703 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-02 09:47:57,476 INFO gateway [PollingServerListUpdater-0] c.n.config.ChainedDynamicProperty [ChainedDynamicProperty.java : 115] Flipping property: jx-mes-basic-application.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-09-02 09:47:58,610 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-02 09:48:03,107 INFO gateway [reactor-http-nio-1] c.n.config.ChainedDynamicProperty [ChainedDynamicProperty.java : 115] Flipping property: jx-mes-business-application.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-09-02 09:48:03,120 INFO gateway [reactor-http-nio-1] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 197] Client: jx-mes-business-application instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=jx-mes-business-application,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-09-02 09:48:03,128 INFO gateway [reactor-http-nio-1] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 222] Using serverListUpdater PollingServerListUpdater
2025-09-02 09:48:03,145 INFO gateway [reactor-http-nio-1] c.n.config.ChainedDynamicProperty [ChainedDynamicProperty.java : 115] Flipping property: jx-mes-business-application.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-09-02 09:48:03,150 INFO gateway [reactor-http-nio-1] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 150] DynamicServerListLoadBalancer for client jx-mes-business-application initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=jx-mes-business-application,current list of Servers=[************:8886],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:************:8886;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@47b445d7
2025-09-02 09:48:03,182 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-02 09:48:03,209 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-02 09:48:03,219 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-02 09:48:03,226 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-02 09:48:03,230 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-02 09:48:03,239 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-02 09:48:03,346 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-02 09:48:03,352 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-02 09:48:03,362 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-02 09:48:03,531 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-02 09:48:03,629 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-02 09:48:04,130 INFO gateway [PollingServerListUpdater-0] c.n.config.ChainedDynamicProperty [ChainedDynamicProperty.java : 115] Flipping property: jx-mes-business-application.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-09-02 09:48:04,995 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-02 09:48:05,087 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-02 09:48:05,170 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-02 09:48:05,244 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-02 09:48:05,889 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-02 10:03:39,922 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/completeProductionOrder
2025-09-02 10:03:39,933 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/awaitingProductionOrderList
2025-09-02 10:03:40,341 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/orderNumber
2025-09-02 10:03:40,697 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/entrustedUnit
2025-09-02 10:03:40,823 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/productModel
2025-09-02 10:03:40,993 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-02 10:03:41,104 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-02 10:03:41,316 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-02 10:03:41,394 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_workOrderStatus
2025-09-02 10:03:41,453 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_reportStatus
2025-09-02 10:03:41,509 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_productionStage
2025-09-02 10:03:52,352 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/createProductionOrderReport
2025-09-02 13:19:51,377 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-02 13:19:51,387 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-02 13:19:51,744 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-02 13:19:51,893 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-02 13:19:52,068 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-02 13:19:52,138 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-02 13:19:52,218 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 13:19:52,361 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-02 13:19:52,474 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-02 13:19:52,585 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-02 13:21:00,131 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-02 13:21:00,134 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-02 13:21:00,135 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-02 13:21:00,163 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-02 13:21:00,166 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-02 13:21:00,183 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-02 13:21:00,255 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-02 13:21:00,307 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-02 13:21:00,401 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/categoryName
2025-09-02 13:21:00,446 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-02 13:21:00,594 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-02 13:21:00,884 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-02 13:21:01,049 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-02 13:21:01,130 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-02 13:31:37,393 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-02 13:31:37,405 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-02 13:31:37,455 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-02 13:31:37,646 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-02 13:31:39,496 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-02 13:31:39,524 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-02 13:31:39,530 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-02 13:31:39,530 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-02 13:31:39,531 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-02 13:31:39,582 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-02 13:31:39,718 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-02 13:31:39,760 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-02 13:31:39,924 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-02 13:31:39,926 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/categoryName
2025-09-02 13:31:39,981 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-02 13:31:40,390 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-02 13:31:40,505 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-02 13:31:40,591 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-02 13:31:40,710 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-02 13:34:49,755 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-02 13:34:49,768 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-02 13:34:49,923 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-02 13:34:50,084 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-02 13:34:50,369 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-02 13:34:50,534 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-02 13:34:50,607 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 13:34:50,653 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-02 13:34:50,700 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-02 13:34:50,750 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-02 13:35:16,724 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-02 13:35:18,957 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-02 13:35:41,047 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 13:35:48,783 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 13:35:51,560 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/details/1951192992299749376
2025-09-02 13:35:53,381 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 13:36:01,685 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 13:36:07,690 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 13:36:14,444 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/details/1950437633012404234
2025-09-02 13:36:16,472 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 13:37:11,194 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 13:37:14,070 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/details/1946108489772437507
2025-09-02 13:37:15,738 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 13:37:21,976 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 13:37:23,778 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/details/1945735408331657216
2025-09-02 13:37:26,014 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 13:37:52,846 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-02 13:37:52,847 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-02 13:37:52,849 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-02 13:37:52,849 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-02 13:37:52,861 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-02 13:37:52,867 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-02 13:37:52,931 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-02 13:37:53,030 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-02 13:37:53,082 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/categoryName
2025-09-02 13:37:53,083 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-02 13:37:53,233 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-02 13:37:53,483 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-02 13:37:53,609 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-02 13:37:53,745 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-02 13:38:02,234 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-02 13:38:02,246 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-02 13:38:02,364 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-02 13:38:02,663 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-02 13:38:02,809 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-02 13:38:02,863 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-02 13:38:02,905 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 13:38:02,944 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-02 13:38:02,981 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-02 13:38:03,029 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-02 13:39:28,676 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 13:39:33,453 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 13:39:47,743 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-02 13:39:47,758 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-02 13:39:48,283 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-02 13:39:49,850 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-02 13:39:54,086 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-02 13:39:55,574 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-02 13:39:56,374 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 13:39:57,927 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-02 13:39:58,502 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-02 13:39:59,078 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-02 13:40:03,048 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-02 13:40:03,059 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-02 13:40:03,396 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-02 13:40:03,429 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-02 13:40:03,466 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-02 13:40:03,473 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-02 13:40:03,504 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-02 13:40:03,515 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-02 13:40:03,555 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-02 13:40:03,569 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-02 13:40:03,600 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 13:40:03,614 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-02 13:40:03,647 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-02 13:40:03,661 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 13:40:03,714 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-02 13:40:03,716 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-02 13:40:03,759 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-02 13:40:03,767 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-02 13:40:03,803 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-02 13:40:48,162 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/details/1945735408331657216
2025-09-02 13:40:49,087 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 13:40:57,240 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 13:45:42,319 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-02 13:45:42,325 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-02 13:45:42,413 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-02 13:45:42,522 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-02 13:45:42,593 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-02 13:45:42,753 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-02 13:45:42,958 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 13:45:43,086 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-02 13:45:43,163 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-02 13:45:43,215 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-02 13:46:03,013 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-02 13:46:03,013 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-02 13:46:03,013 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-02 13:46:03,054 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-02 13:46:03,054 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-02 13:46:03,059 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-02 13:46:03,124 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-02 13:46:03,155 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-02 13:46:03,158 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/categoryName
2025-09-02 13:46:03,160 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-02 13:46:03,245 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-02 13:46:03,310 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-02 13:46:03,397 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-02 13:46:03,455 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-02 13:46:49,710 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-02 13:46:51,312 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-02 13:46:52,707 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-02 13:47:01,041 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 13:47:06,619 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 13:47:08,856 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/details/1953002722957594624
2025-09-02 13:48:06,510 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-02 13:48:06,513 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-02 13:48:06,525 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-02 13:48:06,525 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-02 13:48:06,526 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-02 13:48:06,525 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-02 13:48:06,608 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-02 13:48:06,612 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-02 13:48:06,630 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/categoryName
2025-09-02 13:48:06,664 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-02 13:48:06,814 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-02 13:48:06,917 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-02 13:48:07,146 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-02 13:48:07,371 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-02 13:48:11,167 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-02 13:48:11,177 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-02 13:48:11,283 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-02 13:48:11,521 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-02 13:48:11,606 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-02 13:48:11,702 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-02 13:48:11,739 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 13:48:11,775 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-02 13:48:11,810 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-02 13:48:11,842 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-02 13:48:17,417 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-02 13:48:17,418 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-02 13:48:17,418 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 13:48:17,422 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-02 13:48:17,422 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityAsset
2025-09-02 13:48:17,440 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/capabilityAssetList
2025-09-02 13:48:17,607 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-02 13:48:17,739 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 13:48:17,862 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-02 13:48:17,919 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-02 13:48:17,961 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-02 13:48:23,846 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-02 13:48:23,857 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-02 13:48:23,985 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-02 13:48:24,211 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-02 13:48:24,296 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-02 13:48:24,387 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-02 13:48:24,425 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 13:48:24,477 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-02 13:48:24,511 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-02 13:48:24,556 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-02 13:48:26,815 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-02 13:48:26,816 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 13:48:26,826 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-02 13:48:26,826 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityAsset
2025-09-02 13:48:26,828 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-02 13:48:26,843 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/capabilityAssetList
2025-09-02 13:48:26,998 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-02 13:48:27,143 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 13:48:27,440 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-02 13:48:27,606 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-02 13:48:27,646 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-02 13:49:49,913 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-02 13:49:49,917 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-02 13:49:49,917 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-02 13:49:49,924 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-02 13:49:49,926 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-02 13:49:49,963 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-02 13:49:49,995 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-02 13:49:50,037 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-02 13:49:50,078 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/categoryName
2025-09-02 13:49:50,078 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-02 13:49:50,308 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-02 13:49:50,526 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-02 13:49:50,767 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-02 13:49:50,859 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-02 13:52:07,718 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-02 13:52:07,778 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-02 13:52:07,964 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-02 13:52:08,036 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-02 13:52:08,190 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-02 13:52:08,264 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-02 13:52:08,440 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 13:52:08,545 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-02 13:52:08,588 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-02 13:52:08,642 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-02 14:05:13,078 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-02 14:05:13,078 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-02 14:05:13,080 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-02 14:05:13,085 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-02 14:05:13,085 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-02 14:05:13,112 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-02 14:05:13,171 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-02 14:05:13,220 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-02 14:05:13,250 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-02 14:05:13,250 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/categoryName
2025-09-02 14:05:13,432 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-02 14:05:13,789 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-02 14:05:13,888 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-02 14:05:14,148 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-02 14:05:21,602 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 14:05:21,615 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/capabilityAssetList
2025-09-02 14:05:21,629 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-02 14:05:21,630 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-02 14:05:21,630 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-02 14:05:21,630 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityAsset
2025-09-02 14:05:21,887 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-02 14:05:22,008 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 14:05:22,146 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-02 14:05:22,303 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-02 14:05:22,395 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-02 14:05:31,229 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-02 14:05:31,241 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-02 14:05:31,369 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-02 14:05:31,617 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-02 14:05:31,677 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-02 14:05:31,805 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-02 14:05:32,022 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 14:05:32,060 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-02 14:05:32,109 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-02 14:05:32,188 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-02 14:05:36,348 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-02 14:05:36,349 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-02 14:05:36,349 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-02 14:05:36,349 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-02 14:05:36,349 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-02 14:05:36,432 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-02 14:05:36,541 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/categoryName
2025-09-02 14:05:36,541 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-02 14:05:36,554 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-02 14:05:36,608 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-02 14:05:36,829 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-02 14:05:36,923 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-02 14:05:36,997 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-02 14:05:37,207 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-02 14:06:09,144 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-02 14:06:17,630 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-02 14:06:17,636 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 14:06:17,637 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-02 14:06:17,641 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-02 14:06:17,641 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityAsset
2025-09-02 14:06:17,663 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/capabilityAssetList
2025-09-02 14:06:17,819 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-02 14:06:17,979 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 14:06:18,190 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/productModel
2025-09-02 14:06:18,236 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityAsset/optionsList/taskNumber
2025-09-02 14:06:18,313 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-02 14:06:53,534 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-02 14:06:53,563 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-02 14:06:53,669 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-02 14:06:53,914 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_capabilityType
2025-09-02 14:06:54,214 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-02 14:06:54,363 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-02 14:06:54,405 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 14:06:54,445 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-02 14:06:54,479 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-02 14:06:54,520 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-02 14:07:30,284 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-02 14:07:30,296 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-02 14:07:30,334 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-02 14:07:30,433 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-02 14:07:32,517 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-02 14:07:32,528 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-02 14:07:32,530 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-02 14:07:32,693 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-02 14:07:33,009 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-02 14:07:33,082 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-02 14:07:33,139 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-02 14:07:33,390 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 14:07:33,510 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-02 14:07:33,590 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-02 14:07:33,722 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-02 14:12:12,576 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-02 14:12:12,600 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-02 14:12:12,598 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-02 14:12:12,607 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-02 14:12:12,609 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-02 14:12:12,620 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-02 14:12:12,749 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-02 14:12:12,754 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/categoryName
2025-09-02 14:12:12,754 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-02 14:12:12,754 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-02 14:12:12,851 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-02 14:12:12,977 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-02 14:12:13,164 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-02 14:12:13,248 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-02 14:20:39,040 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 14:20:44,813 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 14:20:46,880 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/details/1953002722957594624
2025-09-02 14:30:45,354 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/details/1953002722957594624
2025-09-02 14:30:48,428 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/details/1953002722957594624
2025-09-02 14:30:53,749 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/delete/
2025-09-02 14:30:54,132 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/details/1953002722957594624
2025-09-02 14:31:41,709 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityDevelopment
2025-09-02 14:31:41,750 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/capabilityDevelopmentList
2025-09-02 14:31:42,143 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_developmentTeam
2025-09-02 14:31:42,284 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/standard_process_management_processClassification
2025-09-02 14:31:42,347 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_status
2025-09-02 14:31:42,399 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_source
2025-09-02 14:31:42,449 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_asset_applicableTestType
2025-09-02 14:31:42,688 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/equipment_inventory_deviceCategory
2025-09-02 14:31:42,881 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityDevelopment/optionsList/debuggingEquipmentNumber
2025-09-02 14:31:43,064 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_development_completed10
2025-09-02 14:31:55,378 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-02 14:31:55,379 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-02 14:31:55,397 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-02 14:31:55,397 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-02 14:31:55,397 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-02 14:31:55,397 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-02 14:31:55,446 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-02 14:31:55,446 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-09-02 14:31:55,595 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-02 14:31:55,813 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-09-02 14:31:55,919 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-09-02 14:31:55,996 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-09-02 14:31:56,057 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-02 14:31:56,118 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-02 14:31:56,822 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-02 14:32:02,201 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1957697467603410945
2025-09-02 14:32:15,271 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/startTask
2025-09-02 14:32:15,545 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-02 14:32:25,933 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/startTask
2025-09-02 14:32:26,015 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-02 14:32:30,848 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1957725413370101762
2025-09-02 14:32:35,737 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1957697467603410945
2025-09-02 14:52:30,049 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1957697467603410945
2025-09-02 14:53:00,401 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-02 14:53:02,200 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1957697467603410945
2025-09-02 14:53:02,662 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:53:02,662 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:53:02,687 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-09-02 14:53:02,687 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-02 14:53:02,687 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/prod_task_op_hist_reason
2025-09-02 14:53:02,849 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:53:02,849 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:53:02,903 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:53:02,905 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:53:02,949 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1957697467603410945
2025-09-02 14:53:12,910 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1957697467603410945
2025-09-02 14:53:14,547 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:53:14,547 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:53:14,602 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1957697467603410945
2025-09-02 14:53:14,667 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:53:14,669 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:53:14,705 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:53:14,711 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:56:03,094 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1957697467603410945
2025-09-02 14:56:03,425 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:56:03,427 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:56:03,516 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:56:03,536 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:56:03,573 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1957697467603410945
2025-09-02 14:56:03,579 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:56:03,784 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:56:07,488 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/startTask
2025-09-02 14:56:07,993 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-02 14:56:10,657 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/detail/1957725413370101762
2025-09-02 14:56:11,154 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:56:11,159 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:56:11,212 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:56:11,213 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:56:11,243 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/capability/1957725413370101762
2025-09-02 14:56:11,257 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:56:11,258 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 14:56:32,789 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-09-02 15:14:20,363 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/completeProductionOrder
2025-09-02 15:14:20,372 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/awaitingProductionOrderList
2025-09-02 15:14:20,521 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/orderNumber
2025-09-02 15:14:20,781 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/entrustedUnit
2025-09-02 15:14:21,018 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/productModel
2025-09-02 15:14:21,109 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-02 15:14:21,264 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-02 15:14:21,346 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-02 15:14:21,395 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_workOrderStatus
2025-09-02 15:14:21,467 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_reportStatus
2025-09-02 15:14:21,529 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_productionStage
2025-09-02 15:14:49,382 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionOrder/view/1958083625902596098
2025-09-02 15:14:49,384 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_productionStage
2025-09-02 15:14:49,399 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_workOrderStatus
2025-09-02 15:14:49,401 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-02 15:14:49,402 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_reportRequirements
2025-09-02 15:14:49,403 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-02 15:14:49,434 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-02 15:14:49,480 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/evaluation_order_reportFormat
2025-09-02 15:14:49,487 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/yes_or_no
2025-09-02 15:14:58,831 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/getProductionOrderTestData
2025-09-02 15:14:58,844 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 15:14:58,844 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 15:14:58,851 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/inspection_type
2025-09-02 15:14:58,896 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 15:14:58,903 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 15:14:59,136 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 15:14:59,137 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 15:46:26,462 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-02 15:46:26,472 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-02 15:46:26,575 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-02 15:46:26,873 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-02 15:46:57,328 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-02 15:46:57,351 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/awaitingProductionOrderList
2025-09-02 15:46:57,375 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/completeProductionOrder
2025-09-02 15:46:57,769 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/orderNumber
2025-09-02 15:46:58,373 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/entrustedUnit
2025-09-02 15:46:58,817 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/productModel
2025-09-02 15:46:58,884 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-02 15:46:58,937 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-02 15:46:58,989 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-02 15:46:59,053 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_workOrderStatus
2025-09-02 15:46:59,111 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_reportStatus
2025-09-02 15:46:59,166 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_productionStage
2025-09-02 16:14:55,551 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/createProductionOrderReport
2025-09-02 16:14:57,975 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/createProductionOrderReport
2025-09-02 16:14:58,985 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/createProductionOrderReport
2025-09-02 16:15:03,391 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 16:15:03,391 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 16:15:03,396 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/getProductionOrderTestData
2025-09-02 16:15:03,428 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/inspection_type
2025-09-02 16:15:03,430 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-09-02 16:15:03,658 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 16:15:03,676 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 16:15:03,934 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 16:15:03,936 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/testDataDictionary
2025-09-02 16:31:31,326 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 16:31:46,333 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 16:31:50,326 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/details/1954841736404668416
2025-09-02 17:38:43,605 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 17:38:50,247 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-09-02 18:57:57,151 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/completeProductionOrder
2025-09-02 18:57:57,166 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/awaitingProductionOrderList
2025-09-02 18:57:57,612 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/orderNumber
2025-09-02 18:57:58,967 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/entrustedUnit
2025-09-02 18:57:59,060 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/optionsList/productModel
2025-09-02 18:57:59,170 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/product_list_taskLevel
2025-09-02 18:57:59,226 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-09-02 18:57:59,292 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/experiment_project_testMethodology
2025-09-02 18:57:59,345 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_workOrderStatus
2025-09-02 18:57:59,400 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_reportStatus
2025-09-02 18:57:59,444 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/awaiting_production_order_productionStage
2025-09-02 18:58:40,475 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/exportCard
2025-09-02 18:58:40,479 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 58] token is empty...
2025-09-02 18:58:56,013 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/exportCard
2025-09-02 18:58:56,018 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 58] token is empty...
2025-09-02 19:03:51,624 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/exportCard
2025-09-02 19:06:03,616 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/menus
2025-09-02 19:06:56,217 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/detail/null
2025-09-02 19:07:12,389 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/menu
2025-09-02 19:07:13,053 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/menus
2025-09-02 19:07:24,008 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/roles
2025-09-02 19:07:32,635 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/roles
2025-09-02 19:07:35,636 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/menu/menu/f52b1f9329cb4cd68d2fc660d9c2aac6
2025-09-02 19:07:50,887 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/role/updateRolePermission/f52b1f9329cb4cd68d2fc660d9c2aac6
2025-09-02 19:07:56,868 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/logout
2025-09-02 19:07:57,782 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/code
2025-09-02 19:08:01,332 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/login
2025-09-02 19:08:01,788 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-02 19:08:01,892 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-02 19:08:03,918 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/messageManagement/countMessages
2025-09-02 19:08:36,671 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/exportCard
2025-09-02 19:09:32,512 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/exportCard
2025-09-02 19:10:41,533 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/exportCard
2025-09-02 19:20:02,030 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-09-02 19:20:02,038 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-09-02 19:20:02,084 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-09-02 19:20:02,275 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-09-02 19:21:51,338 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/listAll
2025-09-02 19:21:51,339 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_abnormalType
2025-09-02 19:21:51,394 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/settlementUnit
2025-09-02 19:21:51,404 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/abnormalfeedback/optionsList/toNotify
2025-09-02 19:21:51,404 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_status
2025-09-02 19:21:51,404 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_solutionMeasures
2025-09-02 19:21:51,435 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-02 19:21:51,472 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/abnormalfeedback_whetherToEnable
2025-09-02 19:21:51,491 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productCategory/cascade/categoryName
2025-09-02 19:21:51,588 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/CapabilityReview
2025-09-02 19:21:51,787 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_inspectionType
2025-09-02 19:21:51,842 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_type
2025-09-02 19:21:51,906 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_reviewResult
2025-09-02 19:21:51,959 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/capability_review_feedbackProcessing
2025-09-02 19:22:47,343 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-02 19:25:57,172 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-02 19:29:59,893 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-02 19:30:04,662 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/capabilityReview/capabilityReviewList
2025-09-02 19:30:51,389 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/exportCard
2025-09-02 19:32:34,736 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/exportCard
2025-09-02 19:33:24,386 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/exportCard
2025-09-02 19:34:52,774 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/completeProductionOrder/exportCard
